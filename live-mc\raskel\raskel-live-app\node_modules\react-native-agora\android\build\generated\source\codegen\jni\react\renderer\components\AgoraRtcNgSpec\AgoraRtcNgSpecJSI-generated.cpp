/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "AgoraRtcNgSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeAgoraRtcNgCxxSpecJSI_newIrisApiEngine(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAgoraRtcNgCxxSpecJSI *>(&turboModule)->newIrisApiEngine(
    rt
  );
}
static jsi::Value __hostFunction_NativeAgoraRtcNgCxxSpecJSI_destroyIrisApiEngine(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAgoraRtcNgCxxSpecJSI *>(&turboModule)->destroyIrisApiEngine(
    rt
  );
}
static jsi::Value __hostFunction_NativeAgoraRtcNgCxxSpecJSI_callApi(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAgoraRtcNgCxxSpecJSI *>(&turboModule)->callApi(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeAgoraRtcNgCxxSpecJSI_showRPSystemBroadcastPickerView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAgoraRtcNgCxxSpecJSI *>(&turboModule)->showRPSystemBroadcastPickerView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
}
static jsi::Value __hostFunction_NativeAgoraRtcNgCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAgoraRtcNgCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAgoraRtcNgCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAgoraRtcNgCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeAgoraRtcNgCxxSpecJSI::NativeAgoraRtcNgCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AgoraRtcNg", jsInvoker) {
  methodMap_["newIrisApiEngine"] = MethodMetadata {0, __hostFunction_NativeAgoraRtcNgCxxSpecJSI_newIrisApiEngine};
  methodMap_["destroyIrisApiEngine"] = MethodMetadata {0, __hostFunction_NativeAgoraRtcNgCxxSpecJSI_destroyIrisApiEngine};
  methodMap_["callApi"] = MethodMetadata {1, __hostFunction_NativeAgoraRtcNgCxxSpecJSI_callApi};
  methodMap_["showRPSystemBroadcastPickerView"] = MethodMetadata {1, __hostFunction_NativeAgoraRtcNgCxxSpecJSI_showRPSystemBroadcastPickerView};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAgoraRtcNgCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAgoraRtcNgCxxSpecJSI_removeListeners};
}


} // namespace facebook::react
