{"buildFiles": ["C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\335l6r1b\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\335l6r1b\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\335l6r1b\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\335l6r1b\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\src\\fabric\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\335l6r1b\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\335l6r1b\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"expo-modules-core::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "expo-modules-core", "output": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\335l6r1b\\obj\\arm64-v8a\\libexpo-modules-core.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "fabric::@3c04bbf757b97f4dae7c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "fabric", "output": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\335l6r1b\\arm64-v8a\\src\\fabric\\libfabric.a"}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cxx", "cpp"]}