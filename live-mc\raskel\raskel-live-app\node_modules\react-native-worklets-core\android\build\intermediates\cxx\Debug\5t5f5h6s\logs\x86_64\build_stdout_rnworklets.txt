ninja: Entering directory `C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\.cxx\Debug\5t5f5h6s\x86_64'
[0/2] Re-checking globbed directories...
[1/9] Building CXX object CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/dispatch/WKTDispatchQueue.cpp.o
[2/9] Building CXX object CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/base/WKTRuntimeLifecycleMonitor.cpp.o
[3/9] Building CXX object CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/base/WKTJsiHostObject.cpp.o
[4/9] Building CXX object CMakeFiles/rnworklets.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/react-native-worklets-core/cpp/WKTJsiWorkletApi.cpp.o
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/cpp/WKTJsiWorkletApi.cpp:1:
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/cpp/WKTJsiWorkletApi.h:14:
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/sharedvalues/WKTJsiSharedValue.h:8:
C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/WKTJsiDispatcher.h:97:22: warning: exception of type 'const std::runtime_error &' will be caught by earlier handler [-Wexceptions]
   97 |       } catch (const std::runtime_error &err) {
      |                      ^
C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/WKTJsiDispatcher.h:94:22: note: for type 'const std::exception &'
   94 |       } catch (const std::exception &err) {
      |                      ^
1 warning generated.
[5/9] Building CXX object CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/wrappers/WKTJsiPromiseWrapper.cpp.o
[6/9] Building CXX object CMakeFiles/rnworklets.dir/cpp-adapter.cpp.o
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/cpp-adapter.cpp:6:
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/WKTJsiWorkletApi.h:14:
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/sharedvalues/WKTJsiSharedValue.h:8:
C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/WKTJsiDispatcher.h:97:22: warning: exception of type 'const std::runtime_error &' will be caught by earlier handler [-Wexceptions]
   97 |       } catch (const std::runtime_error &err) {
      |                      ^
C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/WKTJsiDispatcher.h:94:22: note: for type 'const std::exception &'
   94 |       } catch (const std::exception &err) {
      |                      ^
1 warning generated.
[7/9] Building CXX object CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/wrappers/WKTJsiWrapper.cpp.o
[8/9] Building CXX object CMakeFiles/rnworklets.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/react-native-worklets-core/cpp/WKTJsiWorkletContext.cpp.o
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/cpp/WKTJsiWorkletContext.cpp:3:
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/cpp/WKTJsiWorkletApi.h:14:
In file included from C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/sharedvalues/WKTJsiSharedValue.h:8:
C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/WKTJsiDispatcher.h:97:22: warning: exception of type 'const std::runtime_error &' will be caught by earlier handler [-Wexceptions]
   97 |       } catch (const std::runtime_error &err) {
      |                      ^
C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/../cpp/WKTJsiDispatcher.h:94:22: note: for type 'const std::exception &'
   94 |       } catch (const std::exception &err) {
      |                      ^
1 warning generated.
[9/9] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\5t5f5h6s\obj\x86_64\librnworklets.so
