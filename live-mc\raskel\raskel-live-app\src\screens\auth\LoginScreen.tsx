import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation';
import { useNavigation } from '@react-navigation/native';

type LoginScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Login'>;

const LoginScreen = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { signIn, loading, error } = useAuth();
  const navigation = useNavigation<LoginScreenNavigationProp>();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }
    await signIn(email, password);
  };

  async function signInWithGoogle() {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: 'msrfi://auth/callback'
      }
    });
    if (error) {
      Alert.alert('Error', error.message);
    }
  }

  async function signInWithTiktok() {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'tiktok',
      options: {
        redirectTo: 'msrfi://auth/callback'
      }
    });
    if (error) {
      Alert.alert('Error', error.message);
    }
  }

  async function signInWithInstagram() {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'instagram',
      options: {
        redirectTo: 'msrfi://auth/callback'
      }
    });
    if (error) {
      Alert.alert('Error', error.message);
    }
  }

  async function signInWithFacebook() {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'facebook',
      options: {
        redirectTo: 'msrfi://auth/callback'
      }
    });
    if (error) {
      Alert.alert('Error', error.message);
    }
  }

  async function signInWithLinkedin() {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'linkedin',
      options: {
        redirectTo: 'msrfi://auth/callback'
      }
    });
    if (error) {
      Alert.alert('Error', error.message);
    }
  }

  async function signInWithYoutube() {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'youtube',
      options: {
        redirectTo: 'msrfi://auth/callback'
      }
    });
    if (error) {
      Alert.alert('Error', error.message);
    }
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Fish Kaster</Text>
      <Text style={styles.subtitle}>Fishing Streaming Platform</Text>

      <View style={styles.formContainer}>
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          autoCapitalize="none"
          keyboardType="email-address"
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />

        {error && <Text style={styles.errorText}>{error}</Text>}

        <TouchableOpacity style={styles.button} onPress={handleLogin} disabled={loading}>
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.buttonText}>Login</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity style={styles.googleButton} onPress={signInWithGoogle} disabled={loading}>
          <Text style={styles.googleButtonText}>Sign in with Google</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.tiktokButton} onPress={signInWithTiktok} disabled={loading}>
          <Text style={styles.buttonText}>Sign in with TikTok</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.instagramButton} onPress={signInWithInstagram} disabled={loading}>
          <Text style={styles.buttonText}>Sign in with Instagram</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.facebookButton} onPress={signInWithFacebook} disabled={loading}>
          <Text style={styles.buttonText}>Sign in with Facebook</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.linkedinButton} onPress={signInWithLinkedin} disabled={loading}>
          <Text style={styles.buttonText}>Sign in with LinkedIn</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.youtubeButton} onPress={signInWithYoutube} disabled={loading}>
          <Text style={styles.buttonText}>Sign in with YouTube</Text>
        </TouchableOpacity>

        <TouchableOpacity onPress={() => navigation.navigate('Register')}>
          <Text style={styles.linkText}>Don't have an account? Sign up</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 30,
    color: '#666',
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  button: {
    backgroundColor: '#4a90e2',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  googleButton: {
    backgroundColor: '#DB4437',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  googleButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  tiktokButton: {
    backgroundColor: '#000000',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  instagramButton: {
    backgroundColor: '#C13584',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  facebookButton: {
    backgroundColor: '#4267B2',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  linkedinButton: {
    backgroundColor: '#0077B5',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  youtubeButton: {
    backgroundColor: '#FF0000',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  linkText: {
    color: '#4a90e2',
    textAlign: 'center',
    marginTop: 20,
  },
  errorText: {
    color: 'red',
    marginBottom: 10,
    textAlign: 'center',
  },
});

export default LoginScreen;