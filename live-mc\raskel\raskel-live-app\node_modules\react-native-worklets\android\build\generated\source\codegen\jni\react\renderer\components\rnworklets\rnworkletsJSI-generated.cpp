/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "rnworkletsJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeWorkletsModuleCxxSpecJSI_installTurboModule(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeWorkletsModuleCxxSpecJSI *>(&turboModule)->installTurboModule(
    rt
  );
}

NativeWorkletsModuleCxxSpecJSI::NativeWorkletsModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("WorkletsModule", jsInvoker) {
  methodMap_["installTurboModule"] = MethodMetadata {0, __hostFunction_NativeWorkletsModuleCxxSpecJSI_installTurboModule};
}


} // namespace facebook::react
