<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9fa3c5b8-249b-4400-96d5-2b29d0c19f7b" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/raskel/Live Stream App  Core Requirements &amp; Tech Stack.md" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/Live Stream App  Core Requirements &amp; Tech Stack.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/agent_engineer.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/App.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/App.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/android/app/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/android/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/android/app/src/main/res/values/strings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/android/app/src/main/res/values/strings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/android/gradle.properties" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/android/gradle.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/app.json" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/app.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/eas.json" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/eas.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/components/ui/Button.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/components/ui/Button.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/contexts/AuthContext.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/contexts/AuthContext.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/hooks/useAgora.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/hooks/useChat.ts" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/hooks/useChat.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/lib/agora.ts" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/lib/agora.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/navigation/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/navigation/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/screens/HomeScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/screens/HomeScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/screens/ProfileScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/screens/ProfileScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/screens/streaming/CreateStreamScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/screens/streaming/CreateStreamScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/screens/streaming/ViewStreamScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/screens/streaming/ViewStreamScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/services/api.ts" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/services/api.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/raskel/raskel-live-app/src/types/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/raskel/raskel-live-app/src/types/index.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/raskel" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 8
}]]></component>
  <component name="ProjectId" id="32OnDcIJ1hRHjNeXd3ryozEAw5Y" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "C:/Users/<USER>/forge/live-ak",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "to.speed.mode.migration.done": "true",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b598e85cdad2-JavaScript-WS-252.25557.126" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9fa3c5b8-249b-4400-96d5-2b29d0c19f7b" name="Changes" comment="" />
      <created>1757300368465</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757300368465</updated>
      <workItem from="1757300369835" duration="1994000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>