# Fish Kaster - Fishing Streaming Platform

A professional live streaming application for fishing enthusiasts to share their adventures, built with React Native, Expo, Agora.io for real-time video streaming, and Supabase for backend services.

## ✨ Features

### Core Streaming Features
- **Real-time Video Streaming**: High-quality live video using Agora.io SDK
- **Interactive Chat**: Real-time chat during live streams
- **Viewer Management**: Track active viewers and viewer count
- **Stream Controls**: Camera/microphone toggle, screen sharing (Android)
- **Stream Discovery**: Browse live streams on the home screen

### User Management
- **Authentication**: Secure sign up, sign in, and sign out
- **User Profiles**: Customizable user profiles
- **Stream History**: View past streams and recordings

### Technical Features
- **Cross-platform**: iOS and Android support
- **Real-time Database**: Live updates using Supabase real-time
- **Offline Support**: Graceful handling of network issues
- **Performance Optimized**: Efficient video encoding and streaming

## 🛠 Tech Stack

- **Frontend**: React Native with Expo
- **Live Video**: Agora.io RTC SDK
- **Backend**: Supabase (PostgreSQL + Real-time)
- **Navigation**: React Navigation v6
- **Storage**: Async Storage for authentication persistence
- **State Management**: React Hooks + Context API

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or newer)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd msrfi-live-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase Database**
   - Follow the [Database Setup Guide](docs/database-setup.md)
   - Run the migration script in your Supabase SQL editor
   - Enable real-time for required tables

4. **Configure Agora.io**
   - Create an Agora.io account
   - Get your App ID and App Certificate
   - Update `src/lib/agora.ts` with your credentials

5. **Start the token server**
   ```bash
   cd server
   npm install
   npm start
   ```

6. **Start the Expo development server**
   ```bash
   npm start
   ```

## 📱 Usage

### For Streamers (Hosts)

1. **Create a Stream**
   - Tap "Create Stream" from the home screen
   - Enter stream title and description
   - Tap "Start Streaming"

2. **Stream Controls**
   - Toggle camera on/off
   - Mute/unmute microphone
   - Start/stop screen sharing (Android)
   - Monitor viewer count and chat

3. **End Stream**
   - Tap "End Stream" button
   - Confirm to stop the live stream

### For Viewers

1. **Join a Stream**
   - Browse live streams on the home screen
   - Tap on a stream to join

2. **Interact**
   - Send messages in real-time chat
   - View other viewers' messages
   - See live viewer count

## 🏗 Architecture

### Core Components

```
src/
├── components/          # Reusable UI components
├── contexts/           # React Context providers
├── hooks/              # Custom React hooks
│   └── useAgora.ts     # Agora SDK integration
├── lib/                # External service configurations
│   ├── agora.ts        # Agora configuration
│   └── supabase.ts     # Supabase client
├── navigation/         # Navigation configuration
├── screens/            # Screen components
│   ├── auth/           # Authentication screens
│   └── streaming/      # Streaming-related screens
├── services/           # API service layer
│   └── api.ts          # Database operations
└── types/              # TypeScript type definitions
```

### Key Features Implementation

#### Real-time Video Streaming
- **Agora SDK Integration**: Custom `useAgora` hook manages RTC engine
- **Token Generation**: Secure server-side token generation
- **Video Controls**: Camera, microphone, and screen sharing controls
- **Multi-user Support**: Host and audience roles with different permissions

#### Real-time Chat
- **Supabase Real-time**: Live message synchronization
- **Message Persistence**: Chat history stored in PostgreSQL
- **User Management**: Username display and message attribution

#### Stream Management
- **Database Integration**: Stream metadata stored in Supabase
- **Viewer Tracking**: Real-time viewer count and active user tracking
- **Stream Lifecycle**: Create, join, leave, and end stream operations

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the server directory:

```env
AGORA_APP_ID=your_agora_app_id
AGORA_APP_CERTIFICATE=your_agora_app_certificate
PORT=8080
```

### Supabase Configuration

Update `src/lib/supabase.ts`:

```typescript
const supabaseUrl = 'your_supabase_url';
const supabaseAnonKey = 'your_supabase_anon_key';
```

### Agora Configuration

Update `src/lib/agora.ts`:

```typescript
export const agoraConfig = {
  appId: 'your_agora_app_id',
  appCertificate: 'your_agora_app_certificate',
};
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Manual Testing

1. **Stream Creation**: Test creating and starting a stream
2. **Video Quality**: Verify video and audio quality
3. **Chat Functionality**: Test real-time messaging
4. **Viewer Management**: Test joining/leaving streams
5. **Error Handling**: Test network disconnections and reconnections

## 🚀 Deployment

### Building for Production

```bash
# Build for Android
expo build:android

# Build for iOS
expo build:ios
```

### Server Deployment

Deploy the token server to a cloud provider:

1. **Heroku**
   ```bash
   git subtree push --prefix server heroku main
   ```

2. **Vercel**
   ```bash
   cd server
   vercel --prod
   ```

3. **AWS/Google Cloud**
   - Use Docker container
   - Set up environment variables
   - Configure load balancing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Update documentation
- Follow the existing code style
- Test on both iOS and Android

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Common Issues

1. **Agora Connection Issues**
   - Check App ID and Certificate
   - Verify token server is running
   - Check network connectivity

2. **Database Issues**
   - Verify Supabase configuration
   - Check RLS policies
   - Ensure real-time is enabled

3. **Build Issues**
   - Clear Expo cache: `expo r -c`
   - Reinstall dependencies: `rm -rf node_modules && npm install`
   - Check Expo CLI version

### Getting Help

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/msrfi)
- 📖 Documentation: [docs.msrfi.com](https://docs.msrfi.com)
- 🐛 Issues: [GitHub Issues](https://github.com/msrfi/issues)

## 🙏 Acknowledgments

- [Agora.io](https://agora.io) for real-time video streaming
- [Supabase](https://supabase.com) for backend services
- [Expo](https://expo.dev) for React Native development
- [React Navigation](https://reactnavigation.org) for navigation