{"installationFolder": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-vision-camera\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-vision-camera", "packageInfo": {"packageName": "react-native-vision-camera", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "VisionCamera", "moduleHeaders": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-vision-camera\\android\\build\\headers\\visioncamera", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-vision-camera\\android\\build\\intermediates\\cxx\\Debug\\3h292v2g\\obj\\arm64-v8a\\libVisionCamera.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-vision-camera\\android\\.cxx\\Debug\\3h292v2g\\arm64-v8a\\android_gradle_build.json"}, {"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-vision-camera\\android\\build\\intermediates\\cxx\\Debug\\3h292v2g\\obj\\x86_64\\libVisionCamera.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-vision-camera\\android\\.cxx\\Debug\\3h292v2g\\x86_64\\android_gradle_build.json"}]}]}}