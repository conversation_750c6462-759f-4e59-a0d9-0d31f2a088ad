import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  Dimensions,
  SafeAreaView,
  Modal,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation';
import { MediaUpload, MediaItem } from '../components/MediaUpload';
import { CloudinaryVideoPlayer } from '../components/video/CloudinaryVideoPlayer';

type ProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Profile'>;

const { width: screenWidth } = Dimensions.get('window');

interface UserProfile {
  id: string;
  username: string;
  full_name: string;
  bio: string;
  sector: 'angler' | 'guide' | 'tournament' | 'recreational';
  location: string;
  verified: boolean;
  profileImage?: string;
  totalProjects: number;
  completedProjects: number;
  rating: number;
  reviewCount: number;
  yearsExperience: number;
  specialties: string[];
  portfolio: PortfolioProject[];
  certifications: string[];
  totalStreams: number;
  followers: number;
  following: number;
  joinedDate: string;
}

interface PortfolioProject {
  id: string;
  title: string;
  description: string;
  imageUrl?: string;
  completedDate: string;
  projectValue: number;
}

type TabType = 'overview' | 'portfolio' | 'reviews' | 'gallery';

const ProfileScreen = () => {
  const { user, signOut } = useAuth();
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [galleryMedia, setGalleryMedia] = useState<MediaItem[]>([]);
  const [showMediaModal, setShowMediaModal] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);

  // Sector-specific themes
  const getSectorTheme = (sector: string) => {
    switch (sector) {
      case 'angler':
        return {
          primary: '#2196F3',
          secondary: '#E3F2FD',
          gradient: ['#2196F3', '#42A5F5'],
          icon: '🎣',
        };
      case 'guide':
        return {
          primary: '#4CAF50',
          secondary: '#E8F5E8',
          gradient: ['#4CAF50', '#66BB6A'],
          icon: '🚤',
        };
      case 'diy':
        return {
          primary: '#9C27B0',
          secondary: '#F3E5F5',
          gradient: ['#9C27B0', '#BA68C8'],
          icon: '🛠️',
        };
      case 'realEstate':
        return {
          primary: '#2196F3',
          secondary: '#E3F2FD',
          gradient: ['#2196F3', '#42A5F5'],
          icon: '🏢',
        };
      default:
        return {
          primary: '#2E7DFF',
          secondary: '#F0F4FF',
          gradient: ['#2E7DFF', '#5A96FF'],
          icon: '👤',
        };
    }
  };

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        // Mock comprehensive profile data
        const mockProfile: UserProfile = {
          id: user?.id || '1',
          username: 'angler_pro',
          full_name: user?.email?.split('@')[0] || 'Captain Mike',
          bio: 'Professional fishing guide with 15+ years experience on the Great Lakes. Specializing in bass tournaments, deep sea fishing, and teaching new anglers.',
          sector: 'angler',
          location: 'San Francisco, CA',
          verified: true,
          totalProjects: 247,
          completedProjects: 235,
          rating: 4.9,
          reviewCount: 198,
          yearsExperience: 15,
          specialties: ['Kitchen Remodeling', 'Bathroom Renovation', 'Custom Cabinets', 'Flooring', 'Electrical'],
          portfolio: [
            {
              id: '1',
              title: 'Modern Kitchen Transformation',
              description: 'Complete kitchen remodel with custom cabinetry and quartz countertops',
              completedDate: '2024-01-15',
              projectValue: 45000,
            },
            {
              id: '2',
              title: 'Luxury Master Bathroom',
              description: 'Spa-like bathroom renovation with heated floors and rainfall shower',
              completedDate: '2023-11-20',
              projectValue: 28000,
            },
            {
              id: '3',
              title: 'Open Concept Living Space',
              description: 'Removed walls to create modern open floor plan',
              completedDate: '2023-09-10',
              projectValue: 35000,
            },
          ],
          certifications: ['Licensed General Contractor', 'OSHA 30 Certified', 'Lead-Safe Certified'],
          totalStreams: 42,
          followers: 1247,
          following: 189,
          joinedDate: '2019-03-15',
        };

        // Initialize sample gallery media
        const sampleGalleryMedia: MediaItem[] = [
          {
            id: '1',
            name: 'Kitchen Before & After',
            type: 'video',
            uri: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
            cloudinaryId: 'profile/kitchen_transformation',
            uploadStatus: 'completed',
            uploadProgress: 100,
            tags: ['kitchen', 'renovation', 'before-after'],
            category: 'project-showcase'
          },
          {
            id: '2',
            name: 'Bathroom Renovation Process',
            type: 'video',
            uri: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
            cloudinaryId: 'profile/bathroom_renovation',
            uploadStatus: 'completed',
            uploadProgress: 100,
            tags: ['bathroom', 'renovation', 'process'],
            category: 'project-showcase'
          },
          {
            id: '3',
            name: 'Custom Cabinet Installation',
            type: 'image',
            uri: 'placeholder-image-1',
            cloudinaryId: 'profile/cabinet_installation',
            uploadStatus: 'completed',
            uploadProgress: 100,
            tags: ['cabinets', 'installation', 'custom'],
            category: 'project-showcase'
          },
          {
            id: '4',
            name: 'Flooring Installation Demo',
            type: 'video',
            uri: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
            cloudinaryId: 'profile/flooring_demo',
            uploadStatus: 'completed',
            uploadProgress: 100,
            tags: ['flooring', 'installation', 'demo'],
            category: 'tutorial'
          },
          {
            id: '5',
            name: 'Electrical Work Progress',
            type: 'image',
            uri: 'placeholder-image-2',
            cloudinaryId: 'profile/electrical_work',
            uploadStatus: 'completed',
            uploadProgress: 100,
            tags: ['electrical', 'wiring', 'progress'],
            category: 'project-showcase'
          },
          {
            id: '6',
            name: 'Tool Collection Showcase',
            type: 'image',
            uri: 'placeholder-image-3',
            cloudinaryId: 'profile/tool_collection',
            uploadStatus: 'completed',
            uploadProgress: 100,
            tags: ['tools', 'equipment', 'showcase'],
            category: 'portfolio'
          }
        ];
        
        setGalleryMedia(sampleGalleryMedia);
        
        setProfile(mockProfile);
      } catch (error) {
        console.error('Error fetching profile:', error);
        Alert.alert('Error', 'Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchProfile();
    }
  }, [user]);

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
              navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            } catch (error) {
              console.error('Error signing out:', error);
              Alert.alert('Error', 'Failed to sign out');
            }
          },
        },
      ]
    );
  };

  const handleEditProfile = () => {
    Alert.alert('Coming Soon', 'Profile editing will be available in a future update');
  };

  const handleViewProjects = () => {
    navigation.navigate('Projects');
  };

  const handleSettings = () => {
    navigation.navigate('Settings');
  };

  const handlePrivacyPolicy = () => {
    navigation.navigate('Settings');
  };

  const handleTermsOfService = () => {
    navigation.navigate('Settings');
  };

  const handleHelpSupport = () => {
    navigation.navigate('Settings');
  };

  const handleSubmitFeedback = () => {
    navigation.navigate('Settings');
  };

  // New function to navigate to Professional Features
  const handleProfessionalFeatures = () => {
    navigation.navigate('ProfessionalFeatures');
  };

  // Media handling functions
  const handleMediaUploaded = (newMedia: MediaItem[]) => {
    setGalleryMedia(prev => [...prev, ...newMedia]);
    Alert.alert(
      'Upload Complete! 🎉',
      `${newMedia.length} file(s) added to your project gallery.`,
      [{ text: 'Great!' }]
    );
  };

  const handleMediaSelect = (media: MediaItem) => {
    setSelectedMedia(media);
    setShowMediaModal(true);
  };

  const handleDeleteMedia = (mediaId: string) => {
    Alert.alert(
      'Delete Media',
      'Are you sure you want to remove this from your gallery?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setGalleryMedia(prev => prev.filter(item => item.id !== mediaId));
            setShowMediaModal(false);
            setSelectedMedia(null);
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2E7DFF" />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.errorText}>Failed to load profile</Text>
      </View>
    );
  }

  const theme = getSectorTheme(profile.sector);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <View style={styles.tabContent}>
            {/* Professional Information */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Professional Information</Text>
              <View style={styles.infoGrid}>
                <View style={styles.infoItem}>
                  <Text style={styles.infoLabel}>Experience</Text>
                  <Text style={styles.infoValue}>{profile.yearsExperience} years</Text>
                </View>
                <View style={styles.infoItem}>
                  <Text style={styles.infoLabel}>Total Projects</Text>
                  <Text style={styles.infoValue}>{profile.totalProjects}</Text>
                </View>
                <View style={styles.infoItem}>
                  <Text style={styles.infoLabel}>Success Rate</Text>
                  <Text style={styles.infoValue}>{Math.round((profile.completedProjects / profile.totalProjects) * 100)}%</Text>
                </View>
                <View style={styles.infoItem}>
                  <Text style={styles.infoLabel}>Member Since</Text>
                  <Text style={styles.infoValue}>{new Date(profile.joinedDate).getFullYear()}</Text>
                </View>
              </View>
            </View>

            {/* Specialties */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Specialties</Text>
              <View style={styles.specialtiesContainer}>
                {profile.specialties.map((specialty, index) => (
                  <View key={index} style={[styles.specialtyTag, { backgroundColor: theme.primary }]}>
                    <Text style={styles.specialtyText}>{specialty}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Certifications */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Certifications</Text>
              <View style={styles.infoGrid}>
                {profile.certifications.map((cert, index) => (
                  <View key={index} style={styles.infoItem}>
                    <Text style={styles.infoLabel}>✓ {cert}</Text>
                    <Text style={[styles.infoValue, { color: theme.primary }]}>Verified</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        );

      case 'portfolio':
        return (
          <View style={styles.tabContent}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Recent Projects</Text>
              {profile.portfolio.map((project) => (
                <View key={project.id} style={styles.projectCard}>
                  <View style={styles.projectImage}>
                    <Text style={styles.projectImageIcon}>🏗️</Text>
                  </View>
                  <View style={styles.projectInfo}>
                    <Text style={styles.projectTitle}>{project.title}</Text>
                    <Text style={styles.projectDescription}>{project.description}</Text>
                    <View style={styles.projectMeta}>
                      <Text style={styles.projectDate}>{new Date(project.completedDate).toLocaleDateString()}</Text>
                      <Text style={styles.projectValue}>${project.projectValue.toLocaleString()}</Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </View>
        );

      case 'reviews':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.comingSoon}>Reviews feature coming soon!</Text>
          </View>
        );

      case 'gallery':
        return (
          <View style={styles.tabContent}>
            {/* Media Upload Section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Upload Project Media</Text>
              <Text style={styles.sectionSubtitle}>
                Share photos and videos of your work to showcase your expertise
              </Text>
              <MediaUpload
                onMediaUploaded={handleMediaUploaded}
                uploadFolder={`profiles/${profile?.id || 'default'}`}
                uploadCategory="profile-gallery"
                uploadTags={['profile', 'portfolio', profile?.sector || 'general']}
                maxFiles={10}
                title=""
                subtitle="Upload photos and videos to build your professional portfolio"
                style={styles.mediaUploadContainer}
              />
            </View>

            {/* Gallery Grid */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>
                Your Gallery ({galleryMedia.length})
              </Text>
              {galleryMedia.length === 0 ? (
                <View style={styles.emptyGallery}>
                  <Text style={styles.emptyGalleryIcon}>📸</Text>
                  <Text style={styles.emptyGalleryText}>No media uploaded yet</Text>
                  <Text style={styles.emptyGallerySubtext}>
                    Upload photos and videos to showcase your work
                  </Text>
                </View>
              ) : (
                <View style={styles.galleryGrid}>
                  {galleryMedia.map((media) => (
                    <TouchableOpacity
                      key={media.id}
                      style={styles.galleryItem}
                      onPress={() => handleMediaSelect(media)}
                    >
                      <View style={styles.galleryThumbnail}>
                        <Text style={styles.galleryIcon}>
                          {media.type === 'video' ? '🎬' : '📸'}
                        </Text>
                        {media.type === 'video' && (
                          <View style={styles.playOverlay}>
                            <Text style={styles.playIcon}>▶️</Text>
                          </View>
                        )}
                      </View>
                      <Text style={styles.galleryItemTitle} numberOfLines={2}>
                        {media.name}
                      </Text>
                      <View style={styles.galleryItemTags}>
                        {media.tags?.slice(0, 2).map((tag, index) => (
                          <View key={index} style={styles.galleryTag}>
                            <Text style={styles.galleryTagText}>{tag}</Text>
                          </View>
                        ))}
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>
        <TouchableOpacity style={styles.settingsButton} onPress={handleSettings}>
          <Text style={styles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Hero Section with Gradient Background */}
        <View style={[styles.heroSection, { 
          backgroundColor: theme.primary,
          background: `linear-gradient(135deg, ${theme.gradient[0]}, ${theme.gradient[1]})` 
        }]}>
          <View style={styles.profileInfo}>
            <View style={styles.avatarContainer}>
              <View style={[styles.avatar, { borderColor: 'white' }]}>
                <Text style={styles.avatarText}>
                  {profile.full_name.charAt(0).toUpperCase()}
                </Text>
              </View>
              {profile.verified && (
                <View style={styles.verifiedBadge}>
                  <Text style={styles.verifiedIcon}>✓</Text>
                </View>
              )}
            </View>
            
            <View style={styles.profileDetails}>
              <Text style={styles.profileName}>{profile.full_name}</Text>
              <View style={styles.sectorBadge}>
                <Text style={styles.sectorIcon}>{theme.icon}</Text>
                <Text style={styles.sectorText}>{profile.sector.toUpperCase()}</Text>
              </View>
              <Text style={styles.location}>📍 {profile.location}</Text>
            </View>
          </View>
          
          <Text style={styles.bio}>{profile.bio}</Text>
          
          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.primaryButton} onPress={handleEditProfile}>
              <Text style={styles.primaryButtonText}>Edit Profile</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.secondaryButton} onPress={handleViewProjects}>
              <Text style={styles.secondaryButtonText}>View Projects</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: theme.primary }]}>{profile.rating}</Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{profile.completedProjects}</Text>
            <Text style={styles.statLabel}>Projects</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{profile.reviewCount}</Text>
            <Text style={styles.statLabel}>Reviews</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{profile.followers}</Text>
            <Text style={styles.statLabel}>Followers</Text>
          </View>
        </View>

        {/* Tabs */}
        <View style={styles.tabContainer}>
          {(['overview', 'portfolio', 'reviews', 'gallery'] as TabType[]).map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tab, activeTab === tab && [styles.activeTab, { backgroundColor: theme.primary }]]}
              onPress={() => setActiveTab(tab)}
            >
              <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        {renderTabContent()}

        {/* Professional Features Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Professional Features</Text>
          <View style={styles.infoGrid}>
            <TouchableOpacity style={styles.infoItem} onPress={handleProfessionalFeatures}>
              <Text style={styles.infoLabel}>Portfolio & Verification</Text>
              <Text style={styles.infoValue}>→</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings & Support</Text>
          <View style={styles.infoGrid}>
            <TouchableOpacity style={styles.infoItem} onPress={handlePrivacyPolicy}>
              <Text style={styles.infoLabel}>Privacy Policy</Text>
              <Text style={styles.infoValue}>→</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.infoItem} onPress={handleTermsOfService}>
              <Text style={styles.infoLabel}>Terms of Service</Text>
              <Text style={styles.infoValue}>→</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.infoItem} onPress={handleHelpSupport}>
              <Text style={styles.infoLabel}>Help & Support</Text>
              <Text style={styles.infoValue}>→</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.infoItem} onPress={handleSubmitFeedback}>
              <Text style={styles.infoLabel}>Submit Feedback</Text>
              <Text style={styles.infoValue}>→</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Sign Out Button */}
        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Media Viewer Modal */}
      <Modal
        visible={showMediaModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMediaModal(false)}
      >
        <View style={styles.mediaModalOverlay}>
          <View style={styles.mediaModalContent}>
            <View style={styles.mediaModalHeader}>
              <Text style={styles.mediaModalTitle}>
                {selectedMedia?.name || 'Media Viewer'}
              </Text>
              <TouchableOpacity
                style={styles.mediaModalCloseButton}
                onPress={() => setShowMediaModal(false)}
              >
                <Text style={styles.mediaModalCloseText}>×</Text>
              </TouchableOpacity>
            </View>
            
            {selectedMedia && (
              <View style={styles.mediaModalBody}>
                {selectedMedia.type === 'video' ? (
                  <CloudinaryVideoPlayer
                    videoUrl={selectedMedia.uri}
                    cloudinaryId={selectedMedia.cloudinaryId}
                    title={selectedMedia.name}
                    autoPlay={false}
                    showControls={true}
                    style={styles.modalVideoPlayer}
                    onError={(error) => {
                      console.error('Video player error:', error);
                      Alert.alert('Video Error', 'Failed to load video: ' + error);
                    }}
                  />
                ) : (
                  <View style={styles.modalImagePlaceholder}>
                    <Text style={styles.modalImageIcon}>📸</Text>
                    <Text style={styles.modalImageText}>Image Preview</Text>
                    <Text style={styles.modalImageSubtext}>{selectedMedia.name}</Text>
                  </View>
                )}
                
                <View style={styles.mediaModalInfo}>
                  <View style={styles.mediaModalMeta}>
                    <Text style={styles.mediaModalMetaText}>Type: {selectedMedia.type}</Text>
                    <Text style={styles.mediaModalMetaText}>Category: {selectedMedia.category}</Text>
                    <Text style={styles.mediaModalMetaText}>Status: {selectedMedia.uploadStatus}</Text>
                  </View>
                  
                  {selectedMedia.tags && selectedMedia.tags.length > 0 && (
                    <View style={styles.mediaModalTags}>
                      <Text style={styles.mediaModalTagsTitle}>Tags:</Text>
                      <View style={styles.mediaModalTagsContainer}>
                        {selectedMedia.tags.map((tag, index) => (
                          <View key={index} style={styles.mediaModalTag}>
                            <Text style={styles.mediaModalTagText}>{tag}</Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  )}

                  <TouchableOpacity 
                    style={styles.deleteMediaButton}
                    onPress={() => handleDeleteMedia(selectedMedia.id)}
                  >
                    <Text style={styles.deleteMediaText}>🗑️ Remove from Gallery</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F7F9FC',
  },
  
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  
  errorText: {
    fontSize: 16,
    color: '#F44336',
  },
  
  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E8E8E8',
  },
  
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  backIcon: {
    fontSize: 18,
    color: '#333',
  },
  
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A2E',
  },
  
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  settingsIcon: {
    fontSize: 16,
  },
  
  // Content
  content: {
    flex: 1,
  },
  
  // Hero Section
  heroSection: {
    padding: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    marginBottom: 20,
  },
  
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
  },
  
  avatarText: {
    fontSize: 32,
    fontWeight: '700',
    color: 'white',
  },
  
  verifiedBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  
  verifiedIcon: {
    fontSize: 12,
    color: 'white',
    fontWeight: '700',
  },
  
  profileDetails: {
    flex: 1,
  },
  
  profileName: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
    marginBottom: 4,
  },
  
  sectorBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginBottom: 6,
  },
  
  sectorIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  
  sectorText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  
  location: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.9)',
  },
  
  bio: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    lineHeight: 24,
    marginBottom: 20,
  },
  
  // Action Buttons
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  
  primaryButton: {
    flex: 1,
    backgroundColor: 'white',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A2E',
  },
  
  secondaryButton: {
    flex: 1,
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  
  // Stats Grid
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 20,
  },
  
  statCard: {
    flex: 1,
    minWidth: (screenWidth - 64) / 2,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1A2E',
    marginBottom: 4,
  },
  
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  
  // Tabs
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  
  activeTab: {
    backgroundColor: '#2E7DFF',
  },
  
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  
  activeTabText: {
    color: 'white',
    fontWeight: '600',
  },
  
  // Tab Content
  tabContent: {
    paddingHorizontal: 20,
  },
  
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A2E',
    marginBottom: 16,
  },
  
  // Info Grid
  infoGrid: {
    gap: 12,
  },
  
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  
  infoLabel: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A1A2E',
    flex: 2,
    textAlign: 'right',
  },
  
  // Specialties
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  
  specialtyTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  
  specialtyText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '500',
  },
  
  // Portfolio
  projectCard: {
    flexDirection: 'row',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
  },
  
  projectImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  
  projectImageIcon: {
    fontSize: 24,
  },
  
  projectInfo: {
    flex: 1,
  },
  
  projectTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A2E',
    marginBottom: 4,
  },
  
  projectDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  
  projectMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  projectDate: {
    fontSize: 12,
    color: '#999',
  },
  
  projectValue: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '600',
  },
  
  // Section subtitle
  sectionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },

  // Media Upload
  mediaUploadContainer: {
    marginTop: 0,
  },

  // Gallery
  galleryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  
  galleryItem: {
    width: (screenWidth - 76) / 2,
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 12,
  },

  galleryThumbnail: {
    height: 120,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  
  galleryIcon: {
    fontSize: 32,
    color: '#666',
  },

  playOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -15 }, { translateY: -15 }],
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(46, 125, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  playIcon: {
    fontSize: 12,
    color: 'white',
  },

  galleryItemTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1A1A2E',
    padding: 8,
    paddingBottom: 4,
    lineHeight: 16,
  },

  galleryItemTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 8,
    paddingBottom: 8,
    gap: 4,
  },

  galleryTag: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },

  galleryTagText: {
    fontSize: 9,
    color: '#2196F3',
    fontWeight: '500',
  },

  // Empty Gallery
  emptyGallery: {
    padding: 40,
    alignItems: 'center',
  },

  emptyGalleryIcon: {
    fontSize: 48,
    marginBottom: 12,
  },

  emptyGalleryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },

  emptyGallerySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
  
  // Coming Soon
  comingSoon: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    padding: 40,
  },
  
  // Sign Out
  signOutButton: {
    backgroundColor: '#F44336',
    marginHorizontal: 20,
    marginVertical: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },

  // Media Modal
  mediaModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  mediaModalContent: {
    width: screenWidth - 20,
    maxHeight: '90%',
    backgroundColor: 'white',
    borderRadius: 15,
    overflow: 'hidden',
  },

  mediaModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: '#2E7DFF',
  },

  mediaModalTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginRight: 10,
  },

  mediaModalCloseButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  mediaModalCloseText: {
    fontSize: 20,
    color: 'white',
    fontWeight: 'bold',
  },

  mediaModalBody: {
    padding: 15,
  },

  modalVideoPlayer: {
    height: 200,
    marginBottom: 15,
  },

  modalImagePlaceholder: {
    height: 200,
    backgroundColor: '#F0F4FF',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },

  modalImageIcon: {
    fontSize: 48,
    marginBottom: 8,
  },

  modalImageText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
  },

  modalImageSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },

  mediaModalInfo: {
    marginBottom: 10,
  },

  mediaModalMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 15,
    marginBottom: 15,
  },

  mediaModalMetaText: {
    fontSize: 12,
    color: '#666',
  },

  mediaModalTags: {
    marginBottom: 20,
  },

  mediaModalTagsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },

  mediaModalTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },

  mediaModalTag: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
  },

  mediaModalTagText: {
    fontSize: 11,
    color: '#2196F3',
    fontWeight: '500',
  },

  deleteMediaButton: {
    backgroundColor: '#F44336',
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
  },

  deleteMediaText: {
    fontSize: 14,
    color: 'white',
    fontWeight: 'bold',
  },
});

export default ProfileScreen;