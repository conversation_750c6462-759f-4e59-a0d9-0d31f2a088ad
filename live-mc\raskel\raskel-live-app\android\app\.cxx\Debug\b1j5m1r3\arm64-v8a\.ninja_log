# ninja log v5
114	6037	7802461820527864	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2e4509cb0d0a1d5cc7411f483425a060/ComponentDescriptors.cpp.o	135c1229d7f86d1a
26	3976	7802461799819333	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/29c6ac0a1dd6ebb9204efeee5b088e58/rnasyncstorageJSI-generated.cpp.o	89b46215c03c7a68
2	16	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a/CMakeFiles/cmake.verify_globs	8792d7cf67f22066
98	6141	7802461821568926	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/002469d5b84800fdd549c5e175ca642e/RNCSafeAreaViewShadowNode.cpp.o	aa2575af172f20df
139	4356	7802461803558256	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/34e83742130ce8fa5cb0bd5b71d73d82/jni/safeareacontext-generated.cpp.o	5c8e3c83514a7c78
23	2963	7802461789689282	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	f3bb9d5e314d67db
16	4421	7802461804539469	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	55973994a41e5851
60	3087	7802461790735165	AgoraRtcNgSpec_autolinked_build/CMakeFiles/react_codegen_AgoraRtcNgSpec.dir/react/renderer/components/AgoraRtcNgSpec/States.cpp.o	cd685c0ebf6047d9
4555	7681	7802461837097704	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o	971d6b016106ae21
3514	8656	7802461846892473	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSScreenShadowNode.cpp.o	395bda36d2acae07
50	6183	7802461821984505	AgoraRtcNgSpec_autolinked_build/CMakeFiles/react_codegen_AgoraRtcNgSpec.dir/react/renderer/components/AgoraRtcNgSpec/ComponentDescriptors.cpp.o	dca0a7c9122f4fe6
71	3241	7802461792307369	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	832b8021a865c1ff
121	3784	7802461797545738	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51215515fe21556a3837eeba2b49d411/safeareacontext/EventEmitters.cpp.o	5bcf42501a77a7c5
4654	8575	7802461846046332	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o	db58d6a9e354db23
4060	8092	7802461841238303	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/79a671fe4e2ad3a12207216f48eb05ff/components/rnscreens/rnscreensJSI-generated.cpp.o	7ed56ca9a7edd435
76	4794	7802461808264521	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	b89edf7d9f1b5a1b
41	3513	7802461795128237	AgoraRtcNgSpec_autolinked_build/CMakeFiles/react_codegen_AgoraRtcNgSpec.dir/react/renderer/components/AgoraRtcNgSpec/EventEmitters.cpp.o	69fc265e4f71498d
33	4008	7802461799885124	AgoraRtcNgSpec_autolinked_build/CMakeFiles/react_codegen_AgoraRtcNgSpec.dir/AgoraRtcNgSpec-generated.cpp.o	995f01d5c634068e
82	3554	7802461795630497	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	a70195241f0f8dd5
13	3410	7802461794257111	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	8919652f4ec6710f
2977	7961	7802461839926706	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	5a835b2b4990199c
55	5092	7802461811260230	AgoraRtcNgSpec_autolinked_build/CMakeFiles/react_codegen_AgoraRtcNgSpec.dir/react/renderer/components/AgoraRtcNgSpec/ShadowNodes.cpp.o	7094692ff8faf7ba
153	3477	7802461794907897	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51215515fe21556a3837eeba2b49d411/safeareacontext/States.cpp.o	c5ec1bd3afe18196
103	4589	7802461806126973	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/002469d5b84800fdd549c5e175ca642e/RNCSafeAreaViewState.cpp.o	546355a53b797bc1
4589	8481	7802461845130170	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o	7d8d4a1c64f4ec93
29	3826	7802461797930953	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	2cdee6cfcb638b90
4366	7850	7802461838788235	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o	25f4f60fe4ca4380
37	3961	7802461799451471	AgoraRtcNgSpec_autolinked_build/CMakeFiles/react_codegen_AgoraRtcNgSpec.dir/70cbe3ded53985a98bc8414c5f1a035d/AgoraRtcNgSpecJSI-generated.cpp.o	9cacfffb11bc725a
65	3989	7802461799825052	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	70e80ba4946930a
3989	8013	7802461840477513	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/19c14822b9fd6da13ca8af6ae442fcdf/renderer/components/rnscreens/RNSScreenState.cpp.o	4bb23e9b318e2f53
92	4030	7802461800342814	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	efde635ead5f8bcc
19	4060	7802461800733229	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	71ee04fdc379f3b8
109	4125	7802461801393946	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	3133c2f38a94d64e
4030	8828	7802461848636227	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f60e6a02b5031c13be66380d8fa3a5a7/renderer/components/rnscreens/ShadowNodes.cpp.o	e94a286f8067533
160	4270	7802461802942544	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSBottomTabsState.cpp.o	3d1579506be6adb9
127	4365	7802461803730159	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2e4509cb0d0a1d5cc7411f483425a060/safeareacontextJSI-generated.cpp.o	46127eeff90b2f2f
145	5310	7802461813297694	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51215515fe21556a3837eeba2b49d411/safeareacontext/ShadowNodes.cpp.o	99c9a99c90a56967
10	4554	7802461805721489	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	e7bc6fae849ae0f4
87	4653	7802461806477399	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	f3d5f1d99afdd8e7
4356	8586	7802461846221540	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o	dfc6c5d8b33022b7
7	4804	7802461808224522	CMakeFiles/appmodules.dir/OnLoad.cpp.o	ef9bf7c1ff66bac3
168	5330	7802461813482903	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	9bf2af9d554eab9
4126	7217	7802461832426232	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a328c61c957b5da5800f94032115c8ec/react/renderer/components/rnscreens/States.cpp.o	5d502473ad9f43e
46	5340	7802461813482903	AgoraRtcNgSpec_autolinked_build/CMakeFiles/react_codegen_AgoraRtcNgSpec.dir/react/renderer/components/AgoraRtcNgSpec/Props.cpp.o	cd8b7fda8dd20b22
133	5454	7802461814719311	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd470dadb21de9e1a8a9b540771142e1/components/safeareacontext/Props.cpp.o	8842f0a87a7c6a7b
3976	9012	7802461850522590	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	d0bcb5a7ca657993
5455	8270	7802461843045771	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/States.cpp.o	5111a99f7a895a8d
6142	6388	7802461823591202	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/arm64-v8a/libreact_codegen_safeareacontext.so	45548ac5e47901bd
3242	7440	7802461834568021	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	ef40cca9e2ec07f8
3411	8460	7802461844909490	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e64928fbfda1b3f277e4ea6d26deec00/RNSScreenStackHeaderSubviewShadowNode.cpp.o	b5c91db4c26c1f1
4804	8651	7802461846892473	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/Props.cpp.o	ae9d1a88ad391a50
3555	7762	7802461837945401	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	d8ea3b23678c8dc9
3087	8049	7802461840777765	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSModalScreenShadowNode.cpp.o	a145057690deb4ff
4794	8242	7802461842790524	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/EventEmitters.cpp.o	f38b91e54b7606e3
4422	8301	7802461843386256	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o	df31ab8f42598ad6
3477	8522	7802461845580583	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e64928fbfda1b3f277e4ea6d26deec00/RNSScreenStackHeaderConfigShadowNode.cpp.o	a76121cabecac140
4270	8670	7802461847027692	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o	ac9fefa0564f943b
5331	8904	7802461849428577	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/f62a571eb6863fc2b74961b257b7d8df/RNWorkletsSpecJSI-generated.cpp.o	8f47a6f26e542c69
3784	8752	7802461847879807	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	8300b80f5421cb89
5092	8826	7802461848610922	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/RNWorkletsSpec-generated.cpp.o	7e60a18d6ccbe34b
5340	9110	7802461851523870	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/ShadowNodes.cpp.o	2b489c98077969c1
3	9724	7802461857191597	CMakeFiles/appmodules.dir/6875011670f4259eebb9dffd6fd545ff/raskel-live-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	2a39fed98aa03542
5310	9243	7802461852845885	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/ComponentDescriptors.cpp.o	58fb0d6edbba044d
4008	9713	7802461857326855	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a328c61c957b5da5800f94032115c8ec/react/renderer/components/rnscreens/Props.cpp.o	91f22d81533850fb
3827	9989	7802461860109799	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/79a671fe4e2ad3a12207216f48eb05ff/components/rnscreens/ComponentDescriptors.cpp.o	d235dcf3b6273ae5
3962	10354	7802461863730388	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f60e6a02b5031c13be66380d8fa3a5a7/renderer/components/rnscreens/EventEmitters.cpp.o	f94d601c1dbc298
10354	10446	7802461864741521	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/arm64-v8a/libreact_codegen_rnscreens.so	7b01cf16abf72c0b
10446	10568	7802461865953212	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/arm64-v8a/libappmodules.so	b6e3e594e2c488
1	15	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a/CMakeFiles/cmake.verify_globs	8792d7cf67f22066
