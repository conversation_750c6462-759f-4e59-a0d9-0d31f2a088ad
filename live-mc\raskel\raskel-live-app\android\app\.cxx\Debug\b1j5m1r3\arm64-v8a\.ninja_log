# ninja log v5
3	27	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a/CMakeFiles/cmake.verify_globs	8792d7cf67f22066
52	2835	7802456513814147	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	832b8021a865c1ff
40	2863	7802456513668956	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	f3bb9d5e314d67db
117	2954	7802456514815090	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51215515fe21556a3837eeba2b49d411/safeareacontext/States.cpp.o	c5ec1bd3afe18196
16	3156	7802456516912013	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	8919652f4ec6710f
67	3278	7802456518023061	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	a70195241f0f8dd5
123	3458	7802456520015105	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51215515fe21556a3837eeba2b49d411/safeareacontext/EventEmitters.cpp.o	5bcf42501a77a7c5
30	3633	7802456521716785	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/29c6ac0a1dd6ebb9204efeee5b088e58/rnasyncstorageJSI-generated.cpp.o	89b46215c03c7a68
35	3642	7802456521676791	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	2cdee6cfcb638b90
131	3670	7802456522142268	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2e4509cb0d0a1d5cc7411f483425a060/safeareacontextJSI-generated.cpp.o	46127eeff90b2f2f
62	3715	7802456522307429	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	70e80ba4946930a
25	3757	7802456522492612	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	71ee04fdc379f3b8
79	3779	7802456522422602	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	efde635ead5f8bcc
73	3787	7802456522862895	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	3133c2f38a94d64e
152	3798	7802456523113220	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSBottomTabsState.cpp.o	3d1579506be6adb9
145	3874	7802456524204203	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/34e83742130ce8fa5cb0bd5b71d73d82/jni/safeareacontext-generated.cpp.o	5c8e3c83514a7c78
189	3933	7802456524794857	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	ef40cca9e2ec07f8
20	4020	7802456525690693	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	55973994a41e5851
56	4059	7802456526071017	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	f3d5f1d99afdd8e7
91	4293	7802456528343654	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/002469d5b84800fdd549c5e175ca642e/RNCSafeAreaViewState.cpp.o	546355a53b797bc1
46	4318	7802456528528816	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	b89edf7d9f1b5a1b
12	4346	7802456528779149	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	e7bc6fae849ae0f4
111	4497	7802456530405738	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51215515fe21556a3837eeba2b49d411/safeareacontext/ShadowNodes.cpp.o	99c9a99c90a56967
7	4505	7802456530440931	CMakeFiles/appmodules.dir/OnLoad.cpp.o	6e81f36e7ce81cfb
137	4781	7802456533048692	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	9bf2af9d554eab9
174	4806	7802456533424249	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	5a835b2b4990199c
104	4814	7802456533329032	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd470dadb21de9e1a8a9b540771142e1/components/safeareacontext/Props.cpp.o	8842f0a87a7c6a7b
166	4864	7802456534164898	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSModalScreenShadowNode.cpp.o	a145057690deb4ff
181	4915	7802456534680383	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e64928fbfda1b3f277e4ea6d26deec00/RNSScreenStackHeaderConfigShadowNode.cpp.o	a76121cabecac140
198	4949	7802456534970700	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSScreenShadowNode.cpp.o	395bda36d2acae07
159	4951	7802456535020699	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e64928fbfda1b3f277e4ea6d26deec00/RNSScreenStackHeaderSubviewShadowNode.cpp.o	b5c91db4c26c1f1
97	5437	7802456539817534	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2e4509cb0d0a1d5cc7411f483425a060/ComponentDescriptors.cpp.o	135c1229d7f86d1a
84	5504	7802456540498193	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/002469d5b84800fdd549c5e175ca642e/RNCSafeAreaViewShadowNode.cpp.o	aa2575af172f20df
5504	5632	7802456541801070	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/arm64-v8a/libreact_codegen_safeareacontext.so	45548ac5e47901bd
3642	6117	7802456546700010	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a328c61c957b5da5800f94032115c8ec/react/renderer/components/rnscreens/States.cpp.o	5d502473ad9f43e
3757	6179	7802456547380789	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o	971d6b016106ae21
2863	6205	7802456547611155	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	d8ea3b23678c8dc9
2842	6251	7802456548076486	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/19c14822b9fd6da13ca8af6ae442fcdf/renderer/components/rnscreens/RNSScreenState.cpp.o	4bb23e9b318e2f53
4059	6299	7802456548571841	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/States.cpp.o	5111a99f7a895a8d
3459	6373	7802456549281977	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/79a671fe4e2ad3a12207216f48eb05ff/components/rnscreens/rnscreensJSI-generated.cpp.o	7ed56ca9a7edd435
3788	6378	7802456549372012	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o	25f4f60fe4ca4380
4318	6666	7802456552274279	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/EventEmitters.cpp.o	f38b91e54b7606e3
3716	6674	7802456552294281	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o	7d8d4a1c64f4ec93
3799	6729	7802456552869722	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o	df31ab8f42598ad6
206	6780	7802456553089988	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/79a671fe4e2ad3a12207216f48eb05ff/components/rnscreens/ComponentDescriptors.cpp.o	d235dcf3b6273ae5
3670	6811	7802456553735460	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o	dfc6c5d8b33022b7
4021	6849	7802456554052510	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/f62a571eb6863fc2b74961b257b7d8df/RNWorkletsSpecJSI-generated.cpp.o	8f47a6f26e542c69
4294	6929	7802456554868132	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/RNWorkletsSpec-generated.cpp.o	7e60a18d6ccbe34b
2954	6970	7802456555303565	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	8300b80f5421cb89
3933	6985	7802456555478687	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/ShadowNodes.cpp.o	2b489c98077969c1
3779	7064	7802456556254307	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o	ac9fefa0564f943b
4506	7126	7802456556889800	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/Props.cpp.o	ae9d1a88ad391a50
4346	7140	7802456556934928	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o	db58d6a9e354db23
3633	7181	7802456557420237	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f60e6a02b5031c13be66380d8fa3a5a7/renderer/components/rnscreens/ShadowNodes.cpp.o	e94a286f8067533
3157	7200	7802456557625405	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	d0bcb5a7ca657993
4497	7408	7802456559687473	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/ComponentDescriptors.cpp.o	58fb0d6edbba044d
3875	8126	7802456566697729	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a328c61c957b5da5800f94032115c8ec/react/renderer/components/rnscreens/Props.cpp.o	91f22d81533850fb
3	8260	7802456567863950	CMakeFiles/appmodules.dir/6875011670f4259eebb9dffd6fd545ff/raskel-live-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	1e9e860a1f9f038
3279	8460	7802456569953002	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f60e6a02b5031c13be66380d8fa3a5a7/renderer/components/rnscreens/EventEmitters.cpp.o	f94d601c1dbc298
8460	8550	7802456570999367	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/arm64-v8a/libreact_codegen_rnscreens.so	7b01cf16abf72c0b
8551	8659	7802456572105224	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/arm64-v8a/libappmodules.so	5306de4acc5fe6d
