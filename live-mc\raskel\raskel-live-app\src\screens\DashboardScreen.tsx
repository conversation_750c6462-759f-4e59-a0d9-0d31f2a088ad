import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
  SafeAreaView,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation';
import { useAuth } from '../contexts/AuthContext';
import { streamService } from '../services/api';
import { canUseNativeModules, getUnsupportedEnvironmentMessage } from '../utils/environment';
import * as Location from 'expo-location';
import { fetchWeather } from '../services/weatherService';

type DashboardNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Dashboard'>;

const { width: screenWidth } = Dimensions.get('window');

interface Stream {
  id: string;
  title: string;
  description: string;
  host_name: string;
  viewer_count: number;
  is_live: boolean;
  thumbnail_url?: string;
}

interface WeatherData {
  name: string;
  main: {
    temp: number;
    feels_like: number;
    temp_min: number;
    temp_max: number;
    pressure: number;
    humidity: number;
  };
  weather: {
    id: number;
    main: string;
    description: string;
    icon: string;
  }[];
  wind: {
    speed: number;
    deg: number;
  };
}

interface WeatherData {
  name: string;
  main: {
    temp: number;
    feels_like: number;
    temp_min: number;
    temp_max: number;
    pressure: number;
    humidity: number;
  };
  weather: {
    id: number;
    main: string;
    description: string;
    icon: string;
  }[];
  wind: {
    speed: number;
    deg: number;
  };
}

const DashboardScreen: React.FC = () => {
  const navigation = useNavigation<DashboardNavigationProp>();
  const { user, signOut } = useAuth();
  const [streams, setStreams] = useState<Stream[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);

  const fetchStreams = async () => {
    try {
      setLoading(true);
      const response = await streamService.getLiveStreams();
      
      if (response.data && response.data.length > 0) {
        setStreams(response.data);
      } else {
        // Mock data for development
        const mockStreams: Stream[] = [
          {
            id: 'demo-1',
            title: 'Kitchen Backsplash Installation Tips',
            description: 'Live renovation tips and tricks',
            host_name: "Mike's Tile Co.",
            viewer_count: 128,
            is_live: true,
          },
          {
            id: 'demo-2',
            title: 'DIY Deck Staining: Pro Techniques',
            description: 'Professional staining techniques',
            host_name: "Lisa's Exteriors",
            viewer_count: 85,
            is_live: true,
          },
        ];
        setStreams(mockStreams);
      }
    } catch (error) {
      console.error('Error fetching streams:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchWeatherData = async () => {
    try {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.error('Permission to access location was denied for weather data');
        return;
      }

      let location = await Location.getCurrentPositionAsync({});
      const data = await fetchWeather(location.coords.latitude, location.coords.longitude);
      setWeatherData(data);
    } catch (error) {
      console.error('Error fetching weather data:', error);
    }
  };

  useEffect(() => {
    fetchStreams();
    fetchWeatherData();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    fetchStreams();
    fetchWeatherData();
  };

  const handleNavigateToStream = (stream: Stream) => {
    console.log('🎯 Dashboard: handleNavigateToStream called for stream:', stream.id, stream.title);
    
    // CRITICAL: Check environment before navigating to prevent Expo Go crashes
    if (!canUseNativeModules()) {
      console.log('❌ Dashboard: Environment check failed - showing environment error');
      Alert.alert(
        'Live Streaming Requires Development Build 🚫',
        'You\'re currently using Expo Go, which doesn\'t support live streaming.\n\n✅ TO FIX THIS:\n\n1. Exit Expo Go completely\n2. Run in terminal: npx expo run:android\n3. Use the development build instead\n\nThe development build looks the same but includes live streaming support!',
        [
          { text: 'OK', style: 'default' },
          { 
            text: 'More Help', 
            onPress: () => Alert.alert(
              'Quick Setup Guide',
              'STEPS TO GET LIVE STREAMING:\n\n1. Close Expo Go app\n2. Open terminal/command prompt\n3. Navigate to your project folder\n4. Run: npx expo run:android\n5. Wait for build to complete\n6. Use the new "Raskel Live" app\n\nNote: You only need to do this once!',
              [{ text: 'Got it!' }]
            )
          }
        ]
      );
      return;
    }

    console.log('✅ Dashboard: Environment check passed');
    
    // ⚠️ CRITICAL: Check if this is a mock stream (starts with 'demo-' or common mock patterns)
    if (stream.id.startsWith('demo-') || stream.id.startsWith('stream-') || stream.id.includes('mock')) {
      console.log('⚠️ Dashboard: Detected mock stream - showing warning dialog');
      Alert.alert(
        'Demo Stream - No Host Available 🎥',
        `This is a demo stream card. No real host is broadcasting on "${stream.title}".\n\n💡 TO JOIN REAL STREAMS:\n1. Use "Live Now" to create your own live stream\n2. Have friends join your stream instead\n\n⚠️ Demo streams will show "connecting" forever because no host exists.`,
        [
          { 
            text: 'Try Anyway', 
            onPress: () => {
              console.log('🚨 Dashboard: User chose "Try Anyway" - navigating to mock stream');
              navigateToMockStream(stream);
            }, 
            style: 'destructive' 
          },
          { 
            text: 'Go to Live Now', 
            onPress: () => {
              console.log('🎯 Dashboard: User chose "Go to Live Now"');
              navigation.navigate('CreateStream');
            }
          },
          { 
            text: 'Cancel', 
            onPress: () => console.log('❌ Dashboard: User cancelled mock stream dialog'),
            style: 'cancel' 
          }
        ]
      );
      return;
    }

    console.log('🎯 Dashboard: Real stream detected - navigating directly');
    // Safe to navigate - real stream with actual host
    navigateToRealStream(stream);
  };
  
  const navigateToMockStream = (stream: Stream) => {
    console.log('🚨 Dashboard: navigateToMockStream - WARNING: This will likely cause Agora errors!');
    console.log('🚨 Navigation params:', {
      streamId: stream.id,
      channelName: `channel_${stream.id}`,
      hostUid: 'demo-host-uid',
      streamTitle: `[DEMO] ${stream.title}`,
    });
    
    // Navigate to mock stream (will likely show loading forever)
    navigation.navigate('ViewStream', {
      streamId: stream.id,
      channelName: `channel_${stream.id}`,
      hostUid: 'demo-host-uid',
      streamTitle: `[DEMO] ${stream.title}`,
    });
  };
  
  const navigateToRealStream = (stream: Stream) => {
    console.log('✅ Dashboard: navigateToRealStream - Real stream navigation');
    console.log('✅ Navigation params:', {
      streamId: stream.id,
      channelName: `channel_${stream.id}`,
      hostUid: stream.host_name,
      streamTitle: stream.title,
    });
    
    // Navigate to real stream with proper parameters
    navigation.navigate('ViewStream', {
      streamId: stream.id,
      channelName: `channel_${stream.id}`,
      hostUid: stream.host_name, // Use actual host ID
      streamTitle: stream.title,
    });
  };

  const formatViewerCount = (count: number) => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logo}>
          <Text style={styles.logoIcon}>✅</Text>
          <Text style={styles.logoText}>TICK</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.headerIcon}
            onPress={() => {
              console.log('🔔 Dashboard: Navigating to Notifications');
              navigation.navigate('Notifications');
            }}
          >
            <Text style={styles.iconText}>🔔</Text>
            <View style={styles.notificationBadge}>
              <Text style={styles.badgeText}>3</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.profilePic}
            onPress={() => navigation.navigate('Profile')}
          >
            <Text style={styles.profileText}>
              {(user?.email || 'U')[0].toUpperCase()}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.mainContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Hi, {user?.email?.split('@')[0] || 'User'}!</Text>
          <Text style={styles.welcomeSubtitle}>What are you looking to improve today?</Text>
        </View>

        {/* Weather Section */}
        {weatherData && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current Weather in {weatherData.name}</Text>
            <View style={styles.weatherCard}>
              <Text style={styles.weatherTemp}>{Math.round(weatherData.main.temp)}°C</Text>
              <Text style={styles.weatherDescription}>{weatherData.weather[0].description}</Text>
              <Text style={styles.weatherDetails}>Feels like: {Math.round(weatherData.main.feels_like)}°C</Text>
              <Text style={styles.weatherDetails}>Humidity: {weatherData.main.humidity}%</Text>
              <Text style={styles.weatherDetails}>Wind: {weatherData.wind.speed} m/s</Text>
            </View>
          </View>
        )}

        {/* Weather Section */}
        {weatherData && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current Weather in {weatherData.name}</Text>
            <View style={styles.weatherCard}>
              <Text style={styles.weatherTemp}>{Math.round(weatherData.main.temp)}°C</Text>
              <Text style={styles.weatherDescription}>{weatherData.weather[0].description}</Text>
              <Text style={styles.weatherDetails}>Feels like: {Math.round(weatherData.main.feels_like)}°C</Text>
              <Text style={styles.weatherDetails}>Humidity: {weatherData.main.humidity}%</n>              <Text style={styles.weatherDetails}>Wind: {weatherData.wind.speed} m/s</Text>
            </View>
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity 
            style={styles.actionCard}
            onPress={() => navigation.navigate('NewProject')}
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>➕</Text>
            </View>
            <Text style={styles.actionTitle}>New Project</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionCard}
            onPress={() => navigation.navigate('FindPros')}
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>🔧</Text>
            </View>
            <Text style={styles.actionTitle}>Find Pros</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionCard}
            onPress={() => navigation.navigate('MainFeed')}
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>📱</Text>
            </View>
            <Text style={styles.actionTitle}>Main Feed</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionCard}
            onPress={() => navigation.navigate('CreateStream')}
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>📹</Text>
            </View>
            <Text style={styles.actionTitle}>Live Now</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => navigation.navigate('FishIdentifier')} // Changed from RaskelScan
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>🐠</Text>
            </View>
            <Text style={styles.actionTitle}>Fish Scan</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => navigation.navigate('FishingMap')} // New button for FishingMapScreen
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>🗺️</Text>
            </View>
            <Text style={styles.actionTitle}>Fishing Map</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => {
              console.log('🎓 Dashboard: Navigating to Construction Creator Academy');
              navigation.navigate('ConstructionCreatorAcademy');
            }}
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>🎓</Text>
            </View>
            <Text style={styles.actionTitle}>Creator Academy</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => {
              console.log('🛒 Dashboard: Navigating to Shop Tools');
              navigation.navigate('ShopTools');
            }}
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>🛒</Text>
            </View>
            <Text style={styles.actionTitle}>Shop Tools</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => {
              console.log('🚁 Dashboard: Navigating to DJI Drone Access');
              navigation.navigate('DJIDroneAccess');
            }}
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>🚁</Text>
            </View>
            <Text style={styles.actionTitle}>DJI Drones</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => {
              console.log('📹 Dashboard: Navigating to Video Management');
              navigation.navigate('VideoManagement');
            }}
          >
            <View style={styles.actionIcon}>
              <Text style={styles.actionIconText}>📹</Text>
            </View>
            <Text style={styles.actionTitle}>Video Hub</Text>
          </TouchableOpacity>
        </View>

        {/* Live Streams Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Live Streams</Text>
            <TouchableOpacity onPress={() => navigation.navigate('BrowseStreams', {})}>
              <Text style={styles.seeAll}>See All</Text>
            </TouchableOpacity>
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.streamCards}>
            {streams.map((stream) => (
              <TouchableOpacity
                key={stream.id}
                style={styles.streamCard}
                onPress={() => handleNavigateToStream(stream)}
                activeOpacity={0.8}
              >
                <View style={styles.streamThumbnail}>
                  {/* Enhanced placeholder with gradient */}
                  <View style={styles.thumbnailGradient}>
                    <Text style={styles.thumbnailEmoji}>📹</Text>
                  </View>
                  
                  <View style={styles.liveBadge}>
                    <View style={styles.liveDot} />
                    <Text style={styles.liveText}>LIVE</Text>
                  </View>
                  
                  <View style={styles.streamViewers}>
                    <Text style={styles.viewersIcon}>👁</Text>
                    <Text style={styles.viewersText}>{formatViewerCount(stream.viewer_count)}</Text>
                  </View>
                  
                  {/* Duration badge */}
                  <View style={styles.durationBadge}>
                    <Text style={styles.durationText}>1:23:45</Text>
                  </View>
                </View>
                <View style={styles.streamInfo}>
                  <Text style={styles.streamTitle} numberOfLines={2}>
                    {stream.title}
                  </Text>
                  <View style={styles.streamHost}>
                    <View style={styles.hostAvatar}>
                      <Text style={styles.hostAvatarText}>
                        {(stream.host_name || 'H')[0].toUpperCase()}
                      </Text>
                    </View>
                    <View style={styles.hostInfo}>
                      <Text style={styles.hostName}>{stream.host_name}</Text>
                      <Text style={styles.streamCategory}>Construction</Text>
                    </View>
                  </View>
                  {/* Tags */}
                  <View style={styles.streamTags}>
                    <View style={styles.streamTag}>
                      <Text style={styles.tagText}>live</Text>
                    </View>
                    <View style={styles.streamTag}>
                      <Text style={styles.tagText}>tutorial</Text>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity style={[styles.navItem, styles.activeNavItem]}>
          <Text style={styles.navIcon}>🏠</Text>
          <Text style={[styles.navText, styles.activeNavText]}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.navItem}
          onPress={() => navigation.navigate('Explore')}
        >
          <Text style={styles.navIcon}>🧭</Text>
          <Text style={styles.navText}>Explore</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.navItem}
          onPress={() => navigation.navigate('BrowseStreams', {})}
        >
          <Text style={styles.navIcon}>📹</Text>
          <Text style={styles.navText}>Live</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.navItem}
          onPress={() => navigation.navigate('Projects')}
        >
          <Text style={styles.navIcon}>📋</Text>
          <Text style={styles.navText}>Projects</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.navItem}
          onPress={() => navigation.navigate('Profile')}
        >
          <Text style={styles.navIcon}>👤</Text>
          <Text style={styles.navText}>Profile</Text>
        </TouchableOpacity>
      </View>

      {/* AI Assistant FAB */}
      <TouchableOpacity 
        style={styles.aiFab}
        onPress={() => navigation.navigate('AIAssistant')}
      >
        <Text style={styles.fabIcon}>🤖</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  
  // Header
  header: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F7F9FC',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  
  logo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  
  logoText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#2E7DFF',
  },
  
  logoIcon: {
    color: '#FF6B2E',
    fontSize: 28,
  },
  
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  
  iconText: {
    fontSize: 18,
  },
  
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#FF6B2E',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  badgeText: {
    fontSize: 10,
    color: 'white',
    fontWeight: '600',
  },
  
  profilePic: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2E7DFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 3,
  },
  
  profileText: {
    fontSize: 18,
    color: 'white',
    fontWeight: '600',
  },
  
  // Main Content
  mainContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  
  welcomeSection: {
    paddingVertical: 20,
  },
  
  welcomeTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A1A2E',
    marginBottom: 5,
  },
  
  welcomeSubtitle: {
    fontSize: 14,
    color: '#8A94A6',
  },
  
  // Quick Actions - Updated for 8 cards layout (4x2 grid)
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 30,
    justifyContent: 'space-between',
  },

  actionCard: {
    width: (screenWidth - 90) / 4, // 4 cards per row
    minWidth: 75,
    maxWidth: 95,
    padding: 6,
    borderRadius: 14,
    backgroundColor: 'white',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
  },
  
  actionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(46, 125, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  
  actionIconText: {
    fontSize: 20,
  },
  
  actionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A1A2E',
  },
  
  // Sections
  section: {
    marginBottom: 30,
  },
  
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A2E',
  },
  
  seeAll: {
    fontSize: 14,
    color: '#2E7DFF',
    fontWeight: '500',
  },
  
  // Stream Cards
  streamCards: {
    paddingBottom: 10,
  },
  
  streamCard: {
    width: 200,
    borderRadius: 16,
    backgroundColor: 'white',
    marginRight: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  
  streamThumbnail: {
    width: '100%',
    height: 120,
    backgroundColor: '#E0E0E0',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    position: 'relative',
    overflow: 'hidden',
  },
  
  thumbnailGradient: {
    flex: 1,
    backgroundColor: '#667eea', // Fallback for React Native
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  thumbnailEmoji: {
    fontSize: 32,
    opacity: 0.8,
  },
  
  liveBadge: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: '#E74C3C',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    gap: 5,
  },
  
  liveDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'white',
  },
  
  liveText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  
  streamViewers: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    gap: 5,
  },
  
  viewersIcon: {
    fontSize: 12,
  },
  
  viewersText: {
    fontSize: 12,
    color: 'white',
  },
  
  durationBadge: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  
  durationText: {
    fontSize: 10,
    color: 'white',
    fontWeight: '500',
  },
  
  streamInfo: {
    padding: 15,
  },
  
  streamTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A1A2E',
    marginBottom: 8,
    height: 40,
  },
  
  streamHost: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  
  hostInfo: {
    flex: 1,
  },
  
  streamCategory: {
    fontSize: 11,
    color: '#8A94A6',
    textTransform: 'capitalize',
  },
  
  streamTags: {
    flexDirection: 'row',
    gap: 6,
  },
  
  streamTag: {
    backgroundColor: 'rgba(46, 125, 255, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  
  tagText: {
    fontSize: 10,
    color: '#2E7DFF',
    fontWeight: '500',
  },
  
  hostAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#2E7DFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  hostAvatarText: {
    fontSize: 10,
    color: 'white',
    fontWeight: '600',
  },
  
  hostName: {
    fontSize: 12,
    color: '#8A94A6',
  },
  
  // Bottom Navigation
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  
  navItem: {
    alignItems: 'center',
    gap: 5,
    paddingVertical: 5,
    width: 60,
  },
  
  activeNavItem: {
    // Active state styling
  },
  
  navIcon: {
    fontSize: 20,
  },
  
  navText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8A94A6',
  },
  
  activeNavText: {
    color: '#2E7DFF',
  },
  
  // AI Assistant FAB
  aiFab: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FF6B2E',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FF6B2E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  
  fabIcon: {
    fontSize: 24,
  },
});

export default DashboardScreen;