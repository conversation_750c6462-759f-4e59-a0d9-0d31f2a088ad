/**
 * Feature Flags Configuration for Fish Kaster App
 * Safely enable/disable features during development and production
 */

export interface FeatureFlags {
  // Core Fishing Features
  fishIdentification: boolean;
  weatherDashboard: boolean;
  tidesFeature: boolean;
  solunarTimes: boolean;
  locationFinder: boolean;
  
  // Social & Premium Features
  socialFeed: boolean;
  proFeatures: boolean;
  vipFeatures: boolean;
  
  // Authentication Features
  googleAuth: boolean;
  socialLogin: boolean;
  
  // Streaming Features
  liveStreaming: boolean;
  agoraIntegration: boolean;
  
  // AI Features
  aiPhotoAnalysis: boolean;
  speciesRecognition: boolean;
  
  // Development Features
  mockData: boolean;
  debugMode: boolean;
}

/**
 * Default feature flags configuration
 * These can be overridden by environment variables
 */
const defaultFlags: FeatureFlags = {
  // Core Fishing Features - Enable gradually
  fishIdentification: false, // Start disabled for safety
  weatherDashboard: true,
  tidesFeature: true,
  solunarTimes: true,
  locationFinder: true,
  
  // Social & Premium Features - Disable until ready
  socialFeed: false,
  proFeatures: false,
  vipFeatures: false,
  
  // Authentication Features - Keep existing working
  googleAuth: false, // Disabled until OA<PERSON> is configured
  socialLogin: false,
  
  // Streaming Features - Keep existing working
  liveStreaming: true,
  agoraIntegration: true,
  
  // AI Features - Disable until models are ready
  aiPhotoAnalysis: false,
  speciesRecognition: false,
  
  // Development Features
  mockData: __DEV__, // Only in development
  debugMode: __DEV__,
};

/**
 * Environment variable mapping for feature flags
 */
const envFlagMapping: Record<keyof FeatureFlags, string> = {
  fishIdentification: 'ENABLE_FISH_IDENTIFICATION',
  weatherDashboard: 'ENABLE_WEATHER_DASHBOARD',
  tidesFeature: 'ENABLE_TIDES_FEATURE',
  solunarTimes: 'ENABLE_SOLUNAR_TIMES',
  locationFinder: 'ENABLE_LOCATION_FINDER',
  socialFeed: 'ENABLE_SOCIAL_FEED',
  proFeatures: 'ENABLE_PRO_FEATURES',
  vipFeatures: 'ENABLE_VIP_FEATURES',
  googleAuth: 'ENABLE_GOOGLE_AUTH',
  socialLogin: 'ENABLE_SOCIAL_LOGIN',
  liveStreaming: 'ENABLE_LIVE_STREAMING',
  agoraIntegration: 'ENABLE_AGORA_INTEGRATION',
  aiPhotoAnalysis: 'ENABLE_AI_PHOTO_ANALYSIS',
  speciesRecognition: 'ENABLE_SPECIES_RECOGNITION',
  mockData: 'ENABLE_MOCK_DATA',
  debugMode: 'ENABLE_DEBUG_MODE',
};

/**
 * Parse boolean from environment variable
 */
function parseBooleanEnv(value: string | undefined, defaultValue: boolean): boolean {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true' || value === '1';
}

/**
 * Load feature flags from environment variables
 * Falls back to default values if not set
 */
function loadFeatureFlags(): FeatureFlags {
  const flags: FeatureFlags = { ...defaultFlags };
  
  // Override with environment variables if available
  Object.entries(envFlagMapping).forEach(([flagKey, envKey]) => {
    const envValue = process.env[envKey];
    if (envValue !== undefined) {
      (flags as any)[flagKey] = parseBooleanEnv(envValue, (defaultFlags as any)[flagKey]);
    }
  });
  
  return flags;
}

/**
 * Global feature flags instance
 */
export const featureFlags: FeatureFlags = loadFeatureFlags();

/**
 * Helper function to check if a feature is enabled
 */
export function isFeatureEnabled(feature: keyof FeatureFlags): boolean {
  return featureFlags[feature];
}

/**
 * Helper function to enable/disable features at runtime (for testing)
 */
export function setFeatureFlag(feature: keyof FeatureFlags, enabled: boolean): void {
  if (__DEV__) {
    (featureFlags as any)[feature] = enabled;
    console.log(`🚩 Feature flag '${feature}' set to: ${enabled}`);
  } else {
    console.warn('Feature flags can only be modified in development mode');
  }
}

/**
 * Get all current feature flag states (for debugging)
 */
export function getFeatureFlagStatus(): FeatureFlags {
  return { ...featureFlags };
}

/**
 * Log current feature flag status (for debugging)
 */
export function logFeatureFlags(): void {
  if (__DEV__) {
    console.log('🚩 Current Feature Flags:', featureFlags);
  }
}
