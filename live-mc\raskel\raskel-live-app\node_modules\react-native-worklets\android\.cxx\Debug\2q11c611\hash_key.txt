# Values used to calculate the hash in this folder name.
# Should not depend on the absolute path of the project itself.
#   - AGP: 8.11.0.
#   - $NDK is the path to NDK 27.1.12297006.
#   - $PROJECT is the path to the parent folder of the root Gradle build file.
#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.
#   - $HASH is the hash value computed from this text.
#   - $CMAKE is the path to CMake 3.22.1.
#   - $NINJA is the path to Ninja.
-HC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=$ABI
-DCMAKE_ANDROID_ARCH_ABI=$ABI
-DANDROID_NDK=$NDK
-DCMAKE_ANDROID_NDK=$NDK
-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=$NINJA
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/$HASH/prefab/$ABI/prefab
-BC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/$HASH/$ABI
-GNinja
-DANDROID_STL=c++_shared
-DREACT_NATIVE_MINOR_VERSION=81
-DANDROID_TOOLCHAIN=clang
-DREACT_NATIVE_DIR=C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native
-DJS_RUNTIME=hermes
-DIS_REANIMATED_EXAMPLE_APP=false
-DWORKLETS_BUNDLE_MODE=false
-DWORKLETS_VERSION=0.5.1
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
-DWORKLETS_FEATURE_FLAGS=[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]
-DHERMES_ENABLE_DEBUGGER=1