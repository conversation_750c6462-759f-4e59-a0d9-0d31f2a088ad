# Design Document

## Overview

The Raskel Live app requires proper integration of the `react-native-agora` native module within an Expo development environment. The current implementation fails because Expo Go cannot support custom native modules, requiring a transition to Expo Development Client (Custom Development Build) approach. This design outlines the necessary configuration changes, build process modifications, and deployment strategy to resolve the Agora integration issues.

## Architecture

### Current Architecture Issues
- App is configured for standard Expo Go which doesn't support native modules
- Missing Expo plugins configuration for react-native-agora
- Development build process not properly configured
- Native module linking failing at runtime

### Target Architecture
```mermaid
graph TB
    A[Expo Development Client] --> B[Custom Native Runtime]
    B --> C[React Native Agora SDK]
    C --> D[Agora Token Server]
    D --> E[Agora Cloud Services]
    
    F[Development Build Process] --> G[Android/iOS Native Build]
    G --> H[Bundled Native Modules]
    H --> I[Deployed to Emulator/Device]
    
    J[App Configuration] --> K[expo-dev-client Plugin]
    K --> L[Native Permissions]
    L --> M[Build Properties]
```

### Key Components
1. **Expo Development Client**: Custom runtime that includes native modules
2. **Native Module Integration**: Proper linking of react-native-agora
3. **Build Configuration**: Updated app.json/app.config.js with required plugins
4. **Permission Management**: Camera and microphone permissions for streaming
5. **Development Workflow**: Modified build and deployment process

## Components and Interfaces

### 1. Expo Configuration Updates

**File**: `app.json` or `app.config.js`
- Add `expo-dev-client` plugin for custom development builds
- Configure native permissions for Android and iOS
- Set up build properties for proper SDK versions
- Add Agora-specific configuration if needed

**Required Plugins**:
```json
{
  "plugins": [
    "expo-dev-client",
    [
      "expo-build-properties",
      {
        "android": {
          "minSdkVersion": 24,
          "compileSdkVersion": 35,
          "targetSdkVersion": 35
        }
      }
    ]
  ]
}
```

### 2. Development Build Process

**Build Commands**:
- `npx expo run:android` - Creates and installs custom development build for Android
- `npx expo run:ios` - Creates and installs custom development build for iOS
- `npx expo install --fix` - Ensures all dependencies are compatible

**Build Workflow**:
1. Install dependencies with native modules
2. Generate development build with custom runtime
3. Install build on emulator/device
4. Connect to Metro bundler for hot reloading

### 3. Native Module Integration

**Agora SDK Integration**:
- Ensure `react-native-agora` is properly included in development build
- Configure native permissions in app.json
- Set up proper initialization in useAgoraSimple hook
- Handle runtime errors gracefully

**Permission Configuration**:
```json
{
  "android": {
    "permissions": [
      "android.permission.CAMERA",
      "android.permission.RECORD_AUDIO",
      "android.permission.MODIFY_AUDIO_SETTINGS",
      "android.permission.ACCESS_NETWORK_STATE",
      "android.permission.INTERNET"
    ]
  },
  "ios": {
    "infoPlist": {
      "NSCameraUsageDescription": "This app needs access to camera for live streaming",
      "NSMicrophoneUsageDescription": "This app needs access to microphone for live streaming"
    }
  }
}
```

### 4. Error Handling and Fallbacks

**Runtime Error Management**:
- Detect when running in Expo Go vs Development Client
- Provide clear error messages for unsupported environments
- Graceful degradation when native modules fail
- User-friendly guidance for switching to development client

**Development vs Production Builds**:
- Development builds include debugging capabilities
- Production builds (EAS Build) for app store deployment
- Clear separation of build configurations

## Data Models

### Build Configuration Model
```typescript
interface ExpoConfig {
  expo: {
    name: string;
    slug: string;
    plugins: Array<string | [string, object]>;
    android: {
      permissions: string[];
      package: string;
    };
    ios: {
      bundleIdentifier: string;
      infoPlist: Record<string, string>;
    };
  };
}
```

### Development Environment Model
```typescript
interface DevelopmentEnvironment {
  buildType: 'expo-go' | 'development-client' | 'standalone';
  nativeModulesSupported: boolean;
  agoraSDKAvailable: boolean;
  permissionsGranted: {
    camera: boolean;
    microphone: boolean;
  };
}
```

## Error Handling

### 1. Native Module Detection
- Check if running in compatible environment
- Detect Expo Go vs Development Client
- Provide clear error messages for unsupported configurations

### 2. Build Process Errors
- Handle compilation failures gracefully
- Provide troubleshooting steps for common build issues
- Clear error messages for missing dependencies

### 3. Runtime Error Recovery
- Graceful fallback when Agora SDK fails to initialize
- User-friendly error messages
- Guidance for resolving permission issues

### 4. Development Workflow Errors
- Clear instructions when wrong build type is used
- Automatic detection of environment mismatches
- Step-by-step recovery instructions

## Testing Strategy

### 1. Development Build Testing
- Verify development build creates successfully
- Test installation on Android emulator
- Confirm Agora SDK initializes without errors
- Validate all native permissions work correctly

### 2. Agora Integration Testing
- Test token server connection
- Verify video/audio streaming functionality
- Test channel joining and leaving
- Validate user presence and remote user handling

### 3. Cross-Platform Testing
- Test on Android emulator/device
- Test on iOS simulator/device (if available)
- Verify consistent behavior across platforms
- Test permission handling on both platforms

### 4. Error Scenario Testing
- Test behavior when running in Expo Go
- Test with missing permissions
- Test with network connectivity issues
- Test token server failures

### 5. Build Process Validation
- Verify clean builds work consistently
- Test dependency updates don't break builds
- Validate build caching works correctly
- Test on different development machines

## Implementation Phases

### Phase 1: Configuration Updates
- Update app.json with required plugins
- Add expo-dev-client dependency
- Configure native permissions
- Update build properties

### Phase 2: Development Build Setup
- Create initial development build
- Test installation on emulator
- Verify basic app functionality
- Confirm native module loading

### Phase 3: Agora Integration Validation
- Test Agora SDK initialization
- Verify token server connectivity
- Test basic streaming functionality
- Validate error handling

### Phase 4: Documentation and Workflow
- Document new development workflow
- Create troubleshooting guides
- Update deployment scripts
- Train team on new process