com.fishkaster.app:xml/rn_dev_preferences = 0x7f160005
com.fishkaster.app:xml/image_share_filepaths = 0x7f160003
com.fishkaster.app:xml/com_android_billingclient_phenotype = 0x7f160000
com.fishkaster.app:styleable/ViewStubCompat = 0x7f1500a9
com.fishkaster.app:styleable/ViewPager2 = 0x7f1500a8
com.fishkaster.app:styleable/Transition = 0x7f1500a4
com.fishkaster.app:styleable/Transform = 0x7f1500a3
com.fishkaster.app:styleable/TextAppearance = 0x7f15009d
com.fishkaster.app:styleable/TabLayout = 0x7f15009c
com.fishkaster.app:styleable/TabItem = 0x7f15009b
com.fishkaster.app:styleable/SwitchMaterial = 0x7f15009a
com.fishkaster.app:styleable/SwitchCompat = 0x7f150099
com.fishkaster.app:styleable/StyledPlayerView = 0x7f150097
com.fishkaster.app:styleable/StateSet = 0x7f150095
com.fishkaster.app:styleable/StateListDrawableItem = 0x7f150094
com.fishkaster.app:styleable/StateListDrawable = 0x7f150093
com.fishkaster.app:styleable/State = 0x7f150092
com.fishkaster.app:color/m3_ref_palette_neutral_variant60 = 0x7f060147
com.fishkaster.app:styleable/SearchView = 0x7f150088
com.fishkaster.app:styleable/ScrimInsetsFrameLayout = 0x7f150085
com.fishkaster.app:id/mtrl_calendar_months = 0x7f0a0179
com.fishkaster.app:styleable/SVGImageView = 0x7f150084
com.fishkaster.app:styleable/PreviewView = 0x7f15007e
com.fishkaster.app:style/TextAppearance.Material3.LabelLarge = 0x7f14021f
com.fishkaster.app:styleable/PopupWindowBackgroundState = 0x7f15007d
com.fishkaster.app:styleable/PlayerView = 0x7f15007b
com.fishkaster.app:styleable/PlayerControlView = 0x7f15007a
com.fishkaster.app:styleable/OnSwipe = 0x7f150079
com.fishkaster.app:drawable/exo_icon_play = 0x7f0800cb
com.fishkaster.app:drawable/ic_clear_black_24 = 0x7f080102
com.fishkaster.app:styleable/NavigationBarActiveIndicator = 0x7f150073
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f140178
com.fishkaster.app:styleable/NavGraphNavigator = 0x7f150070
com.fishkaster.app:attr/dividerInsetStart = 0x7f0401b1
com.fishkaster.app:styleable/NavDeepLink = 0x7f15006f
com.fishkaster.app:attr/actualImageResource = 0x7f040027
com.fishkaster.app:integer/google_play_services_version = 0x7f0b000a
com.fishkaster.app:styleable/NavAction = 0x7f15006d
com.fishkaster.app:dimen/exo_small_icon_height = 0x7f0700a6
com.fishkaster.app:styleable/MotionLayout = 0x7f15006a
com.fishkaster.app:styleable/Motion = 0x7f150068
com.fishkaster.app:styleable/MenuGroup = 0x7f150064
com.fishkaster.app:styleable/MaterialTimePicker = 0x7f150062
com.fishkaster.app:styleable/MaterialSwitch = 0x7f15005f
com.fishkaster.app:color/mtrl_fab_bg_color_selector = 0x7f0602f1
com.fishkaster.app:styleable/MaterialRadioButton = 0x7f15005d
com.fishkaster.app:styleable/MaterialDivider = 0x7f15005c
com.fishkaster.app:id/open_search_view_scrim = 0x7f0a01a8
com.fishkaster.app:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f14017f
com.fishkaster.app:styleable/MaterialCheckBoxStates = 0x7f15005b
com.fishkaster.app:id/transitionToEnd = 0x7f0a0248
com.fishkaster.app:styleable/MaterialButtonToggleGroup = 0x7f150056
com.fishkaster.app:id/open_search_view_root = 0x7f0a01a7
com.fishkaster.app:styleable/MaterialButton = 0x7f150055
com.fishkaster.app:styleable/MapAttrs = 0x7f150051
com.fishkaster.app:styleable/LoadingImageView = 0x7f150050
com.fishkaster.app:styleable/KeyPosition = 0x7f150048
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface = 0x7f0601db
com.fishkaster.app:styleable/KeyFramesVelocity = 0x7f150047
com.fishkaster.app:styleable/KeyFramesAcceleration = 0x7f150046
com.fishkaster.app:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070085
com.fishkaster.app:styleable/KeyFrame = 0x7f150045
com.fishkaster.app:styleable/KeyAttribute = 0x7f150043
com.fishkaster.app:id/material_clock_display_and_toggle = 0x7f0a0153
com.fishkaster.app:styleable/Insets = 0x7f150042
com.fishkaster.app:styleable/ForegroundLinearLayout = 0x7f15003b
com.fishkaster.app:styleable/FontFamilyFont = 0x7f15003a
com.fishkaster.app:styleable/FontFamily = 0x7f150039
com.fishkaster.app:string/combobox_description = 0x7f130052
com.fishkaster.app:styleable/FlowLayout = 0x7f150038
com.fishkaster.app:styleable/FloatingActionButton = 0x7f150036
com.fishkaster.app:drawable/abc_action_bar_item_background_material = 0x7f080029
com.fishkaster.app:styleable/DrawerLayout = 0x7f150033
com.fishkaster.app:styleable/DrawerArrowToggle = 0x7f150032
com.fishkaster.app:attr/onPositiveCross = 0x7f040391
com.fishkaster.app:styleable/DefaultTimeBar = 0x7f150031
com.fishkaster.app:styleable/ConstraintLayout_Layout = 0x7f15002a
com.fishkaster.app:attr/numericModifiers = 0x7f04038c
com.fishkaster.app:color/ripple_material_light = 0x7f060320
com.fishkaster.app:styleable/Constraint = 0x7f150029
com.fishkaster.app:styleable/ColorStateListItem = 0x7f150027
com.fishkaster.app:styleable/CollapsingToolbarLayout_Layout = 0x7f150026
com.fishkaster.app:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0e00ed
com.fishkaster.app:styleable/ClockFaceView = 0x7f150023
com.fishkaster.app:styleable/CheckedTextView = 0x7f15001f
com.fishkaster.app:styleable/Carousel = 0x7f15001e
com.fishkaster.app:styleable/BottomSheetBehavior_Layout = 0x7f15001a
com.fishkaster.app:styleable/BottomAppBar = 0x7f150018
com.fishkaster.app:styleable/Badge = 0x7f150016
com.fishkaster.app:attr/trackThickness = 0x7f040505
com.fishkaster.app:id/open_search_bar_text_view = 0x7f0a019f
com.fishkaster.app:styleable/AppBarLayout_Layout = 0x7f15000d
com.fishkaster.app:styleable/ActivityNavigator = 0x7f150006
com.fishkaster.app:styleable/ActivityChooserView = 0x7f150005
com.fishkaster.app:styleable/ActionBarLayout = 0x7f150001
com.fishkaster.app:styleable/ActionBar = 0x7f150000
com.fishkaster.app:style/custom = 0x7f1404af
com.fishkaster.app:anim/rns_no_animation_20 = 0x7f010042
com.fishkaster.app:styleable/LinearLayoutCompat = 0x7f15004c
com.fishkaster.app:style/amu_ClusterIcon.TextAppearance = 0x7f1404ae
com.fishkaster.app:style/amu_Bubble.TextAppearance.Light = 0x7f1404ad
com.fishkaster.app:style/amu_Bubble.TextAppearance.Dark = 0x7f1404ac
com.fishkaster.app:style/Widget.Support.CoordinatorLayout = 0x7f1404ab
com.fishkaster.app:styleable/CardView = 0x7f15001d
com.fishkaster.app:style/Widget.MaterialComponents.Tooltip = 0x7f1404aa
com.fishkaster.app:macro/m3_comp_assist_chip_container_shape = 0x7f0e0000
com.fishkaster.app:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f1404a9
com.fishkaster.app:attr/textAppearanceCaption = 0x7f040493
com.fishkaster.app:style/Widget.MaterialComponents.Toolbar = 0x7f1404a6
com.fishkaster.app:xml/image_picker_provider_paths = 0x7f160002
com.fishkaster.app:id/visible = 0x7f0a0263
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f1404a4
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f1404a3
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f1404a1
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f1404a0
com.fishkaster.app:attr/indeterminateAnimationType = 0x7f040270
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.Button = 0x7f14049d
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f080013
com.fishkaster.app:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f0701b9
com.fishkaster.app:string/exo_controls_overflow_hide_description = 0x7f130077
com.fishkaster.app:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f14049a
com.fishkaster.app:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f140499
com.fishkaster.app:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f070270
com.fishkaster.app:drawable/notification_oversize_large_icon_bg = 0x7f08015a
com.fishkaster.app:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f140498
com.fishkaster.app:id/ic_flip_24_horizontally = 0x7f0a012c
com.fishkaster.app:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f140497
com.fishkaster.app:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f140423
com.fishkaster.app:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f140491
com.fishkaster.app:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f14048e
com.fishkaster.app:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f14048d
com.fishkaster.app:style/Widget.MaterialComponents.ProgressIndicator = 0x7f140486
com.fishkaster.app:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f140485
com.fishkaster.app:dimen/design_bottom_navigation_item_min_width = 0x7f070069
com.fishkaster.app:style/Widget.MaterialComponents.PopupMenu = 0x7f140482
com.fishkaster.app:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f14047e
com.fishkaster.app:style/TextAppearance.MaterialComponents.Headline3 = 0x7f140231
com.fishkaster.app:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f14047d
com.fishkaster.app:dimen/m3_sys_elevation_level5 = 0x7f07021b
com.fishkaster.app:id/compatible = 0x7f0a008d
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f140476
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f140474
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f140473
com.fishkaster.app:color/m3_ref_palette_tertiary0 = 0x7f060167
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f140471
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f14046d
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f14046c
com.fishkaster.app:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0e0171
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f14046a
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f140469
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f140467
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f140464
com.fishkaster.app:style/Widget.MaterialComponents.FloatingActionButton = 0x7f14045f
com.fishkaster.app:dimen/m3_carousel_extra_small_item_size = 0x7f070114
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f07022a
com.fishkaster.app:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f14045e
com.fishkaster.app:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f140455
com.fishkaster.app:attr/roundTopStart = 0x7f0403ec
com.fishkaster.app:style/Widget.MaterialComponents.Chip.Entry = 0x7f140452
com.fishkaster.app:animator/m3_chip_state_list_anim = 0x7f02000e
com.fishkaster.app:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f140449
com.fishkaster.app:styleable/NavigationView = 0x7f150076
com.fishkaster.app:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f140444
com.fishkaster.app:style/CardView.Dark = 0x7f140125
com.fishkaster.app:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f140441
com.fishkaster.app:drawable/ic_launcher_background = 0x7f080108
com.fishkaster.app:style/Widget.MaterialComponents.BottomSheet = 0x7f140440
com.fishkaster.app:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f14043e
com.fishkaster.app:drawable/abc_item_background_holo_dark = 0x7f08004a
com.fishkaster.app:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0e00a1
com.fishkaster.app:style/Widget.MaterialComponents.Badge = 0x7f140439
com.fishkaster.app:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f140437
com.fishkaster.app:attr/layout_constraintLeft_creator = 0x7f0402cd
com.fishkaster.app:styleable/KeyTimeCycle = 0x7f150049
com.fishkaster.app:style/ExoStyledControls.Button.Center.RewWithAmount = 0x7f140144
com.fishkaster.app:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f140435
com.fishkaster.app:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f140434
com.fishkaster.app:color/cardview_light_background = 0x7f060030
com.fishkaster.app:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f140247
com.fishkaster.app:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f140433
com.fishkaster.app:color/m3_ref_palette_primary99 = 0x7f060159
com.fishkaster.app:styleable/AnimatedStateListDrawableTransition = 0x7f15000a
com.fishkaster.app:attr/chipMinTouchTargetSize = 0x7f0400d2
com.fishkaster.app:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f140430
com.fishkaster.app:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f14042f
com.fishkaster.app:integer/m3_sys_motion_duration_long3 = 0x7f0b0018
com.fishkaster.app:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f14042e
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f08001b
com.fishkaster.app:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f14042d
com.fishkaster.app:style/Widget.Material3.Toolbar.OnSurface = 0x7f14042a
com.fishkaster.app:styleable/AppCompatTheme = 0x7f150013
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f14015b
com.fishkaster.app:style/Widget.Material3.Toolbar = 0x7f140429
com.fishkaster.app:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f140428
com.fishkaster.app:attr/colorPrimaryContainer = 0x7f040119
com.fishkaster.app:dimen/m3_fab_corner_size = 0x7f0701dc
com.fishkaster.app:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f140421
com.fishkaster.app:drawable/exo_icon_shuffle_off = 0x7f0800d1
com.fishkaster.app:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f14041f
com.fishkaster.app:style/Widget.Material3.Snackbar.FullWidth = 0x7f140418
com.fishkaster.app:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0702bb
com.fishkaster.app:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0e004a
com.fishkaster.app:style/Widget.Material3.Snackbar = 0x7f140417
com.fishkaster.app:style/Widget.Material3.SideSheet.Detached = 0x7f140410
com.fishkaster.app:color/m3_highlighted_text = 0x7f0600b2
com.fishkaster.app:style/Widget.Material3.SideSheet = 0x7f14040f
com.fishkaster.app:style/Widget.Material3.SearchView.Prefix = 0x7f14040d
com.fishkaster.app:style/Widget.Material3.PopupMenu.Overflow = 0x7f140407
com.fishkaster.app:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f140406
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1403ff
com.fishkaster.app:dimen/mtrl_btn_padding_top = 0x7f07028e
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1403fe
com.fishkaster.app:style/Widget.MaterialComponents.NavigationView = 0x7f140481
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1403fd
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1403fb
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1403f9
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1403f4
com.fishkaster.app:style/Theme.Catalyst = 0x7f140256
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1403f3
com.fishkaster.app:attr/cropSaveBitmapToInstanceState = 0x7f04017f
com.fishkaster.app:anim/design_snackbar_in = 0x7f010020
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1403f0
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1403ed
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface = 0x7f0601b9
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1403e8
com.fishkaster.app:integer/mtrl_badge_max_character_count = 0x7f0b0030
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1403e5
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1403e4
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1403e1
com.fishkaster.app:color/design_default_color_surface = 0x7f06005d
com.fishkaster.app:color/m3_sys_color_light_primary = 0x7f060205
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1403e0
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Day = 0x7f1403df
com.fishkaster.app:style/Widget.Material3.MaterialCalendar = 0x7f1403de
com.fishkaster.app:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1403dd
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f14028b
com.fishkaster.app:style/Widget.Material3.LinearProgressIndicator = 0x7f1403db
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1403d9
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1403d7
com.fishkaster.app:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070223
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1403d2
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f080014
com.fishkaster.app:dimen/material_helper_text_font_1_3_padding_top = 0x7f070265
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1403cf
com.fishkaster.app:attr/switchPadding = 0x7f040467
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1403ce
com.fishkaster.app:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1403cd
com.fishkaster.app:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1403ca
com.fishkaster.app:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1403c9
com.fishkaster.app:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1403c6
com.fishkaster.app:attr/sideSheetModalStyle = 0x7f040429
com.fishkaster.app:style/Widget.Material3.DrawerLayout = 0x7f1403c5
com.fishkaster.app:styleable/MenuView = 0x7f150066
com.fishkaster.app:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.fishkaster.app:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1403bf
com.fishkaster.app:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f1403bd
com.fishkaster.app:attr/defaultMarginsEnabled = 0x7f0401a0
com.fishkaster.app:color/primary_material_dark = 0x7f060319
com.fishkaster.app:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f1403bb
com.fishkaster.app:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f1403ba
com.fishkaster.app:attr/textAppearanceLineHeightEnabled = 0x7f0404a4
com.fishkaster.app:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f1403b8
com.fishkaster.app:style/TextAppearance.Material3.DisplaySmall = 0x7f14021b
com.fishkaster.app:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f1403b7
com.fishkaster.app:style/Widget.Material3.Chip.Assist.Elevated = 0x7f1403ac
com.fishkaster.app:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f140197
com.fishkaster.app:style/Widget.Material3.CheckedTextView = 0x7f1403aa
com.fishkaster.app:style/Widget.Material3.CardView.Filled = 0x7f1403a8
com.fishkaster.app:attr/cropInitialCropWindowPaddingRatio = 0x7f040176
com.fishkaster.app:style/Widget.Material3.CardView.Elevated = 0x7f1403a7
com.fishkaster.app:style/Widget.Material3.Button.UnelevatedButton = 0x7f1403a6
com.fishkaster.app:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f14039d
com.fishkaster.app:attr/selectorSize = 0x7f040406
com.fishkaster.app:style/Widget.Material3.Button.IconButton.Outlined = 0x7f14039b
com.fishkaster.app:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f14039a
com.fishkaster.app:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f140396
com.fishkaster.app:style/Widget.Material3.Button = 0x7f140394
com.fishkaster.app:style/Widget.Material3.BottomSheet.DragHandle = 0x7f140392
com.fishkaster.app:styleable/AppCompatImageView = 0x7f15000f
com.fishkaster.app:id/material_textinput_timepicker = 0x7f0a015f
com.fishkaster.app:style/Widget.Material3.BottomNavigationView = 0x7f14038f
com.fishkaster.app:color/m3_sys_color_dynamic_dark_error_container = 0x7f0601a4
com.fishkaster.app:style/Widget.Material3.BottomAppBar = 0x7f14038b
com.fishkaster.app:style/Widget.Material3.Badge = 0x7f140389
com.fishkaster.app:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f140386
com.fishkaster.app:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f140385
com.fishkaster.app:dimen/mtrl_calendar_month_horizontal_padding = 0x7f0702af
com.fishkaster.app:id/exo_repeat_toggle = 0x7f0a00ee
com.fishkaster.app:style/Widget.Material3.AppBarLayout = 0x7f140384
com.fishkaster.app:style/Widget.Design.TextInputLayout = 0x7f140381
com.fishkaster.app:style/Widget.Design.Snackbar = 0x7f14037e
com.fishkaster.app:style/Widget.Design.CollapsingToolbar = 0x7f14037a
com.fishkaster.app:style/Widget.Design.AppBarLayout = 0x7f140377
com.fishkaster.app:id/alert_title = 0x7f0a0057
com.fishkaster.app:style/Widget.Autofill.InlineSuggestionTitle = 0x7f140374
com.fishkaster.app:style/Widget.Autofill.InlineSuggestionEndIconStyle = 0x7f140371
com.fishkaster.app:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f14036e
com.fishkaster.app:attr/ad_marker_width = 0x7f04002b
com.fishkaster.app:style/Base.V7.Widget.AppCompat.EditText = 0x7f1400c3
com.fishkaster.app:style/Widget.AppCompat.Toolbar = 0x7f14036d
com.fishkaster.app:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f14036c
com.fishkaster.app:style/Widget.AppCompat.TextView = 0x7f14036b
com.fishkaster.app:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f1402a6
com.fishkaster.app:style/Widget.AppCompat.Spinner.DropDown = 0x7f140368
com.fishkaster.app:bool/enable_system_job_service_default = 0x7f050004
com.fishkaster.app:style/Widget.AppCompat.Spinner = 0x7f140367
com.fishkaster.app:dimen/tooltip_corner_radius = 0x7f070341
com.fishkaster.app:style/Widget.AppCompat.SeekBar = 0x7f140365
com.fishkaster.app:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f14035f
com.fishkaster.app:style/Widget.AppCompat.ProgressBar = 0x7f14035e
com.fishkaster.app:attr/actionModeBackground = 0x7f040011
com.fishkaster.app:style/Widget.AppCompat.PopupWindow = 0x7f14035d
com.fishkaster.app:dimen/m3_btn_stroke_size = 0x7f070103
com.fishkaster.app:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f14035c
com.fishkaster.app:style/Widget.AppCompat.PopupMenu = 0x7f14035b
com.fishkaster.app:style/Widget.AppCompat.ListView.Menu = 0x7f14035a
com.fishkaster.app:style/Widget.AppCompat.ListView.DropDown = 0x7f140359
com.fishkaster.app:style/Widget.AppCompat.ListView = 0x7f140358
com.fishkaster.app:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f140350
com.fishkaster.app:layout/mtrl_alert_dialog = 0x7f0d0058
com.fishkaster.app:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f14034b
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f140348
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f140345
com.fishkaster.app:string/mtrl_picker_announce_current_selection = 0x7f1300f6
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f140344
com.fishkaster.app:style/Widget.AppCompat.ImageButton = 0x7f14033f
com.fishkaster.app:style/Widget.AppCompat.EditText = 0x7f14033e
com.fishkaster.app:attr/preserveIconSpacing = 0x7f0403c5
com.fishkaster.app:style/Widget.AppCompat.DrawerArrowToggle = 0x7f14033c
com.fishkaster.app:style/Widget.AppCompat.CompoundButton.Switch = 0x7f14033b
com.fishkaster.app:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f140339
com.fishkaster.app:style/Base.Widget.Material3.ActionBar.Solid = 0x7f140100
com.fishkaster.app:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f140334
com.fishkaster.app:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f140333
com.fishkaster.app:style/Widget.AppCompat.Button.Borderless = 0x7f140332
com.fishkaster.app:string/catalyst_perf_monitor_stop = 0x7f130044
com.fishkaster.app:style/Widget.AppCompat.ActivityChooserView = 0x7f14032f
com.fishkaster.app:color/material_slider_inactive_track_color = 0x7f0602d6
com.fishkaster.app:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f14032c
com.fishkaster.app:style/Widget.AppCompat.ActionBar.TabText = 0x7f140329
com.fishkaster.app:style/Widget.AppCompat.ActionBar.Solid = 0x7f140327
com.fishkaster.app:style/TranslucentDialog = 0x7f140325
com.fishkaster.app:attr/windowFixedWidthMajor = 0x7f040533
com.fishkaster.app:color/m3_switch_thumb_tint = 0x7f06017e
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f140324
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f14031e
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f140317
com.fishkaster.app:color/exo_white = 0x7f06007a
com.fishkaster.app:style/Widget.Design.BottomNavigationView = 0x7f140378
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f140316
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f14030c
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f14030a
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Dark = 0x7f140309
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f140302
com.fishkaster.app:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0701c9
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1402fe
com.fishkaster.app:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1402fb
com.fishkaster.app:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1402f7
com.fishkaster.app:style/ThemeOverlay.Material3.TabLayout = 0x7f1402f5
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0601e1
com.fishkaster.app:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1402f3
com.fishkaster.app:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1402ee
com.fishkaster.app:style/Widget.Material3.BottomAppBar.Legacy = 0x7f14038d
com.fishkaster.app:style/TextAppearance.Material3.BodyLarge = 0x7f140216
com.fishkaster.app:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1402ec
com.fishkaster.app:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1402ea
com.fishkaster.app:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1402e8
com.fishkaster.app:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1402e7
com.fishkaster.app:style/ThemeOverlay.Material3.Light = 0x7f1402e6
com.fishkaster.app:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f1403a3
com.fishkaster.app:id/tag_unhandled_key_listeners = 0x7f0a022a
com.fishkaster.app:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1402e5
com.fishkaster.app:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1402e4
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f140311
com.fishkaster.app:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1402e2
com.fishkaster.app:layout/crop_image_activity = 0x7f0d0023
com.fishkaster.app:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1402df
com.fishkaster.app:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0702e3
com.fishkaster.app:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1402de
com.fishkaster.app:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1402dd
com.fishkaster.app:attr/tintMode = 0x7f0404db
com.fishkaster.app:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1402d5
com.fishkaster.app:style/ShapeAppearance.Material3.Tooltip = 0x7f1401a4
com.fishkaster.app:style/ThemeOverlay.Material3.Dark = 0x7f1402d2
com.fishkaster.app:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f140495
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f140074
com.fishkaster.app:style/ThemeOverlay.Material3.Chip = 0x7f1402d0
com.fishkaster.app:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1402ce
com.fishkaster.app:dimen/compat_button_padding_horizontal_material = 0x7f07005b
com.fishkaster.app:drawable/common_google_signin_btn_text_dark = 0x7f080093
com.fishkaster.app:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1402cd
com.fishkaster.app:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1402cb
com.fishkaster.app:id/start = 0x7f0a0212
com.fishkaster.app:style/Theme.AppCompat.Light.DarkActionBar = 0x7f14024e
com.fishkaster.app:style/ThemeOverlay.Material3.Button = 0x7f1402c8
com.fishkaster.app:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1402c4
com.fishkaster.app:color/m3_sys_color_light_secondary = 0x7f060207
com.fishkaster.app:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1402c2
com.fishkaster.app:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0e00ec
com.fishkaster.app:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1402ba
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f07022c
com.fishkaster.app:style/ThemeOverlay.AppCompat.DayNight = 0x7f1402b7
com.fishkaster.app:style/Theme.ReactNative.TextInput.DefaultBackground = 0x7f1402b2
com.fishkaster.app:attr/cropBorderCornerThickness = 0x7f04016a
com.fishkaster.app:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f1402af
com.fishkaster.app:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f1402ad
com.fishkaster.app:attr/showMarker = 0x7f040418
com.fishkaster.app:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f07015d
com.fishkaster.app:style/Widget.Autofill = 0x7f14036f
com.fishkaster.app:attr/layout_constraintVertical_bias = 0x7f0402d9
com.fishkaster.app:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f1402ac
com.fishkaster.app:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f1402ab
com.fishkaster.app:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f1402a8
com.fishkaster.app:attr/motionPath = 0x7f040377
com.fishkaster.app:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f1402a7
com.fishkaster.app:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0e0065
com.fishkaster.app:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f1402a5
com.fishkaster.app:style/Theme.MaterialComponents.Light.Dialog = 0x7f1402a3
com.fishkaster.app:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f1402a1
com.fishkaster.app:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f14029b
com.fishkaster.app:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f140297
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f140033
com.fishkaster.app:attr/cornerSize = 0x7f040158
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f140291
com.fishkaster.app:color/m3_ref_palette_neutral87 = 0x7f060138
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f14028f
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f14028e
com.fishkaster.app:color/abc_secondary_text_material_dark = 0x7f060011
com.fishkaster.app:color/exo_black_opacity_70 = 0x7f060075
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f14028d
com.fishkaster.app:drawable/exo_ic_play_circle_filled = 0x7f0800bd
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f14028c
com.fishkaster.app:string/material_timepicker_select_time = 0x7f1300df
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f140288
com.fishkaster.app:style/Theme.MaterialComponents.DayNight = 0x7f140285
com.fishkaster.app:color/abc_decor_view_status_guard = 0x7f060005
com.fishkaster.app:style/Theme.Material3.Light.SideSheetDialog = 0x7f140280
com.fishkaster.app:color/material_personalized_color_tertiary_container = 0x7f0602c8
com.fishkaster.app:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f140114
com.fishkaster.app:style/Theme.Material3.Light.DialogWhenLarge = 0x7f14027e
com.fishkaster.app:id/icon_only = 0x7f0a0132
com.fishkaster.app:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f14027d
com.fishkaster.app:attr/textAppearanceSmallPopupMenu = 0x7f0404ac
com.fishkaster.app:drawable/abc_spinner_mtrl_am_alpha = 0x7f080065
com.fishkaster.app:style/Theme.Material3.Light.Dialog.Alert = 0x7f14027c
com.fishkaster.app:string/exo_controls_cc_disabled_description = 0x7f13006f
com.fishkaster.app:style/Theme.Material3.Light = 0x7f140279
com.fishkaster.app:id/textinput_placeholder = 0x7f0a023a
com.fishkaster.app:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f140276
com.fishkaster.app:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f140274
com.fishkaster.app:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f140272
com.fishkaster.app:color/m3_fab_efab_background_color_selector = 0x7f0600ae
com.fishkaster.app:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f140270
com.fishkaster.app:style/Theme.Material3.DayNight.Dialog = 0x7f14026d
com.fishkaster.app:style/Theme.Material3.Dark.SideSheetDialog = 0x7f14026a
com.fishkaster.app:style/Theme.Material3.Dark.NoActionBar = 0x7f140269
com.fishkaster.app:style/Theme.Material3.Dark.Dialog.Alert = 0x7f140266
com.fishkaster.app:style/Theme.Material3.Dark = 0x7f140263
com.fishkaster.app:style/Theme.FullScreenDialogAnimatedFade = 0x7f140261
com.fishkaster.app:style/Theme.DevLauncher.ErrorActivity = 0x7f14025f
com.fishkaster.app:style/Theme.Design.Light.NoActionBar = 0x7f14025d
com.fishkaster.app:style/Theme.Design.Light.BottomSheetDialog = 0x7f14025c
com.fishkaster.app:dimen/m3_btn_disabled_elevation = 0x7f0700f3
com.fishkaster.app:style/Widget.Material3.Chip.Input.Elevated = 0x7f1403b0
com.fishkaster.app:style/Theme.Design.Light = 0x7f14025b
com.fishkaster.app:style/Theme.Design = 0x7f140259
com.fishkaster.app:string/m3_sys_motion_easing_emphasized_path_data = 0x7f1300c3
com.fishkaster.app:style/Theme.AutofillInlineSuggestion = 0x7f140255
com.fishkaster.app:macro/m3_comp_badge_color = 0x7f0e0002
com.fishkaster.app:style/Theme.AppCompat.NoActionBar = 0x7f140254
com.fishkaster.app:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1402cc
com.fishkaster.app:dimen/abc_action_button_min_width_material = 0x7f07000e
com.fishkaster.app:style/Theme.AppCompat.Light.NoActionBar = 0x7f140253
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0e00fe
com.fishkaster.app:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f140250
com.fishkaster.app:attr/fontProviderQuery = 0x7f04023e
com.fishkaster.app:style/Theme.AppCompat.Empty = 0x7f14024c
com.fishkaster.app:layout/abc_tooltip = 0x7f0d001b
com.fishkaster.app:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0e0097
com.fishkaster.app:style/Theme.AppCompat.Dialog = 0x7f140248
com.fishkaster.app:attr/drawableRightCompat = 0x7f0401bc
com.fishkaster.app:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f140246
com.fishkaster.app:id/activity_chooser_view_content = 0x7f0a0052
com.fishkaster.app:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1402d8
com.fishkaster.app:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1402c6
com.fishkaster.app:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f140245
com.fishkaster.app:id/mtrl_view_tag_bottom_padding = 0x7f0a018a
com.fishkaster.app:style/Theme.AppCompat.CompactMenu = 0x7f140240
com.fishkaster.app:style/Theme = 0x7f14023d
com.fishkaster.app:id/action_context_bar = 0x7f0a0048
com.fishkaster.app:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f14023a
com.fishkaster.app:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f140237
com.fishkaster.app:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f140236
com.fishkaster.app:dimen/tooltip_precise_anchor_threshold = 0x7f070345
com.fishkaster.app:style/TextAppearance.MaterialComponents.Overline = 0x7f140235
com.fishkaster.app:attr/boxStrokeWidthFocused = 0x7f040093
com.fishkaster.app:style/TextAppearance.MaterialComponents.Chip = 0x7f14022e
com.fishkaster.app:style/TextAppearance.MaterialComponents.Button = 0x7f14022c
com.fishkaster.app:style/TextAppearance.Material3.TitleMedium = 0x7f140227
com.fishkaster.app:style/TextAppearance.Material3.TitleLarge = 0x7f140226
com.fishkaster.app:style/Theme.AppCompat.Light.Dialog = 0x7f14024f
com.fishkaster.app:color/mtrl_btn_ripple_color = 0x7f0602de
com.fishkaster.app:style/TextAppearance.Material3.SearchBar = 0x7f140223
com.fishkaster.app:style/TextAppearance.Material3.LabelMedium = 0x7f140220
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f140477
com.fishkaster.app:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f140460
com.fishkaster.app:style/TextAppearance.Material3.HeadlineMedium = 0x7f14021d
com.fishkaster.app:style/TextAppearance.Material3.HeadlineLarge = 0x7f14021c
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0601d4
com.fishkaster.app:style/TextAppearance.Material3.DisplayMedium = 0x7f14021a
com.fishkaster.app:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f140496
com.fishkaster.app:style/TextAppearance.Material3.BodySmall = 0x7f140218
com.fishkaster.app:color/tooltip_background_light = 0x7f06032d
com.fishkaster.app:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f140214
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0e00d2
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f140213
com.fishkaster.app:string/exposed_dropdown_menu_content_description = 0x7f1300a7
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f140212
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f140211
com.fishkaster.app:style/Widget.AppCompat.ActionBar.TabView = 0x7f14032a
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f14020f
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f14020e
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f14020b
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f140209
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f140208
com.fishkaster.app:id/pointer_events = 0x7f0a01bf
com.fishkaster.app:style/TextAppearance.Design.Snackbar.Message = 0x7f140202
com.fishkaster.app:attr/isMaterial3Theme = 0x7f04027d
com.fishkaster.app:style/TextAppearance.Design.Prefix = 0x7f140201
com.fishkaster.app:style/TextAppearance.Design.HelperText = 0x7f1401fe
com.fishkaster.app:style/Base.DialogWindowTitle.AppCompat = 0x7f140014
com.fishkaster.app:style/TextAppearance.Compat.Notification.Title = 0x7f1401f8
com.fishkaster.app:style/TextAppearance.Compat.Notification = 0x7f1401f0
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1401eb
com.fishkaster.app:layout/abc_alert_dialog_title_material = 0x7f0d000a
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1401ea
com.fishkaster.app:attr/cornerRadius = 0x7f040156
com.fishkaster.app:style/Widget.AppCompat.ActionButton.Overflow = 0x7f14032d
com.fishkaster.app:attr/fontStyle = 0x7f040240
com.fishkaster.app:dimen/abc_action_button_min_height_material = 0x7f07000d
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1401e9
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1401e8
com.fishkaster.app:id/invalidate_transform = 0x7f0a013a
com.fishkaster.app:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0e0132
com.fishkaster.app:style/Widget.Material3.SearchBar = 0x7f14040a
com.fishkaster.app:attr/layout_goneMarginStart = 0x7f0402e7
com.fishkaster.app:style/Widget.AppCompat.Button = 0x7f140331
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1401e5
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1401de
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1401dd
com.fishkaster.app:attr/foregroundInsidePadding = 0x7f040245
com.fishkaster.app:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1401db
com.fishkaster.app:style/TextAppearance.AppCompat.Title = 0x7f1401da
com.fishkaster.app:attr/textAppearanceOverline = 0x7f0404a8
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1401ef
com.fishkaster.app:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1401d9
com.fishkaster.app:attr/colorSurfaceContainerHigh = 0x7f040129
com.fishkaster.app:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1401d7
com.fishkaster.app:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1401d4
com.fishkaster.app:style/TextAppearance.AppCompat.Large = 0x7f1401cb
com.fishkaster.app:styleable/MaterialCalendar = 0x7f150057
com.fishkaster.app:style/TextAppearance.AppCompat.Headline = 0x7f1401c9
com.fishkaster.app:dimen/fastscroll_minimum_range = 0x7f0700b8
com.fishkaster.app:style/Widget.Material3.Chip.Input.Icon = 0x7f1403b1
com.fishkaster.app:style/TextAppearance.AppCompat.Display4 = 0x7f1401c8
com.fishkaster.app:style/TextAppearance.AppCompat.Display2 = 0x7f1401c6
com.fishkaster.app:style/TextAppearance.AppCompat.Button = 0x7f1401c3
com.fishkaster.app:style/TextAppearance.AppCompat.Body2 = 0x7f1401c2
com.fishkaster.app:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f1404a8
com.fishkaster.app:attr/expandedTitleMarginEnd = 0x7f0401f0
com.fishkaster.app:style/TextAppearance.AppCompat.Body1 = 0x7f1401c1
com.fishkaster.app:attr/activityChooserViewStyle = 0x7f040026
com.fishkaster.app:attr/layout_constraintHeight_percent = 0x7f0402c9
com.fishkaster.app:style/TextAppearance.AppCompat = 0x7f1401c0
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1401bd
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1401bb
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1401ba
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1401b9
com.fishkaster.app:drawable/exo_icon_circular_play = 0x7f0800c5
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1401b8
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f1401b6
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f1401b3
com.fishkaster.app:id/media_controller_compat_view_tag = 0x7f0a0168
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f1401b2
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f1401b1
com.fishkaster.app:attr/hide_on_touch = 0x7f040259
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f1401ae
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f140315
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.Button = 0x7f1401ab
com.fishkaster.app:attr/colorOnPrimaryContainer = 0x7f040107
com.fishkaster.app:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0e001c
com.fishkaster.app:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f1401aa
com.fishkaster.app:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1402ef
com.fishkaster.app:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f1401a7
com.fishkaster.app:style/Widget.Material3.NavigationView = 0x7f140403
com.fishkaster.app:style/ShapeAppearance.MaterialComponents = 0x7f1401a5
com.fishkaster.app:style/ShapeAppearance.Material3.SmallComponent = 0x7f1401a3
com.fishkaster.app:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f1401a2
com.fishkaster.app:color/m3_ref_palette_neutral22 = 0x7f06012e
com.fishkaster.app:style/ShapeAppearance.Material3.MediumComponent = 0x7f1401a1
com.fishkaster.app:color/button_material_dark = 0x7f06002b
com.fishkaster.app:color/m3_efab_ripple_color_selector = 0x7f0600ac
com.fishkaster.app:id/actions = 0x7f0a0051
com.fishkaster.app:style/ShapeAppearance.Material3.LargeComponent = 0x7f1401a0
com.fishkaster.app:style/ShapeAppearance.Material3.Corner.Small = 0x7f14019f
com.fishkaster.app:style/ShapeAppearance.Material3.Corner.Medium = 0x7f14019d
com.fishkaster.app:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f14019a
com.fishkaster.app:styleable/Fragment = 0x7f15003c
com.fishkaster.app:attr/enforceTextAppearance = 0x7f0401dc
com.fishkaster.app:attr/startDestination = 0x7f04043e
com.fishkaster.app:style/Theme.Material3.DynamicColors.Light = 0x7f140277
com.fishkaster.app:attr/layout_behavior = 0x7f0402b3
com.fishkaster.app:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f140199
com.fishkaster.app:style/Widget.AppCompat.Button.Small = 0x7f140336
com.fishkaster.app:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f140198
com.fishkaster.app:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f140483
com.fishkaster.app:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f140196
com.fishkaster.app:dimen/mtrl_calendar_header_text_padding = 0x7f0702aa
com.fishkaster.app:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f14018e
com.fishkaster.app:drawable/exo_styled_controls_shuffle_off = 0x7f0800ee
com.fishkaster.app:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f14018d
com.fishkaster.app:style/Widget.AppCompat.SearchView.ActionBar = 0x7f140364
com.fishkaster.app:string/m3_sys_motion_easing_legacy = 0x7f1300c4
com.fishkaster.app:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f14018b
com.fishkaster.app:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080085
com.fishkaster.app:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f14018a
com.fishkaster.app:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f140185
com.fishkaster.app:styleable/ConstraintLayout_placeholder = 0x7f15002b
com.fishkaster.app:color/bright_foreground_material_dark = 0x7f060025
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface_container = 0x7f0601bb
com.fishkaster.app:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f140351
com.fishkaster.app:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f140180
com.fishkaster.app:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0e0015
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f14017d
com.fishkaster.app:color/material_grey_600 = 0x7f06028c
com.fishkaster.app:attr/chipMinHeight = 0x7f0400d1
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f14017b
com.fishkaster.app:dimen/m3_toolbar_text_size_title = 0x7f070246
com.fishkaster.app:attr/materialThemeOverlay = 0x7f04033b
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f140177
com.fishkaster.app:attr/actionModePasteDrawable = 0x7f040018
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f140176
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f1404a5
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f140175
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f140170
com.fishkaster.app:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f14016f
com.fishkaster.app:style/Widget.Material3.NavigationRailView.Badge = 0x7f140402
com.fishkaster.app:attr/itemStrokeWidth = 0x7f040296
com.fishkaster.app:style/ProxyAmazonBillingActivityTheme = 0x7f14016e
com.fishkaster.app:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0e00af
com.fishkaster.app:style/Theme.Design.NoActionBar = 0x7f14025e
com.fishkaster.app:style/Platform.V25.AppCompat.Light = 0x7f14016c
com.fishkaster.app:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f140167
com.fishkaster.app:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f0701b2
com.fishkaster.app:style/Platform.ThemeOverlay.AppCompat = 0x7f140166
com.fishkaster.app:style/Platform.AppCompat.Light = 0x7f140161
com.fishkaster.app:style/Platform.AppCompat = 0x7f140160
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f14015a
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f140159
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f140156
com.fishkaster.app:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f140154
com.fishkaster.app:style/MaterialAlertDialog.Material3.Title.Text = 0x7f140153
com.fishkaster.app:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f140152
com.fishkaster.app:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f14014f
com.fishkaster.app:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f14014e
com.fishkaster.app:id/exo_next = 0x7f0a00e2
com.fishkaster.app:id/rewind_button = 0x7f0a01cf
com.fishkaster.app:style/ThemeOverlay.Material3.Search = 0x7f1402f2
com.fishkaster.app:style/FloatingDialogWindowTheme = 0x7f14014a
com.fishkaster.app:dimen/mtrl_switch_text_padding = 0x7f07031c
com.fishkaster.app:styleable/MaterialCalendarItem = 0x7f150058
com.fishkaster.app:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.fishkaster.app:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f140387
com.fishkaster.app:style/ExoStyledControls.TimeText.Separator = 0x7f140149
com.fishkaster.app:attr/waveOffset = 0x7f04052a
com.fishkaster.app:style/ExoStyledControls.TimeText.Position = 0x7f140148
com.fishkaster.app:style/ExoStyledControls.TimeText = 0x7f140146
com.fishkaster.app:style/TextAppearance.Design.Counter = 0x7f1401fb
com.fishkaster.app:style/ExoStyledControls.TimeBar = 0x7f140145
com.fishkaster.app:style/ExoStyledControls.Button.Center.Previous = 0x7f140143
com.fishkaster.app:attr/colorSecondaryContainer = 0x7f040122
com.fishkaster.app:style/ExoStyledControls.Button.Center.Next = 0x7f140141
com.fishkaster.app:color/m3_sys_color_dark_error_container = 0x7f060182
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.Shuffle = 0x7f14013d
com.fishkaster.app:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0701e4
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.RepeatToggle = 0x7f14013b
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.PlaybackSpeed = 0x7f14013a
com.fishkaster.app:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f14023c
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.OverflowShow = 0x7f140139
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.OverflowHide = 0x7f140138
com.fishkaster.app:color/m3_sys_color_on_tertiary_fixed = 0x7f060218
com.fishkaster.app:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f140427
com.fishkaster.app:style/ExoStyledControls.Button = 0x7f140133
com.fishkaster.app:id/list_item = 0x7f0a014d
com.fishkaster.app:style/ExoStyledControls = 0x7f140132
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0e006c
com.fishkaster.app:attr/colorButtonNormal = 0x7f0400fa
com.fishkaster.app:attr/cornerFamilyTopRight = 0x7f040155
com.fishkaster.app:style/ExoMediaButton.Rewind = 0x7f140130
com.fishkaster.app:style/ExoMediaButton.Play = 0x7f14012e
com.fishkaster.app:style/ExoMediaButton.Pause = 0x7f14012d
com.fishkaster.app:style/ExoMediaButton.FastForward = 0x7f14012b
com.fishkaster.app:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1400fa
com.fishkaster.app:style/CardView.Light = 0x7f140126
com.fishkaster.app:style/CalendarDatePickerDialog = 0x7f140122
com.fishkaster.app:style/Base.Widget.MaterialComponents.TextView = 0x7f140121
com.fishkaster.app:style/Base.Widget.MaterialComponents.Snackbar = 0x7f14011e
com.fishkaster.app:style/Base.Widget.MaterialComponents.Slider = 0x7f14011d
com.fishkaster.app:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f14011c
com.fishkaster.app:style/TextAppearance.AppCompat.Inverse = 0x7f1401ca
com.fishkaster.app:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08006c
com.fishkaster.app:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f14011a
com.fishkaster.app:attr/errorAccessibilityLiveRegion = 0x7f0401e0
com.fishkaster.app:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f140117
com.fishkaster.app:style/Base.Widget.MaterialComponents.Chip = 0x7f140116
com.fishkaster.app:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f140112
com.fishkaster.app:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f140424
com.fishkaster.app:interpolator/mtrl_linear_out_slow_in = 0x7f0c0011
com.fishkaster.app:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f14010e
com.fishkaster.app:integer/m3_btn_anim_duration_ms = 0x7f0b000e
com.fishkaster.app:id/seek_bar = 0x7f0a01f5
com.fishkaster.app:attr/nestedScrollable = 0x7f040389
com.fishkaster.app:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f14010d
com.fishkaster.app:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f140108
com.fishkaster.app:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f140107
com.fishkaster.app:style/Base.Widget.Material3.CardView = 0x7f140103
com.fishkaster.app:style/Base.Widget.Material3.BottomNavigationView = 0x7f140102
com.fishkaster.app:attr/singleLine = 0x7f04042f
com.fishkaster.app:string/mtrl_picker_range_header_unselected = 0x7f13010a
com.fishkaster.app:style/Base.Widget.Material3.ActionMode = 0x7f140101
com.fishkaster.app:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1400fe
com.fishkaster.app:color/m3_text_button_background_color_selector = 0x7f060226
com.fishkaster.app:style/Base.Widget.AppCompat.Toolbar = 0x7f1400fd
com.fishkaster.app:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1400fc
com.fishkaster.app:style/Base.Widget.AppCompat.TextView = 0x7f1400fb
com.fishkaster.app:color/abc_secondary_text_material_light = 0x7f060012
com.fishkaster.app:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1400f4
com.fishkaster.app:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1400f3
com.fishkaster.app:style/CardView = 0x7f140124
com.fishkaster.app:style/Base.Widget.AppCompat.RatingBar = 0x7f1400f2
com.fishkaster.app:style/Base.Widget.AppCompat.PopupWindow = 0x7f1400ef
com.fishkaster.app:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1400ec
com.fishkaster.app:attr/editTextStyle = 0x7f0401cc
com.fishkaster.app:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1400e7
com.fishkaster.app:attr/imageButtonStyle = 0x7f04026e
com.fishkaster.app:attr/voiceIcon = 0x7f040527
com.fishkaster.app:id/mtrl_picker_text_input_date = 0x7f0a0186
com.fishkaster.app:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1400e6
com.fishkaster.app:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1400e5
com.fishkaster.app:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1400e4
com.fishkaster.app:styleable/CollapsingToolbarLayout = 0x7f150025
com.fishkaster.app:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1400e0
com.fishkaster.app:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0702cb
com.fishkaster.app:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1400dc
com.fishkaster.app:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f140150
com.fishkaster.app:attr/materialSearchViewToolbarStyle = 0x7f040339
com.fishkaster.app:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1400da
com.fishkaster.app:style/Base.Widget.AppCompat.Button.Small = 0x7f1400d5
com.fishkaster.app:color/dev_launcher_colorAccentDark = 0x7f060069
com.fishkaster.app:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0c0008
com.fishkaster.app:style/Base.Widget.AppCompat.Button.Colored = 0x7f1400d4
com.fishkaster.app:style/Base.Widget.AppCompat.ActionMode = 0x7f1400cd
com.fishkaster.app:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1400cb
com.fishkaster.app:style/Base.Widget.AppCompat.ActionButton = 0x7f1400ca
com.fishkaster.app:string/state_expanded_description = 0x7f13013f
com.fishkaster.app:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1400c9
com.fishkaster.app:string/abc_action_menu_overflow_description = 0x7f130002
com.fishkaster.app:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f14044b
com.fishkaster.app:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1400c8
com.fishkaster.app:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1400c6
com.fishkaster.app:style/Widget.AppCompat.ActionBar.TabBar = 0x7f140328
com.fishkaster.app:style/Base.Widget.AppCompat.ActionBar = 0x7f1400c5
com.fishkaster.app:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1400c0
com.fishkaster.app:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1400ba
com.fishkaster.app:style/Base.V26.Theme.AppCompat = 0x7f1400b8
com.fishkaster.app:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1400b7
com.fishkaster.app:style/Base.V24.Theme.Material3.Light = 0x7f1400b6
com.fishkaster.app:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1400b5
com.fishkaster.app:style/Base.V24.Theme.Material3.Dark = 0x7f1400b4
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f140095
com.fishkaster.app:style/Base.V23.Theme.AppCompat = 0x7f1400b2
com.fishkaster.app:style/Base.V22.Theme.AppCompat.Light = 0x7f1400b1
com.fishkaster.app:style/Base.V22.Theme.AppCompat = 0x7f1400b0
com.fishkaster.app:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1400af
com.fishkaster.app:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1400ad
com.fishkaster.app:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1402d7
com.fishkaster.app:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1400ac
com.fishkaster.app:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f140459
com.fishkaster.app:dimen/mtrl_extended_fab_top_padding = 0x7f0702d8
com.fishkaster.app:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1400ab
com.fishkaster.app:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1400a7
com.fishkaster.app:dimen/m3_carousel_small_item_size_min = 0x7f070118
com.fishkaster.app:style/Base.V21.Theme.AppCompat.Light = 0x7f1400a6
com.fishkaster.app:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1400a5
com.fishkaster.app:style/Base.V21.Theme.AppCompat = 0x7f1400a4
com.fishkaster.app:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1400a2
com.fishkaster.app:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f14009f
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f14009a
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f140099
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents.Light = 0x7f140098
com.fishkaster.app:attr/maxWidth = 0x7f040348
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f140096
com.fishkaster.app:color/material_grey_50 = 0x7f06028b
com.fishkaster.app:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f140093
com.fishkaster.app:style/Base.V14.Theme.Material3.Light = 0x7f140090
com.fishkaster.app:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f14008f
com.fishkaster.app:attr/cropMinCropWindowHeight = 0x7f04017c
com.fishkaster.app:attr/chipIconTint = 0x7f0400cf
com.fishkaster.app:style/Theme.MaterialComponents.Dialog.Alert = 0x7f140296
com.fishkaster.app:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f14008e
com.fishkaster.app:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f14008d
com.fishkaster.app:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f14008b
com.fishkaster.app:dimen/appcompat_dialog_background_inset = 0x7f070051
com.fishkaster.app:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f07013f
com.fishkaster.app:styleable/MaterialCheckBox = 0x7f15005a
com.fishkaster.app:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f14008a
com.fishkaster.app:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f140087
com.fishkaster.app:attr/checkedIcon = 0x7f0400bf
com.fishkaster.app:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f140085
com.fishkaster.app:styleable/MotionHelper = 0x7f150069
com.fishkaster.app:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f14018f
com.fishkaster.app:style/Base.ThemeOverlay.Material3.Dialog = 0x7f140084
com.fishkaster.app:style/Base.ThemeOverlay.AppCompat.Light = 0x7f140081
com.fishkaster.app:anim/rns_fade_in = 0x7f010037
com.fishkaster.app:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f14007e
com.fishkaster.app:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f14007d
com.fishkaster.app:style/Base.ThemeOverlay.AppCompat = 0x7f14007b
com.fishkaster.app:color/material_personalized_color_on_primary_container = 0x7f0602aa
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f140078
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f140077
com.fishkaster.app:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1402c5
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light = 0x7f140071
com.fishkaster.app:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f14006f
com.fishkaster.app:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f140065
com.fishkaster.app:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f08001d
com.fishkaster.app:attr/cropBorderCornerColor = 0x7f040167
com.fishkaster.app:style/Base.Theme.Material3.Light.Dialog = 0x7f140064
com.fishkaster.app:string/exo_controls_fastforward_description = 0x7f130072
com.fishkaster.app:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f140063
com.fishkaster.app:color/secondary_text_default_material_dark = 0x7f060321
com.fishkaster.app:style/Base.Theme.Material3.Light = 0x7f140062
com.fishkaster.app:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f140061
com.fishkaster.app:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f140060
com.fishkaster.app:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f14005f
com.fishkaster.app:attr/badgeVerticalPadding = 0x7f040067
com.fishkaster.app:style/Base.Theme.Material3.Dark.Dialog = 0x7f14005e
com.fishkaster.app:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f14005d
com.fishkaster.app:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f14026f
com.fishkaster.app:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f140278
com.fishkaster.app:style/Base.Widget.Material3.Snackbar = 0x7f140110
com.fishkaster.app:dimen/m3_comp_switch_track_height = 0x7f0701bd
com.fishkaster.app:style/Base.Widget.AppCompat.Button = 0x7f1400d0
com.fishkaster.app:style/Base.Theme.Material3.Dark = 0x7f14005c
com.fishkaster.app:color/mtrl_navigation_item_icon_tint = 0x7f0602fd
com.fishkaster.app:style/Base.Theme.AppCompat.Light.Dialog = 0x7f140057
com.fishkaster.app:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f140054
com.fishkaster.app:style/Base.Theme.AppCompat.Dialog = 0x7f140050
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f140300
com.fishkaster.app:attr/autoSizePresetSizes = 0x7f040047
com.fishkaster.app:style/Base.Theme.AppCompat = 0x7f14004e
com.fishkaster.app:attr/imageAspectRatioAdjust = 0x7f04026d
com.fishkaster.app:style/TextAppearance.Material3.BodyMedium = 0x7f140217
com.fishkaster.app:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f14004d
com.fishkaster.app:drawable/abc_btn_check_material_anim = 0x7f08002c
com.fishkaster.app:id/submit_area = 0x7f0a021c
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1403d0
com.fishkaster.app:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f14004c
com.fishkaster.app:style/Widget.Material3.Slider.Legacy.Label = 0x7f140416
com.fishkaster.app:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f14004a
com.fishkaster.app:dimen/m3_btn_max_width = 0x7f0700fe
com.fishkaster.app:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f140049
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f140045
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f140043
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f140041
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f14003a
com.fishkaster.app:layout/support_simple_spinner_dropdown_item = 0x7f0d008e
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f140038
com.fishkaster.app:attr/dividerColor = 0x7f0401ae
com.fishkaster.app:attr/number = 0x7f04038b
com.fishkaster.app:string/exo_controls_next_description = 0x7f130076
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f140034
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Subhead = 0x7f140030
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f14002f
com.fishkaster.app:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f14002d
com.fishkaster.app:string/material_minute_suffix = 0x7f1300d1
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Medium = 0x7f140028
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Large = 0x7f140024
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Display1 = 0x7f14001e
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Body1 = 0x7f14001a
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Menu = 0x7f14002a
com.fishkaster.app:style/Base.TextAppearance.AppCompat = 0x7f140019
com.fishkaster.app:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f140018
com.fishkaster.app:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f140017
com.fishkaster.app:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f0701b6
com.fishkaster.app:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f140016
com.fishkaster.app:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f140015
com.fishkaster.app:attr/roundTopEnd = 0x7f0403e9
com.fishkaster.app:style/Base.CardView = 0x7f140013
com.fishkaster.app:style/Base.Animation.AppCompat.Tooltip = 0x7f140012
com.fishkaster.app:style/Base.Animation.AppCompat.Dialog = 0x7f140010
com.fishkaster.app:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f1403b9
com.fishkaster.app:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f14000c
com.fishkaster.app:style/Animation.Material3.SideSheetDialog.Left = 0x7f14000a
com.fishkaster.app:styleable/ListPopupWindow = 0x7f15004f
com.fishkaster.app:id/search_edit_frame = 0x7f0a01ef
com.fishkaster.app:string/exo_track_bitrate = 0x7f130093
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f140171
com.fishkaster.app:drawable/m3_radiobutton_ripple = 0x7f08011c
com.fishkaster.app:style/Animation.Material3.BottomSheetDialog = 0x7f140008
com.fishkaster.app:attr/commitIcon = 0x7f040135
com.fishkaster.app:style/Animation.Design.BottomSheetDialog = 0x7f140007
com.fishkaster.app:style/Animation.Catalyst.RedBox = 0x7f140006
com.fishkaster.app:style/Widget.Design.TabLayout = 0x7f14037f
com.fishkaster.app:style/Widget.Material3.TabLayout.OnSurface = 0x7f14041b
com.fishkaster.app:style/Animation.AppCompat.Tooltip = 0x7f140004
com.fishkaster.app:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f14005a
com.fishkaster.app:style/Animation.AppCompat.Dialog = 0x7f140002
com.fishkaster.app:string/tooltip_label = 0x7f13014f
com.fishkaster.app:id/floating = 0x7f0a0113
com.fishkaster.app:style/Widget.Compat.NotificationActionContainer = 0x7f140375
com.fishkaster.app:string/tooltip_description = 0x7f13014e
com.fishkaster.app:string/toolbar_description = 0x7f13014d
com.fishkaster.app:color/material_dynamic_secondary40 = 0x7f060274
com.fishkaster.app:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f140067
com.fishkaster.app:string/tab = 0x7f130149
com.fishkaster.app:string/status_bar_notification_info_overflow = 0x7f130146
com.fishkaster.app:string/state_off = 0x7f130141
com.fishkaster.app:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f070144
com.fishkaster.app:string/state_empty = 0x7f13013e
com.fishkaster.app:string/state_collapsed_description = 0x7f13013d
com.fishkaster.app:attr/layout_constraintRight_creator = 0x7f0402d0
com.fishkaster.app:string/spinbutton_description = 0x7f13013b
com.fishkaster.app:string/side_sheet_behavior = 0x7f130139
com.fishkaster.app:string/side_sheet_accessibility_pane_title = 0x7f130138
com.fishkaster.app:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.fishkaster.app:string/selected = 0x7f130137
com.fishkaster.app:anim/fragment_fast_out_extra_slow_in = 0x7f010022
com.fishkaster.app:string/searchview_navigation_content_description = 0x7f130136
com.fishkaster.app:string/searchview_clear_text_content_description = 0x7f130135
com.fishkaster.app:string/exo_download_paused = 0x7f13008e
com.fishkaster.app:string/searchbar_scrolling_view_behavior = 0x7f130134
com.fishkaster.app:string/search_menu_title = 0x7f130133
com.fishkaster.app:string/rn_tab_description = 0x7f130131
com.fishkaster.app:dimen/mtrl_alert_dialog_background_inset_end = 0x7f07026d
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f140470
com.fishkaster.app:string/react_native_dev_server_ip = 0x7f130130
com.fishkaster.app:string/range_end = 0x7f13012e
com.fishkaster.app:string/progressbar_description = 0x7f13012c
com.fishkaster.app:drawable/exo_controls_shuffle_on = 0x7f0800b1
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1403d5
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f140320
com.fishkaster.app:string/pick_image_chooser_title = 0x7f13012a
com.fishkaster.app:id/fast_forward_button = 0x7f0a00ff
com.fishkaster.app:string/template_percent = 0x7f13014b
com.fishkaster.app:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0e009c
com.fishkaster.app:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f140238
com.fishkaster.app:dimen/mtrl_snackbar_margin = 0x7f070319
com.fishkaster.app:dimen/mtrl_bottomappbar_height = 0x7f07027e
com.fishkaster.app:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f140183
com.fishkaster.app:id/action_bar_subtitle = 0x7f0a0045
com.fishkaster.app:string/path_password_eye_mask_visible = 0x7f130127
com.fishkaster.app:string/password_toggle_content_description = 0x7f130124
com.fishkaster.app:string/mtrl_switch_thumb_path_unchecked = 0x7f13011d
com.fishkaster.app:string/mtrl_picker_toggle_to_year_selection = 0x7f130117
com.fishkaster.app:string/mtrl_picker_toggle_to_day_selection = 0x7f130115
com.fishkaster.app:string/mtrl_picker_text_input_month_abbr = 0x7f130111
com.fishkaster.app:drawable/abc_ic_menu_overflow_material = 0x7f080044
com.fishkaster.app:string/mtrl_picker_text_input_day_abbr = 0x7f130110
com.fishkaster.app:drawable/notification_bg_low_normal = 0x7f080155
com.fishkaster.app:string/mtrl_picker_text_input_date_range_end_hint = 0x7f13010e
com.fishkaster.app:attr/cameraMinZoomPreference = 0x7f0400a9
com.fishkaster.app:string/mtrl_picker_navigate_to_current_year_description = 0x7f130103
com.fishkaster.app:string/mtrl_picker_invalid_range = 0x7f130102
com.fishkaster.app:string/exo_track_surround_7_point_1 = 0x7f1300a2
com.fishkaster.app:string/mtrl_picker_invalid_format_use = 0x7f130101
com.fishkaster.app:string/mtrl_picker_invalid_format_example = 0x7f130100
com.fishkaster.app:string/mtrl_picker_invalid_format = 0x7f1300ff
com.fishkaster.app:string/mtrl_picker_date_header_unselected = 0x7f1300fc
com.fishkaster.app:string/mtrl_picker_date_header_selected = 0x7f1300fa
com.fishkaster.app:string/mtrl_picker_confirm = 0x7f1300f9
com.fishkaster.app:string/mtrl_picker_announce_current_range_selection = 0x7f1300f5
com.fishkaster.app:color/m3_slider_thumb_color = 0x7f06017c
com.fishkaster.app:attr/motionStagger = 0x7f04037a
com.fishkaster.app:string/mtrl_picker_a11y_prev_month = 0x7f1300f4
com.fishkaster.app:id/accessibility_order = 0x7f0a0039
com.fishkaster.app:string/mtrl_picker_a11y_next_month = 0x7f1300f3
com.fishkaster.app:string/mtrl_chip_close_icon_content_description = 0x7f1300f0
com.fishkaster.app:string/mtrl_checkbox_state_description_unchecked = 0x7f1300ef
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker = 0x7f1403f7
com.fishkaster.app:string/mtrl_checkbox_state_description_indeterminate = 0x7f1300ee
com.fishkaster.app:attr/bar_height = 0x7f040071
com.fishkaster.app:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f140388
com.fishkaster.app:string/mtrl_checkbox_button_path_unchecked = 0x7f1300ec
com.fishkaster.app:string/mtrl_checkbox_button_icon_path_name = 0x7f1300e8
com.fishkaster.app:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f1300e7
com.fishkaster.app:string/menubar_description = 0x7f1300e2
com.fishkaster.app:string/menu_description = 0x7f1300e1
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1403d1
com.fishkaster.app:string/material_timepicker_text_input_mode_description = 0x7f1300e0
com.fishkaster.app:string/material_timepicker_pm = 0x7f1300de
com.fishkaster.app:string/material_timepicker_minute = 0x7f1300dd
com.fishkaster.app:string/material_slider_value = 0x7f1300d9
com.fishkaster.app:integer/design_snackbar_text_max_lines = 0x7f0b0006
com.fishkaster.app:string/material_slider_range_start = 0x7f1300d8
com.fishkaster.app:string/material_slider_range_end = 0x7f1300d7
com.fishkaster.app:style/DialogAnimationSlide = 0x7f140128
com.fishkaster.app:string/material_motion_easing_standard = 0x7f1300d6
com.fishkaster.app:string/material_motion_easing_linear = 0x7f1300d5
com.fishkaster.app:color/cardview_dark_background = 0x7f06002f
com.fishkaster.app:string/material_motion_easing_emphasized = 0x7f1300d4
com.fishkaster.app:string/m3_sys_motion_easing_standard_decelerate = 0x7f1300ca
com.fishkaster.app:style/Widget.MaterialComponents.TabLayout = 0x7f14048c
com.fishkaster.app:string/m3_sys_motion_easing_standard_accelerate = 0x7f1300c9
com.fishkaster.app:string/m3_sys_motion_easing_linear = 0x7f1300c7
com.fishkaster.app:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f1300c2
com.fishkaster.app:dimen/material_bottom_sheet_max_width = 0x7f070247
com.fishkaster.app:string/m3_ref_typeface_plain_regular = 0x7f1300bf
com.fishkaster.app:string/m3_ref_typeface_brand_regular = 0x7f1300bd
com.fishkaster.app:string/m3_ref_typeface_brand_medium = 0x7f1300bc
com.fishkaster.app:string/m3_exceed_max_badge_text_suffix = 0x7f1300bb
com.fishkaster.app:string/link_description = 0x7f1300ba
com.fishkaster.app:style/TextAppearance.Compat.Notification.Time = 0x7f1401f6
com.fishkaster.app:string/indeterminate = 0x7f1300b8
com.fishkaster.app:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1400cc
com.fishkaster.app:string/image_description = 0x7f1300b5
com.fishkaster.app:string/icon_content_description = 0x7f1300b4
com.fishkaster.app:string/ic_flip_24_vertically = 0x7f1300b1
com.fishkaster.app:attr/itemMaxLines = 0x7f040287
com.fishkaster.app:attr/ambientEnabled = 0x7f040035
com.fishkaster.app:styleable/NavArgument = 0x7f15006e
com.fishkaster.app:attr/materialCardViewElevatedStyle = 0x7f040328
com.fishkaster.app:dimen/abc_text_size_display_3_material = 0x7f070045
com.fishkaster.app:string/ic_flip_24_horizontally = 0x7f1300b0
com.fishkaster.app:string/ic_flip_24 = 0x7f1300af
com.fishkaster.app:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f070163
com.fishkaster.app:string/hide_bottom_view_on_scroll_behavior = 0x7f1300ae
com.fishkaster.app:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f14045c
com.fishkaster.app:attr/showText = 0x7f04041b
com.fishkaster.app:string/header_description = 0x7f1300ad
com.fishkaster.app:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1401d5
com.fishkaster.app:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0701c4
com.fishkaster.app:string/fab_transformation_scrim_behavior = 0x7f1300a8
com.fishkaster.app:string/exo_track_surround = 0x7f1300a0
com.fishkaster.app:string/exo_track_selection_title_video = 0x7f13009e
com.fishkaster.app:dimen/abc_button_inset_vertical_material = 0x7f070013
com.fishkaster.app:string/exo_track_selection_title_audio = 0x7f13009c
com.fishkaster.app:style/Widget.Material3.CircularProgressIndicator = 0x7f1403b6
com.fishkaster.app:color/m3_ref_palette_error40 = 0x7f060120
com.fishkaster.app:dimen/m3_searchbar_elevation = 0x7f070200
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f140040
com.fishkaster.app:attr/isMaterial3DynamicColorApplied = 0x7f04027c
com.fishkaster.app:string/exo_track_selection_none = 0x7f13009b
com.fishkaster.app:color/m3_card_ripple_color = 0x7f060092
com.fishkaster.app:macro/m3_comp_divider_color = 0x7f0e0028
com.fishkaster.app:string/exo_track_selection_auto = 0x7f13009a
com.fishkaster.app:string/exo_track_role_supplementary = 0x7f130099
com.fishkaster.app:style/TextAppearance.Compat.Notification.Info = 0x7f1401f1
com.fishkaster.app:string/exo_track_resolution = 0x7f130095
com.fishkaster.app:layout/notification_template_big_media_custom = 0x7f0d007d
com.fishkaster.app:string/exo_track_mono = 0x7f130094
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1403e3
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1401df
com.fishkaster.app:string/exo_download_removing = 0x7f130091
com.fishkaster.app:string/exo_download_paused_for_wifi = 0x7f130090
com.fishkaster.app:string/exo_download_paused_for_network = 0x7f13008f
com.fishkaster.app:string/exo_download_downloading = 0x7f13008b
com.fishkaster.app:dimen/notification_action_text_size = 0x7f070333
com.fishkaster.app:string/exit_fullscreen_mode = 0x7f13006e
com.fishkaster.app:string/exo_download_description = 0x7f13008a
com.fishkaster.app:string/exo_controls_vr_description = 0x7f130088
com.fishkaster.app:string/exo_controls_time_placeholder = 0x7f130087
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1401ec
com.fishkaster.app:drawable/notification_template_icon_bg = 0x7f08015b
com.fishkaster.app:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f140181
com.fishkaster.app:string/exo_controls_shuffle_off_description = 0x7f130084
com.fishkaster.app:string/exo_controls_repeat_one_description = 0x7f13007f
com.fishkaster.app:string/exo_controls_playback_speed = 0x7f13007b
com.fishkaster.app:macro/m3_comp_elevated_card_container_shape = 0x7f0e002b
com.fishkaster.app:id/decor_content_parent = 0x7f0a00a5
com.fishkaster.app:string/mtrl_picker_save = 0x7f13010b
com.fishkaster.app:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1400ee
com.fishkaster.app:string/exo_controls_hide = 0x7f130075
com.fishkaster.app:dimen/mtrl_progress_circular_inset = 0x7f0702fa
com.fishkaster.app:string/exo_controls_fullscreen_exit_description = 0x7f130074
com.fishkaster.app:attr/defaultScrollFlagsEnabled = 0x7f0401a2
com.fishkaster.app:string/mtrl_picker_navigate_to_year_description = 0x7f130104
com.fishkaster.app:string/error_icon_content_description = 0x7f13006d
com.fishkaster.app:attr/tooltipText = 0x7f0404f4
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.VR = 0x7f14013e
com.fishkaster.app:color/mtrl_chip_text_color = 0x7f0602ec
com.fishkaster.app:string/enter_fullscreen_mode = 0x7f13006b
com.fishkaster.app:string/dropdown_menu = 0x7f13006a
com.fishkaster.app:color/material_dynamic_tertiary0 = 0x7f06027c
com.fishkaster.app:string/crop_image_menu_crop = 0x7f130066
com.fishkaster.app:string/common_open_on_phone = 0x7f130062
com.fishkaster.app:string/common_google_play_services_updating_text = 0x7f130060
com.fishkaster.app:string/fallback_menu_item_open_in_browser = 0x7f1300ab
com.fishkaster.app:string/common_google_play_services_update_text = 0x7f13005e
com.fishkaster.app:string/common_google_play_services_notification_ticker = 0x7f13005a
com.fishkaster.app:string/exo_controls_repeat_all_description = 0x7f13007d
com.fishkaster.app:id/tag_accessibility_pane_title = 0x7f0a0222
com.fishkaster.app:string/ic_rotate_left_24 = 0x7f1300b2
com.fishkaster.app:string/common_google_play_services_install_text = 0x7f130057
com.fishkaster.app:color/m3_slider_halo_color_legacy = 0x7f060179
com.fishkaster.app:string/common_google_play_services_install_button = 0x7f130056
com.fishkaster.app:macro/m3_sys_color_dark_surface_tint = 0x7f0e0175
com.fishkaster.app:string/common_google_play_services_enable_title = 0x7f130055
com.fishkaster.app:string/close_sheet = 0x7f130051
com.fishkaster.app:string/close_drawer = 0x7f130050
com.fishkaster.app:attr/materialAlertDialogBodyTextStyle = 0x7f040310
com.fishkaster.app:style/SpinnerDatePickerDialog = 0x7f1401be
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Display3 = 0x7f140020
com.fishkaster.app:attr/zOrderOnTop = 0x7f04053b
com.fishkaster.app:string/clear_text_end_icon_content_description = 0x7f13004f
com.fishkaster.app:string/character_counter_pattern = 0x7f13004e
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f14015e
com.fishkaster.app:string/character_counter_content_description = 0x7f13004c
com.fishkaster.app:string/catalyst_settings_title = 0x7f13004b
com.fishkaster.app:string/mtrl_switch_track_path = 0x7f13011f
com.fishkaster.app:string/catalyst_reload_error = 0x7f130047
com.fishkaster.app:string/catalyst_reload = 0x7f130045
com.fishkaster.app:styleable/MaterialTextView = 0x7f150061
com.fishkaster.app:string/catalyst_open_debugger_error = 0x7f130042
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f14003b
com.fishkaster.app:layout/fps_view = 0x7f0d0042
com.fishkaster.app:string/catalyst_loading_from_url = 0x7f130041
com.fishkaster.app:string/catalyst_inspector_toggle = 0x7f130040
com.fishkaster.app:string/catalyst_hot_reloading_stop = 0x7f13003f
com.fishkaster.app:attr/buttonPanelSideLayout = 0x7f0400a1
com.fishkaster.app:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f14034d
com.fishkaster.app:string/fab_transformation_sheet_behavior = 0x7f1300a9
com.fishkaster.app:dimen/mtrl_calendar_days_of_week_height = 0x7f0702a2
com.fishkaster.app:string/catalyst_hot_reloading_auto_enable = 0x7f13003e
com.fishkaster.app:string/catalyst_hot_reloading_auto_disable = 0x7f13003d
com.fishkaster.app:style/Widget.Compat.NotificationActionText = 0x7f140376
com.fishkaster.app:string/catalyst_hot_reloading = 0x7f13003c
com.fishkaster.app:string/catalyst_debug_open = 0x7f130036
com.fishkaster.app:string/catalyst_copy_button = 0x7f130033
com.fishkaster.app:string/catalyst_change_bundle_location_instructions = 0x7f130032
com.fishkaster.app:color/m3_sys_color_dark_surface_variant = 0x7f06019f
com.fishkaster.app:string/catalyst_change_bundle_location_input_label = 0x7f130031
com.fishkaster.app:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0e001e
com.fishkaster.app:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1403cc
com.fishkaster.app:string/catalyst_change_bundle_location_input_hint = 0x7f130030
com.fishkaster.app:string/catalyst_change_bundle_location_apply = 0x7f13002e
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f1401af
com.fishkaster.app:string/catalyst_change_bundle_location = 0x7f13002d
com.fishkaster.app:attr/trackCornerRadius = 0x7f0404fe
com.fishkaster.app:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f1403b2
com.fishkaster.app:string/call_notification_screening_text = 0x7f13002c
com.fishkaster.app:string/call_notification_ongoing_text = 0x7f13002b
com.fishkaster.app:string/call_notification_incoming_text = 0x7f13002a
com.fishkaster.app:string/call_notification_decline_action = 0x7f130028
com.fishkaster.app:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f080025
com.fishkaster.app:style/Base.Widget.AppCompat.Spinner = 0x7f1400f9
com.fishkaster.app:string/call_notification_answer_action = 0x7f130026
com.fishkaster.app:string/bottom_sheet_behavior = 0x7f130020
com.fishkaster.app:string/appbar_scrolling_view_behavior = 0x7f13001e
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary10 = 0x7f060102
com.fishkaster.app:color/m3_calendar_item_stroke_color = 0x7f060090
com.fishkaster.app:string/app_name = 0x7f13001d
com.fishkaster.app:string/abc_shareactionprovider_share_with_application = 0x7f130019
com.fishkaster.app:string/abc_shareactionprovider_share_with = 0x7f130018
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_background = 0x7f0601a8
com.fishkaster.app:string/abc_searchview_description_search = 0x7f130015
com.fishkaster.app:macro/m3_comp_fab_secondary_container_color = 0x7f0e003b
com.fishkaster.app:string/abc_prepend_shortcut_label = 0x7f130011
com.fishkaster.app:string/abc_menu_sym_shortcut_label = 0x7f130010
com.fishkaster.app:string/exo_controls_pause_description = 0x7f130079
com.fishkaster.app:id/notification_main_column = 0x7f0a0199
com.fishkaster.app:string/abc_menu_space_shortcut_label = 0x7f13000f
com.fishkaster.app:id/search_go_btn = 0x7f0a01f0
com.fishkaster.app:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1400e3
com.fishkaster.app:string/abc_menu_shift_shortcut_label = 0x7f13000e
com.fishkaster.app:string/abc_menu_function_shortcut_label = 0x7f13000c
com.fishkaster.app:attr/layout_goneMarginTop = 0x7f0402e8
com.fishkaster.app:string/abc_menu_enter_shortcut_label = 0x7f13000b
com.fishkaster.app:string/abc_menu_delete_shortcut_label = 0x7f13000a
com.fishkaster.app:string/abc_menu_ctrl_shortcut_label = 0x7f130009
com.fishkaster.app:style/Widget.AppCompat.Button.Colored = 0x7f140335
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f140042
com.fishkaster.app:attr/layout_constraintVertical_weight = 0x7f0402db
com.fishkaster.app:color/m3_ref_palette_secondary0 = 0x7f06015a
com.fishkaster.app:string/abc_menu_alt_shortcut_label = 0x7f130008
com.fishkaster.app:attr/colorError = 0x7f0400ff
com.fishkaster.app:style/Animation.Catalyst.LogBox = 0x7f140005
com.fishkaster.app:color/material_dynamic_neutral_variant70 = 0x7f06025d
com.fishkaster.app:dimen/m3_navigation_rail_item_padding_top = 0x7f0701f8
com.fishkaster.app:string/abc_capital_on = 0x7f130007
com.fishkaster.app:string/abc_capital_off = 0x7f130006
com.fishkaster.app:string/abc_activity_chooser_view_see_all = 0x7f130004
com.fishkaster.app:string/abc_action_mode_done = 0x7f130003
com.fishkaster.app:string/abc_action_bar_up_description = 0x7f130001
com.fishkaster.app:raw/keep = 0x7f120002
com.fishkaster.app:styleable/AppCompatTextView = 0x7f150012
com.fishkaster.app:raw/com_android_billingclient_registration_info = 0x7f120001
com.fishkaster.app:raw/com_android_billingclient_heterodyne_info = 0x7f120000
com.fishkaster.app:plurals/mtrl_badge_content_description = 0x7f110002
com.fishkaster.app:integer/m3_sys_motion_duration_long1 = 0x7f0b0016
com.fishkaster.app:mipmap/ic_launcher_foreground = 0x7f100001
com.fishkaster.app:mipmap/ic_launcher = 0x7f100000
com.fishkaster.app:macro/m3_sys_color_light_surface_tint = 0x7f0e0176
com.fishkaster.app:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0e00b3
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f140027
com.fishkaster.app:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0e0173
com.fishkaster.app:style/Widget.MaterialComponents.NavigationRailView = 0x7f14047c
com.fishkaster.app:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0e0172
com.fishkaster.app:macro/m3_comp_top_app_bar_small_container_color = 0x7f0e016f
com.fishkaster.app:dimen/design_tab_text_size = 0x7f07008e
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Light = 0x7f14030f
com.fishkaster.app:layout/material_timepicker_textinput_display = 0x7f0d0057
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f140318
com.fishkaster.app:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0e016c
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0e016a
com.fishkaster.app:attr/failureImageScaleType = 0x7f04020d
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0e0167
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f14030d
com.fishkaster.app:string/catalyst_dismiss_button = 0x7f13003a
com.fishkaster.app:attr/circularProgressIndicatorStyle = 0x7f0400de
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0e0163
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0e0161
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0e015f
com.fishkaster.app:attr/state_collapsible = 0x7f040448
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0e015e
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0e015c
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0e015b
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0e0159
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0e0158
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0e0157
com.fishkaster.app:string/call_notification_answer_video_action = 0x7f130027
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0e0152
com.fishkaster.app:color/design_dark_default_color_error = 0x7f060045
com.fishkaster.app:macro/m3_comp_time_picker_headline_color = 0x7f0e0150
com.fishkaster.app:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.fishkaster.app:macro/m3_comp_time_picker_container_color = 0x7f0e014e
com.fishkaster.app:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0e014d
com.fishkaster.app:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0e014a
com.fishkaster.app:style/Widget.AppCompat.Light.SearchView = 0x7f140354
com.fishkaster.app:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0e0149
com.fishkaster.app:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0e0148
com.fishkaster.app:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0e0147
com.fishkaster.app:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0e0146
com.fishkaster.app:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0e0141
com.fishkaster.app:macro/m3_comp_switch_unselected_track_color = 0x7f0e0140
com.fishkaster.app:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0e013e
com.fishkaster.app:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0e013c
com.fishkaster.app:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0e013b
com.fishkaster.app:style/TextAppearance.AppCompat.Display3 = 0x7f1401c7
com.fishkaster.app:id/topPanel = 0x7f0a0244
com.fishkaster.app:macro/m3_comp_switch_unselected_handle_color = 0x7f0e0134
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents = 0x7f140094
com.fishkaster.app:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0e012f
com.fishkaster.app:macro/m3_comp_switch_selected_track_color = 0x7f0e012e
com.fishkaster.app:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0e012d
com.fishkaster.app:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0e012c
com.fishkaster.app:attr/motion_postLayoutCollision = 0x7f04037c
com.fishkaster.app:color/m3_simple_item_ripple_color = 0x7f060176
com.fishkaster.app:id/add = 0x7f0a0053
com.fishkaster.app:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0e012b
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f140286
com.fishkaster.app:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0e012a
com.fishkaster.app:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f14026e
com.fishkaster.app:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f140490
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600d3
com.fishkaster.app:macro/m3_comp_switch_selected_hover_track_color = 0x7f0e0128
com.fishkaster.app:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0e0126
com.fishkaster.app:style/TextAppearance.Compat.Notification.Line2 = 0x7f1401f3
com.fishkaster.app:macro/m3_comp_switch_selected_focus_track_color = 0x7f0e0123
com.fishkaster.app:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1400d9
com.fishkaster.app:string/default_popup_window_title = 0x7f130069
com.fishkaster.app:attr/titleMarginEnd = 0x7f0404e3
com.fishkaster.app:style/TextAppearance.MaterialComponents.Body1 = 0x7f14022a
com.fishkaster.app:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0e0120
com.fishkaster.app:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0e011a
com.fishkaster.app:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0e0119
com.fishkaster.app:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0e002d
com.fishkaster.app:macro/m3_comp_suggestion_chip_container_shape = 0x7f0e0117
com.fishkaster.app:macro/m3_comp_snackbar_container_shape = 0x7f0e0114
com.fishkaster.app:macro/m3_comp_snackbar_container_color = 0x7f0e0113
com.fishkaster.app:id/BOTTOM_END = 0x7f0a0001
com.fishkaster.app:macro/m3_comp_slider_label_container_color = 0x7f0e0111
com.fishkaster.app:macro/m3_comp_slider_handle_color = 0x7f0e010f
com.fishkaster.app:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0e010e
com.fishkaster.app:macro/m3_comp_slider_disabled_active_track_color = 0x7f0e010c
com.fishkaster.app:color/m3_ref_palette_tertiary40 = 0x7f06016c
com.fishkaster.app:string/range_start = 0x7f13012f
com.fishkaster.app:attr/textEndPadding = 0x7f0404b4
com.fishkaster.app:macro/m3_comp_slider_active_track_color = 0x7f0e010b
com.fishkaster.app:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1400e2
com.fishkaster.app:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0e0109
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f1401b4
com.fishkaster.app:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0e0107
com.fishkaster.app:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0e0104
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0e0103
com.fishkaster.app:id/search_button = 0x7f0a01ed
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0e0102
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0e00fc
com.fishkaster.app:attr/cropShape = 0x7f040181
com.fishkaster.app:color/material_dynamic_neutral40 = 0x7f06024d
com.fishkaster.app:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0e00f9
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f1401ad
com.fishkaster.app:macro/m3_comp_search_view_header_input_text_type = 0x7f0e00f5
com.fishkaster.app:macro/m3_comp_search_view_header_input_text_color = 0x7f0e00f4
com.fishkaster.app:macro/m3_comp_search_view_divider_color = 0x7f0e00f2
com.fishkaster.app:macro/m3_comp_search_bar_supporting_text_type = 0x7f0e00ef
com.fishkaster.app:dimen/design_bottom_navigation_active_text_size = 0x7f070064
com.fishkaster.app:styleable/Capability = 0x7f15001c
com.fishkaster.app:macro/m3_comp_search_bar_supporting_text_color = 0x7f0e00ee
com.fishkaster.app:macro/m3_comp_search_bar_leading_icon_color = 0x7f0e00eb
com.fishkaster.app:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0e00e8
com.fishkaster.app:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0e00e7
com.fishkaster.app:string/item_view_role_description = 0x7f1300b9
com.fishkaster.app:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0e00e5
com.fishkaster.app:string/state_mixed_description = 0x7f130140
com.fishkaster.app:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0e00e3
com.fishkaster.app:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0e00e1
com.fishkaster.app:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0e00e0
com.fishkaster.app:color/background_floating_material_light = 0x7f06001e
com.fishkaster.app:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0e00df
com.fishkaster.app:style/Widget.Material3.Badge.AdjustToBounds = 0x7f14038a
com.fishkaster.app:string/mtrl_picker_range_header_only_start_selected = 0x7f130107
com.fishkaster.app:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0e00dd
com.fishkaster.app:color/m3_ref_palette_error99 = 0x7f060127
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f140157
com.fishkaster.app:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0e00db
com.fishkaster.app:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1400ce
com.fishkaster.app:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0e00d4
com.fishkaster.app:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f1403a0
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0e00d3
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0e00d1
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0e0166
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0e00ce
com.fishkaster.app:id/wide = 0x7f0a0267
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0e00cd
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0e00c9
com.fishkaster.app:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0e00c6
com.fishkaster.app:style/Theme.MaterialComponents.CompactMenu = 0x7f140284
com.fishkaster.app:id/centerCrop = 0x7f0a007d
com.fishkaster.app:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0e00c4
com.fishkaster.app:style/Widget.Material3.Slider.Legacy = 0x7f140415
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1403e7
com.fishkaster.app:drawable/abc_ic_go_search_api_material = 0x7f080041
com.fishkaster.app:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0e00bf
com.fishkaster.app:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1402cf
com.fishkaster.app:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0e00bb
com.fishkaster.app:color/mtrl_navigation_item_text_color = 0x7f0602fe
com.fishkaster.app:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0e00b7
com.fishkaster.app:attr/floatingActionButtonPrimaryStyle = 0x7f040219
com.fishkaster.app:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0e00b6
com.fishkaster.app:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0e00b2
com.fishkaster.app:style/Widget.AppCompat.RatingBar.Indicator = 0x7f140361
com.fishkaster.app:style/ExoMediaButton.Previous = 0x7f14012f
com.fishkaster.app:drawable/user = 0x7f080172
com.fishkaster.app:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0e00ab
com.fishkaster.app:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0e00a7
com.fishkaster.app:styleable/BottomNavigationView = 0x7f150019
com.fishkaster.app:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0e00a0
com.fishkaster.app:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1402e3
com.fishkaster.app:color/material_cursor_color = 0x7f06023c
com.fishkaster.app:macro/m3_comp_navigation_rail_label_text_type = 0x7f0e009f
com.fishkaster.app:id/textinput_counter = 0x7f0a0237
com.fishkaster.app:drawable/exo_styled_controls_previous = 0x7f0800e8
com.fishkaster.app:styleable/MaterialAlertDialog = 0x7f150052
com.fishkaster.app:style/Widget.MaterialComponents.Slider = 0x7f140488
com.fishkaster.app:attr/hintAnimationEnabled = 0x7f04025a
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1403f8
com.fishkaster.app:attr/tabIndicatorGravity = 0x7f040474
com.fishkaster.app:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0e009b
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f070138
com.fishkaster.app:macro/m3_comp_navigation_rail_container_color = 0x7f0e0099
com.fishkaster.app:color/material_dynamic_neutral90 = 0x7f060252
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f14049e
com.fishkaster.app:attr/badgeTextAppearance = 0x7f040065
com.fishkaster.app:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0e0098
com.fishkaster.app:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0e0096
com.fishkaster.app:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f14006d
com.fishkaster.app:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0e0094
com.fishkaster.app:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0e0018
com.fishkaster.app:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0e0093
com.fishkaster.app:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0e0092
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0e008f
com.fishkaster.app:attr/cropTouchRadius = 0x7f040186
com.fishkaster.app:attr/cropBorderCornerLength = 0x7f040168
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0e008e
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0e008c
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0e008b
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0e0088
com.fishkaster.app:macro/m3_comp_navigation_drawer_headline_type = 0x7f0e0085
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f14031b
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0e0083
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0e0082
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0e0080
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0e007d
com.fishkaster.app:attr/enforceMaterialTheme = 0x7f0401db
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0e007b
com.fishkaster.app:attr/chipStandaloneStyle = 0x7f0400d6
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0e0079
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0e0076
com.fishkaster.app:color/error_color_material_light = 0x7f060073
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0e0074
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0e0073
com.fishkaster.app:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0e0068
com.fishkaster.app:attr/motion_triggerOnCollision = 0x7f04037d
com.fishkaster.app:styleable/Navigator = 0x7f150077
com.fishkaster.app:attr/checkedState = 0x7f0400c6
com.fishkaster.app:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0e0064
com.fishkaster.app:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0e0060
com.fishkaster.app:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0e005f
com.fishkaster.app:macro/m3_comp_input_chip_label_text_type = 0x7f0e005c
com.fishkaster.app:attr/layoutDescription = 0x7f0402ae
com.fishkaster.app:color/material_timepicker_clock_text_color = 0x7f0602da
com.fishkaster.app:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f070122
com.fishkaster.app:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0e005a
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f07022b
com.fishkaster.app:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1403c7
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f140319
com.fishkaster.app:layout/material_timepicker = 0x7f0d0055
com.fishkaster.app:macro/m3_comp_icon_button_selected_icon_color = 0x7f0e0059
com.fishkaster.app:macro/m3_comp_filter_chip_label_text_type = 0x7f0e0058
com.fishkaster.app:id/accessibility_order_parent = 0x7f0a003a
com.fishkaster.app:macro/m3_comp_filter_chip_container_shape = 0x7f0e0057
com.fishkaster.app:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0e0054
com.fishkaster.app:macro/m3_comp_filled_tonal_button_container_color = 0x7f0e0052
com.fishkaster.app:string/m3_sys_motion_easing_emphasized = 0x7f1300c0
com.fishkaster.app:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0e0051
com.fishkaster.app:string/mtrl_checkbox_button_icon_path_checked = 0x7f1300e5
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0e0169
com.fishkaster.app:macro/m3_comp_filled_text_field_container_shape = 0x7f0e004c
com.fishkaster.app:macro/m3_comp_filled_text_field_container_color = 0x7f0e004b
com.fishkaster.app:style/TextAppearance.Design.Hint = 0x7f1401ff
com.fishkaster.app:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0e0049
com.fishkaster.app:macro/m3_comp_filled_icon_button_container_color = 0x7f0e0048
com.fishkaster.app:macro/m3_comp_filled_card_container_shape = 0x7f0e0047
com.fishkaster.app:macro/m3_comp_filled_card_container_color = 0x7f0e0046
com.fishkaster.app:attr/show_shuffle_button = 0x7f040422
com.fishkaster.app:style/ThemeOverlay.Material3.NavigationView = 0x7f1402f0
com.fishkaster.app:macro/m3_comp_filled_button_label_text_type = 0x7f0e0045
com.fishkaster.app:dimen/mtrl_tooltip_padding = 0x7f070330
com.fishkaster.app:dimen/mtrl_extended_fab_elevation = 0x7f0702cf
com.fishkaster.app:macro/m3_comp_filled_button_label_text_color = 0x7f0e0044
com.fishkaster.app:attr/bottomNavigationStyle = 0x7f040085
com.fishkaster.app:macro/m3_comp_fab_tertiary_icon_color = 0x7f0e0040
com.fishkaster.app:macro/m3_comp_fab_tertiary_container_color = 0x7f0e003f
com.fishkaster.app:layout/notification_template_big_media = 0x7f0d007c
com.fishkaster.app:macro/m3_comp_fab_surface_icon_color = 0x7f0e003e
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0e00fd
com.fishkaster.app:macro/m3_comp_fab_primary_small_container_shape = 0x7f0e003a
com.fishkaster.app:attr/materialCalendarYearNavigationButton = 0x7f040327
com.fishkaster.app:macro/m3_comp_switch_selected_icon_color = 0x7f0e0129
com.fishkaster.app:macro/m3_comp_fab_primary_icon_color = 0x7f0e0038
com.fishkaster.app:macro/m3_comp_fab_primary_container_shape = 0x7f0e0037
com.fishkaster.app:macro/m3_comp_fab_primary_container_color = 0x7f0e0036
com.fishkaster.app:color/switch_thumb_material_light = 0x7f060329
com.fishkaster.app:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0e0031
com.fishkaster.app:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0e0030
com.fishkaster.app:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0e002e
com.fishkaster.app:macro/m3_comp_elevated_button_container_color = 0x7f0e0029
com.fishkaster.app:style/ShapeAppearance.Material3.Corner.None = 0x7f14019e
com.fishkaster.app:macro/m3_comp_dialog_supporting_text_color = 0x7f0e0026
com.fishkaster.app:macro/m3_comp_dialog_container_color = 0x7f0e0022
com.fishkaster.app:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f080026
com.fishkaster.app:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0e0021
com.fishkaster.app:id/title = 0x7f0a023f
com.fishkaster.app:style/Widget.AppCompat.SearchView = 0x7f140363
com.fishkaster.app:string/exo_item_list = 0x7f130092
com.fishkaster.app:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0e001d
com.fishkaster.app:drawable/abc_cab_background_internal_bg = 0x7f080037
com.fishkaster.app:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0e001a
com.fishkaster.app:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0e0016
com.fishkaster.app:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0e0014
com.fishkaster.app:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0601d6
com.fishkaster.app:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0e0011
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f140025
com.fishkaster.app:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f140082
com.fishkaster.app:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0e000f
com.fishkaster.app:macro/m3_comp_date_picker_modal_container_color = 0x7f0e000d
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f140341
com.fishkaster.app:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0e0008
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1401e7
com.fishkaster.app:color/abc_tint_spinner = 0x7f060017
com.fishkaster.app:macro/m3_comp_badge_large_label_text_type = 0x7f0e0004
com.fishkaster.app:macro/m3_comp_assist_chip_label_text_type = 0x7f0e0001
com.fishkaster.app:attr/navigationViewStyle = 0x7f040386
com.fishkaster.app:color/common_google_signin_btn_text_light = 0x7f06003c
com.fishkaster.app:style/Widget.MaterialComponents.CardView = 0x7f14044e
com.fishkaster.app:layout/redbox_item_title = 0x7f0d0089
com.fishkaster.app:drawable/common_google_signin_btn_text_disabled = 0x7f080097
com.fishkaster.app:attr/tickColorInactive = 0x7f0404d2
com.fishkaster.app:layout/paused_in_debugger_view = 0x7f0d0087
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0e007e
com.fishkaster.app:layout/notification_template_media = 0x7f0d0083
com.fishkaster.app:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1400c4
com.fishkaster.app:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f07025e
com.fishkaster.app:layout/notification_template_lines_media = 0x7f0d0082
com.fishkaster.app:string/call_notification_hang_up_action = 0x7f130029
com.fishkaster.app:layout/notification_template_icon_group = 0x7f0d0081
com.fishkaster.app:style/Theme.AppCompat.DayNight = 0x7f140241
com.fishkaster.app:layout/notification_template_big_media_narrow_custom = 0x7f0d007f
com.fishkaster.app:id/enterAlways = 0x7f0a00c7
com.fishkaster.app:layout/notification_template_big_media_narrow = 0x7f0d007e
com.fishkaster.app:layout/notification_media_cancel_action = 0x7f0d007b
com.fishkaster.app:layout/mtrl_search_view = 0x7f0d0077
com.fishkaster.app:layout/mtrl_search_bar = 0x7f0d0076
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.Display = 0x7f14049f
com.fishkaster.app:layout/mtrl_picker_text_input_date_range = 0x7f0d0075
com.fishkaster.app:layout/mtrl_picker_text_input_date = 0x7f0d0074
com.fishkaster.app:dimen/m3_btn_text_btn_icon_padding_left = 0x7f070104
com.fishkaster.app:plurals/exo_controls_fastforward_by_amount_description = 0x7f110000
com.fishkaster.app:layout/mtrl_picker_header_toggle = 0x7f0d0073
com.fishkaster.app:id/button_text = 0x7f0a0078
com.fishkaster.app:layout/mtrl_picker_header_selection_text = 0x7f0d0071
com.fishkaster.app:layout/mtrl_picker_header_fullscreen = 0x7f0d0070
com.fishkaster.app:style/Theme.Design.BottomSheetDialog = 0x7f14025a
com.fishkaster.app:attr/customBoolean = 0x7f040190
com.fishkaster.app:layout/mtrl_picker_header_dialog = 0x7f0d006f
com.fishkaster.app:layout/mtrl_picker_fullscreen = 0x7f0d006e
com.fishkaster.app:layout/mtrl_picker_actions = 0x7f0d006c
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f14030b
com.fishkaster.app:layout/m3_alert_dialog_actions = 0x7f0d0046
com.fishkaster.app:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1400e9
com.fishkaster.app:layout/mtrl_navigation_rail_item = 0x7f0d006b
com.fishkaster.app:layout/mtrl_calendar_vertical = 0x7f0d0067
com.fishkaster.app:string/material_clock_toggle_content_description = 0x7f1300cc
com.fishkaster.app:layout/mtrl_calendar_horizontal = 0x7f0d0062
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Year = 0x7f1403f1
com.fishkaster.app:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f14009e
com.fishkaster.app:id/mtrl_picker_header_toggle = 0x7f0a0185
com.fishkaster.app:layout/mtrl_calendar_days_of_week = 0x7f0d0061
com.fishkaster.app:id/compress = 0x7f0a0090
com.fishkaster.app:macro/m3_comp_outlined_card_container_shape = 0x7f0e00a9
com.fishkaster.app:xml/library_file_paths = 0x7f160004
com.fishkaster.app:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f140462
com.fishkaster.app:attr/itemRippleColor = 0x7f04028c
com.fishkaster.app:macro/m3_comp_switch_unselected_icon_color = 0x7f0e013a
com.fishkaster.app:layout/material_timepicker_dialog = 0x7f0d0056
com.fishkaster.app:layout/material_time_chip = 0x7f0d0053
com.fishkaster.app:style/TextAppearance.AppCompat.Small = 0x7f1401d6
com.fishkaster.app:layout/material_textinput_timepicker = 0x7f0d0052
com.fishkaster.app:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f140242
com.fishkaster.app:attr/hideAnimationBehavior = 0x7f040253
com.fishkaster.app:layout/material_radial_view_group = 0x7f0d0051
com.fishkaster.app:layout/material_clockface_textview = 0x7f0d004f
com.fishkaster.app:layout/material_clock_period_toggle_land = 0x7f0d004e
com.fishkaster.app:style/Base.V28.Theme.AppCompat.Light = 0x7f1400bc
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0601cf
com.fishkaster.app:layout/m3_side_sheet_dialog = 0x7f0d0049
com.fishkaster.app:layout/m3_alert_dialog_title = 0x7f0d0047
com.fishkaster.app:color/material_personalized_color_surface_bright = 0x7f0602be
com.fishkaster.app:layout/m3_alert_dialog = 0x7f0d0045
com.fishkaster.app:style/Widget.Material3.Toolbar.Surface = 0x7f14042b
com.fishkaster.app:layout/expo_media_controller = 0x7f0d0041
com.fishkaster.app:anim/rns_default_enter_in = 0x7f010032
com.fishkaster.app:layout/exo_styled_settings_list_item = 0x7f0d003e
com.fishkaster.app:layout/exo_styled_settings_list = 0x7f0d003d
com.fishkaster.app:id/rectangles = 0x7f0a01cc
com.fishkaster.app:layout/exo_styled_player_control_view = 0x7f0d003b
com.fishkaster.app:layout/exo_styled_player_control_rewind_button = 0x7f0d003a
com.fishkaster.app:layout/exo_styled_player_control_ffwd_button = 0x7f0d0039
com.fishkaster.app:layout/exo_player_control_view = 0x7f0d0037
com.fishkaster.app:layout/exo_list_divider = 0x7f0d0036
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f14031a
com.fishkaster.app:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f140106
com.fishkaster.app:layout/design_text_input_start_icon = 0x7f0d0034
com.fishkaster.app:layout/design_navigation_menu_item = 0x7f0d0032
com.fishkaster.app:color/m3_sys_color_light_tertiary = 0x7f060212
com.fishkaster.app:style/Theme.FullScreenDialogAnimatedSlide = 0x7f140262
com.fishkaster.app:layout/design_navigation_menu = 0x7f0d0031
com.fishkaster.app:layout/design_navigation_item_subheader = 0x7f0d0030
com.fishkaster.app:color/material_dynamic_primary40 = 0x7f060267
com.fishkaster.app:id/rn_frame_method = 0x7f0a01d5
com.fishkaster.app:layout/design_navigation_item_separator = 0x7f0d002f
com.fishkaster.app:attr/percentX = 0x7f0403ac
com.fishkaster.app:drawable/exo_styled_controls_pause = 0x7f0800e6
com.fishkaster.app:layout/design_navigation_item_header = 0x7f0d002e
com.fishkaster.app:layout/design_layout_snackbar_include = 0x7f0d0029
com.fishkaster.app:layout/design_bottom_navigation_item = 0x7f0d0026
com.fishkaster.app:color/design_error = 0x7f06005e
com.fishkaster.app:layout/custom_dialog = 0x7f0d0025
com.fishkaster.app:attr/rangeFillColor = 0x7f0403d1
com.fishkaster.app:style/Widget.Material3.Button.TonalButton = 0x7f1403a4
com.fishkaster.app:id/ic_rotate_right_24 = 0x7f0a012f
com.fishkaster.app:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1401ce
com.fishkaster.app:styleable/PopupWindow = 0x7f15007c
com.fishkaster.app:layout/browser_actions_context_menu_row = 0x7f0d0022
com.fishkaster.app:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f070172
com.fishkaster.app:menu/crop_image_menu = 0x7f0f0000
com.fishkaster.app:attr/animationMode = 0x7f040039
com.fishkaster.app:layout/browser_actions_context_menu_page = 0x7f0d0021
com.fishkaster.app:attr/actionViewClass = 0x7f040024
com.fishkaster.app:attr/thumbIconTintMode = 0x7f0404c7
com.fishkaster.app:dimen/def_drawer_elevation = 0x7f070060
com.fishkaster.app:layout/amu_info_window = 0x7f0d001d
com.fishkaster.app:layout/abc_search_view = 0x7f0d0019
com.fishkaster.app:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0700d3
com.fishkaster.app:layout/abc_search_dropdown_item_icons_2line = 0x7f0d0018
com.fishkaster.app:attr/shrinkMotionSpec = 0x7f040426
com.fishkaster.app:layout/abc_screen_simple = 0x7f0d0015
com.fishkaster.app:layout/abc_screen_content_include = 0x7f0d0014
com.fishkaster.app:layout/abc_popup_menu_header_item_layout = 0x7f0d0012
com.fishkaster.app:layout/abc_list_menu_item_radio = 0x7f0d0011
com.fishkaster.app:layout/abc_list_menu_item_layout = 0x7f0d0010
com.fishkaster.app:layout/abc_list_menu_item_checkbox = 0x7f0d000e
com.fishkaster.app:layout/abc_cascading_menu_item_layout = 0x7f0d000b
com.fishkaster.app:attr/suffixTextAppearance = 0x7f040460
com.fishkaster.app:style/ThemeOverlay.Material3.Snackbar = 0x7f1402f4
com.fishkaster.app:layout/abc_alert_dialog_material = 0x7f0d0009
com.fishkaster.app:dimen/mtrl_btn_focused_z = 0x7f070284
com.fishkaster.app:layout/abc_activity_chooser_view_list_item = 0x7f0d0007
com.fishkaster.app:dimen/m3_badge_with_text_vertical_padding = 0x7f0700e1
com.fishkaster.app:layout/abc_activity_chooser_view = 0x7f0d0006
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f140026
com.fishkaster.app:layout/abc_action_mode_bar = 0x7f0d0004
com.fishkaster.app:layout/abc_action_menu_item_layout = 0x7f0d0002
com.fishkaster.app:style/AlertDialog.AppCompat = 0x7f140000
com.fishkaster.app:layout/abc_action_bar_up_container = 0x7f0d0001
com.fishkaster.app:style/Widget.MaterialComponents.MaterialDivider = 0x7f14047b
com.fishkaster.app:color/material_dynamic_secondary90 = 0x7f060279
com.fishkaster.app:string/mtrl_picker_out_of_range = 0x7f130105
com.fishkaster.app:layout/abc_action_bar_title_item = 0x7f0d0000
com.fishkaster.app:id/action_bar_activity_content = 0x7f0a0041
com.fishkaster.app:interpolator/mtrl_linear = 0x7f0c0010
com.fishkaster.app:interpolator/mtrl_fast_out_slow_in = 0x7f0c000f
com.fishkaster.app:string/mtrl_switch_thumb_path_name = 0x7f13011b
com.fishkaster.app:interpolator/mtrl_fast_out_linear_in = 0x7f0c000e
com.fishkaster.app:interpolator/m3_sys_motion_easing_standard = 0x7f0c000b
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f140292
com.fishkaster.app:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003
com.fishkaster.app:style/TextAppearance.MaterialComponents.Headline4 = 0x7f140232
com.fishkaster.app:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002
com.fishkaster.app:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001
com.fishkaster.app:attr/thickness = 0x7f0404c0
com.fishkaster.app:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000
com.fishkaster.app:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f14043f
com.fishkaster.app:integer/status_bar_notification_info_maxnum = 0x7f0b0047
com.fishkaster.app:integer/show_password_duration = 0x7f0b0046
com.fishkaster.app:id/honorRequest = 0x7f0a0129
com.fishkaster.app:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f14026c
com.fishkaster.app:integer/mtrl_view_gone = 0x7f0b0042
com.fishkaster.app:drawable/exo_ic_check = 0x7f0800b5
com.fishkaster.app:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f14048a
com.fishkaster.app:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0b0041
com.fishkaster.app:integer/mtrl_switch_track_viewport_height = 0x7f0b003f
com.fishkaster.app:string/exo_controls_cc_enabled_description = 0x7f130070
com.fishkaster.app:integer/mtrl_switch_thumb_viewport_size = 0x7f0b003e
com.fishkaster.app:style/ExoMediaButton.VR = 0x7f140131
com.fishkaster.app:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0b003d
com.fishkaster.app:integer/mtrl_switch_thumb_pressed_duration = 0x7f0b003c
com.fishkaster.app:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0b003b
com.fishkaster.app:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0b003a
com.fishkaster.app:color/m3_ref_palette_dynamic_primary50 = 0x7f0600fa
com.fishkaster.app:integer/mtrl_chip_anim_duration = 0x7f0b0038
com.fishkaster.app:integer/mtrl_card_anim_duration_ms = 0x7f0b0037
com.fishkaster.app:integer/mtrl_calendar_year_selector_span = 0x7f0b0035
com.fishkaster.app:integer/mtrl_btn_anim_duration_ms = 0x7f0b0032
com.fishkaster.app:drawable/$m3_avd_hide_password__0 = 0x7f080006
com.fishkaster.app:dimen/m3_chip_dragged_translation_z = 0x7f07011c
com.fishkaster.app:integer/material_motion_duration_medium_2 = 0x7f0b002c
com.fishkaster.app:integer/m3_sys_shape_corner_small_corner_family = 0x7f0b0028
com.fishkaster.app:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0b0027
com.fishkaster.app:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0e0143
com.fishkaster.app:integer/m3_sys_shape_corner_full_corner_family = 0x7f0b0025
com.fishkaster.app:attr/passwordToggleTintMode = 0x7f0403a7
com.fishkaster.app:integer/m3_sys_motion_duration_short2 = 0x7f0b001f
com.fishkaster.app:integer/m3_sys_motion_duration_short1 = 0x7f0b001e
com.fishkaster.app:id/accessibility_custom_action_18 = 0x7f0a0020
com.fishkaster.app:integer/m3_sys_motion_duration_medium1 = 0x7f0b001a
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f14003f
com.fishkaster.app:style/Widget.Material3.CollapsingToolbar = 0x7f1403be
com.fishkaster.app:integer/react_native_dev_server_port = 0x7f0b0045
com.fishkaster.app:integer/m3_sys_motion_duration_long4 = 0x7f0b0019
com.fishkaster.app:integer/m3_sys_motion_duration_extra_long4 = 0x7f0b0015
com.fishkaster.app:integer/m3_sys_motion_duration_extra_long3 = 0x7f0b0014
com.fishkaster.app:integer/m3_sys_motion_duration_extra_long2 = 0x7f0b0013
com.fishkaster.app:integer/m3_chip_anim_duration = 0x7f0b0011
com.fishkaster.app:integer/m3_card_anim_delay_ms = 0x7f0b000f
com.fishkaster.app:attr/autofillInlineSuggestionEndIconStyle = 0x7f04004d
com.fishkaster.app:integer/m3_btn_anim_delay_ms = 0x7f0b000d
com.fishkaster.app:integer/hide_password_duration = 0x7f0b000b
com.fishkaster.app:integer/exo_media_button_opacity_percentage_enabled = 0x7f0b0009
com.fishkaster.app:integer/exo_media_button_opacity_percentage_disabled = 0x7f0b0008
com.fishkaster.app:integer/cancel_button_image_alpha = 0x7f0b0004
com.fishkaster.app:integer/bottom_sheet_slide_duration = 0x7f0b0003
com.fishkaster.app:integer/app_bar_elevation_anim_duration = 0x7f0b0002
com.fishkaster.app:integer/abc_config_activityDefaultDur = 0x7f0b0000
com.fishkaster.app:id/zoom = 0x7f0a026f
com.fishkaster.app:attr/switchStyle = 0x7f040468
com.fishkaster.app:id/wrapped_composition_tag = 0x7f0a026e
com.fishkaster.app:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f14045b
com.fishkaster.app:id/masked = 0x7f0a0150
com.fishkaster.app:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1400d7
com.fishkaster.app:id/wrap_content = 0x7f0a026d
com.fishkaster.app:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0a0260
com.fishkaster.app:string/common_google_play_services_enable_text = 0x7f130054
com.fishkaster.app:id/withinBounds = 0x7f0a026b
com.fishkaster.app:id/with_icon = 0x7f0a026a
com.fishkaster.app:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1402fa
com.fishkaster.app:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0e0013
com.fishkaster.app:id/webview = 0x7f0a0265
com.fishkaster.app:id/visible_removing_fragment_view_tag = 0x7f0a0264
com.fishkaster.app:style/Widget.MaterialComponents.Button = 0x7f140442
com.fishkaster.app:id/view_tag_instance_handle = 0x7f0a025c
com.fishkaster.app:id/standard = 0x7f0a0211
com.fishkaster.app:id/view_offset_helper = 0x7f0a025b
com.fishkaster.app:dimen/mtrl_tooltip_minWidth = 0x7f07032f
com.fishkaster.app:id/beginning = 0x7f0a006d
com.fishkaster.app:id/view_clipped = 0x7f0a025a
com.fishkaster.app:id/up = 0x7f0a0256
com.fishkaster.app:color/m3_sys_color_dynamic_dark_primary = 0x7f0601b5
com.fishkaster.app:id/transition_transform = 0x7f0a0251
com.fishkaster.app:id/transition_scene_layoutid_cache = 0x7f0a0250
com.fishkaster.app:layout/redbox_view = 0x7f0d008a
com.fishkaster.app:id/transition_pause_alpha = 0x7f0a024e
com.fishkaster.app:id/transition_current_scene = 0x7f0a024b
com.fishkaster.app:string/material_timepicker_clock_mode_description = 0x7f1300db
com.fishkaster.app:id/transform_origin = 0x7f0a0247
com.fishkaster.app:id/toggle = 0x7f0a0242
com.fishkaster.app:layout/abc_screen_toolbar = 0x7f0d0017
com.fishkaster.app:color/m3_ref_palette_error90 = 0x7f060125
com.fishkaster.app:integer/m3_sys_motion_duration_medium4 = 0x7f0b001d
com.fishkaster.app:id/title_template = 0x7f0a0241
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary50 = 0x7f060107
com.fishkaster.app:style/Animation.Material3.SideSheetDialog.Right = 0x7f14000b
com.fishkaster.app:id/titleDividerNoCustom = 0x7f0a0240
com.fishkaster.app:id/time = 0x7f0a023e
com.fishkaster.app:id/texture_view = 0x7f0a023d
com.fishkaster.app:color/m3_text_button_foreground_color_selector = 0x7f060227
com.fishkaster.app:id/textinput_prefix_text = 0x7f0a023b
com.fishkaster.app:style/Widget.MaterialComponents.ActionMode = 0x7f140431
com.fishkaster.app:id/textinput_error = 0x7f0a0238
com.fishkaster.app:string/abc_activitychooserview_choose_application = 0x7f130005
com.fishkaster.app:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0d005d
com.fishkaster.app:color/m3_hint_foreground = 0x7f0600b3
com.fishkaster.app:id/text_input_end_icon = 0x7f0a0234
com.fishkaster.app:styleable/Snackbar = 0x7f15008f
com.fishkaster.app:string/catalyst_report_button = 0x7f130048
com.fishkaster.app:id/textTop = 0x7f0a0233
com.fishkaster.app:id/textStart = 0x7f0a0232
com.fishkaster.app:id/textSpacerNoButtons = 0x7f0a0230
com.fishkaster.app:string/bottomsheet_action_expand = 0x7f130022
com.fishkaster.app:id/textEnd = 0x7f0a022f
com.fishkaster.app:drawable/ic_arrow_back_24 = 0x7f0800fa
com.fishkaster.app:id/text = 0x7f0a022d
com.fishkaster.app:id/terrain = 0x7f0a022c
com.fishkaster.app:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0e0033
com.fishkaster.app:id/tag_state_description = 0x7f0a0227
com.fishkaster.app:id/tag_accessibility_clickable_spans = 0x7f0a0220
com.fishkaster.app:style/TextAppearance.MaterialComponents.Headline6 = 0x7f140234
com.fishkaster.app:id/tag_accessibility_actions = 0x7f0a021f
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0e0155
com.fishkaster.app:id/tabMode = 0x7f0a021e
com.fishkaster.app:macro/m3_comp_dialog_supporting_text_type = 0x7f0e0027
com.fishkaster.app:id/surface_view = 0x7f0a021d
com.fishkaster.app:color/material_personalized_color_surface_inverse = 0x7f0602c5
com.fishkaster.app:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f070198
com.fishkaster.app:id/stop = 0x7f0a0219
com.fishkaster.app:id/staticPostLayout = 0x7f0a0217
com.fishkaster.app:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0e00e2
com.fishkaster.app:id/startToEnd = 0x7f0a0214
com.fishkaster.app:id/src_in = 0x7f0a020f
com.fishkaster.app:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.fishkaster.app:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0e0042
com.fishkaster.app:dimen/disabled_alpha_material_light = 0x7f070092
com.fishkaster.app:id/src_atop = 0x7f0a020e
com.fishkaster.app:string/mtrl_picker_text_input_date_hint = 0x7f13010d
com.fishkaster.app:id/spread_inside = 0x7f0a020c
com.fishkaster.app:color/m3_ref_palette_neutral92 = 0x7f06013a
com.fishkaster.app:dimen/mtrl_slider_track_side_padding = 0x7f070314
com.fishkaster.app:id/spread = 0x7f0a020b
com.fishkaster.app:id/split_action_bar = 0x7f0a020a
com.fishkaster.app:id/spacer = 0x7f0a0206
com.fishkaster.app:id/snapMargins = 0x7f0a0205
com.fishkaster.app:id/snap = 0x7f0a0204
com.fishkaster.app:id/snackbar_text = 0x7f0a0203
com.fishkaster.app:dimen/m3_btn_icon_only_icon_padding = 0x7f0700fb
com.fishkaster.app:id/autoComplete = 0x7f0a0063
com.fishkaster.app:id/snackbar_action = 0x7f0a0202
com.fishkaster.app:drawable/refresh_round_icon = 0x7f080165
com.fishkaster.app:string/abc_searchview_description_submit = 0x7f130016
com.fishkaster.app:id/slide = 0x7f0a0201
com.fishkaster.app:id/skip_previous_button = 0x7f0a0200
com.fishkaster.app:attr/cropBackgroundColor = 0x7f040166
com.fishkaster.app:style/Base.Widget.Material3.FloatingActionButton = 0x7f14010b
com.fishkaster.app:id/skipCollapsed = 0x7f0a01fe
com.fishkaster.app:id/showTitle = 0x7f0a01fc
com.fishkaster.app:id/showCustom = 0x7f0a01fa
com.fishkaster.app:id/search_close_btn = 0x7f0a01ee
com.fishkaster.app:style/TextAppearance.Design.Placeholder = 0x7f140200
com.fishkaster.app:string/mtrl_checkbox_state_description_checked = 0x7f1300ed
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0e00cc
com.fishkaster.app:id/search_badge = 0x7f0a01eb
com.fishkaster.app:string/default_error_message = 0x7f130067
com.fishkaster.app:id/scrollIndicatorUp = 0x7f0a01e8
com.fishkaster.app:id/screen = 0x7f0a01e5
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f070229
com.fishkaster.app:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1402db
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary70 = 0x7f060116
com.fishkaster.app:id/sawtooth = 0x7f0a01e3
com.fishkaster.app:id/match_parent = 0x7f0a0151
com.fishkaster.app:id/satellite = 0x7f0a01e0
com.fishkaster.app:id/rounded = 0x7f0a01de
com.fishkaster.app:styleable/LinearLayoutCompat_Layout = 0x7f15004d
com.fishkaster.app:drawable/abc_btn_radio_material_anim = 0x7f080032
com.fishkaster.app:id/rn_redbox_report_label = 0x7f0a01db
com.fishkaster.app:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1403c0
com.fishkaster.app:id/rn_redbox_report_button = 0x7f0a01da
com.fishkaster.app:id/rn_redbox_line_separator = 0x7f0a01d7
com.fishkaster.app:color/material_dynamic_neutral_variant10 = 0x7f060256
com.fishkaster.app:id/rn_redbox_dismiss_button = 0x7f0a01d6
com.fishkaster.app:id/rightToLeft = 0x7f0a01d1
com.fishkaster.app:id/report_drawn = 0x7f0a01cd
com.fishkaster.app:id/rectangleHorizontalOnly = 0x7f0a01ca
com.fishkaster.app:attr/shutter_background_color = 0x7f040427
com.fishkaster.app:id/rectangle = 0x7f0a01c9
com.fishkaster.app:id/ratio = 0x7f0a01c7
com.fishkaster.app:id/radio = 0x7f0a01c6
com.fishkaster.app:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f14048f
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Item = 0x7f1403ee
com.fishkaster.app:id/postLayout = 0x7f0a01c2
com.fishkaster.app:id/pooling_container_listener_holder_tag = 0x7f0a01c0
com.fishkaster.app:id/play_button = 0x7f0a01be
com.fishkaster.app:attr/defaultQueryHint = 0x7f0401a1
com.fishkaster.app:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f140338
com.fishkaster.app:id/pin = 0x7f0a01bd
com.fishkaster.app:id/percent = 0x7f0a01bb
com.fishkaster.app:id/peekHeight = 0x7f0a01ba
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f14031d
com.fishkaster.app:id/pathRelative = 0x7f0a01b9
com.fishkaster.app:style/MaterialAlertDialog.Material3.Animation = 0x7f14014c
com.fishkaster.app:string/pick_image_camera = 0x7f130129
com.fishkaster.app:attr/cropGuidelinesThickness = 0x7f040175
com.fishkaster.app:id/path = 0x7f0a01b8
com.fishkaster.app:style/Base.TextAppearance.Material3.Search = 0x7f140046
com.fishkaster.app:id/password_toggle = 0x7f0a01b7
com.fishkaster.app:id/parent_matrix = 0x7f0a01b6
com.fishkaster.app:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f070197
com.fishkaster.app:id/parentRelative = 0x7f0a01b5
com.fishkaster.app:id/parentPanel = 0x7f0a01b4
com.fishkaster.app:attr/badgeWidth = 0x7f040069
com.fishkaster.app:attr/backgroundColor = 0x7f040053
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0e015a
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface_container = 0x7f0601dd
com.fishkaster.app:id/parallax = 0x7f0a01b2
com.fishkaster.app:id/packed = 0x7f0a01b1
com.fishkaster.app:id/outline = 0x7f0a01ae
com.fishkaster.app:dimen/mtrl_high_ripple_default_alpha = 0x7f0702e0
com.fishkaster.app:id/open_search_view_divider = 0x7f0a01a3
com.fishkaster.app:id/open_search_view_clear_button = 0x7f0a01a1
com.fishkaster.app:style/Base.Widget.AppCompat.EditText = 0x7f1400de
com.fishkaster.app:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f080022
com.fishkaster.app:integer/mtrl_btn_anim_delay_ms = 0x7f0b0031
com.fishkaster.app:id/accelerate = 0x7f0a0011
com.fishkaster.app:id/open_search_view_background = 0x7f0a01a0
com.fishkaster.app:id/onTouch = 0x7f0a019d
com.fishkaster.app:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f140369
com.fishkaster.app:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1402ed
com.fishkaster.app:id/off = 0x7f0a019b
com.fishkaster.app:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1402c3
com.fishkaster.app:id/normal = 0x7f0a0197
com.fishkaster.app:drawable/m3_avd_hide_password = 0x7f080117
com.fishkaster.app:id/never = 0x7f0a0194
com.fishkaster.app:id/navigation_bar_item_small_label_view = 0x7f0a0192
com.fishkaster.app:id/navigation_bar_item_icon_container = 0x7f0a018e
com.fishkaster.app:id/multiply = 0x7f0a018b
com.fishkaster.app:id/mtrl_picker_text_input_range_start = 0x7f0a0188
com.fishkaster.app:id/light = 0x7f0a0148
com.fishkaster.app:style/Theme.FullScreenDialog = 0x7f140260
com.fishkaster.app:id/mtrl_picker_header_selection_text = 0x7f0a0183
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f140305
com.fishkaster.app:id/mtrl_picker_header = 0x7f0a0182
com.fishkaster.app:id/mtrl_motion_snapshot_view = 0x7f0a0180
com.fishkaster.app:id/exo_progress = 0x7f0a00ec
com.fishkaster.app:id/mtrl_child_content_container = 0x7f0a017e
com.fishkaster.app:id/mtrl_calendar_year_selector_frame = 0x7f0a017c
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600e4
com.fishkaster.app:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0e0019
com.fishkaster.app:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f140448
com.fishkaster.app:id/mtrl_calendar_text_input_frame = 0x7f0a017b
com.fishkaster.app:id/mtrl_calendar_selection_frame = 0x7f0a017a
com.fishkaster.app:layout/crop_image_view = 0x7f0d0024
com.fishkaster.app:id/mtrl_calendar_day_selector_frame = 0x7f0a0175
com.fishkaster.app:layout/mtrl_calendar_months = 0x7f0d0066
com.fishkaster.app:id/mtrl_anchor_parent = 0x7f0a0174
com.fishkaster.app:id/month_navigation_next = 0x7f0a0170
com.fishkaster.app:id/month_grid = 0x7f0a016d
com.fishkaster.app:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0e00da
com.fishkaster.app:style/TextAppearance.MaterialComponents.Tooltip = 0x7f140239
com.fishkaster.app:id/mix_blend_mode = 0x7f0a016c
com.fishkaster.app:id/mini = 0x7f0a016b
com.fishkaster.app:id/message = 0x7f0a0169
com.fishkaster.app:id/media_actions = 0x7f0a0167
com.fishkaster.app:id/matrix = 0x7f0a0166
com.fishkaster.app:color/bright_foreground_inverse_material_dark = 0x7f060023
com.fishkaster.app:color/design_icon_tint = 0x7f060066
com.fishkaster.app:color/dim_foreground_disabled_material_light = 0x7f06006f
com.fishkaster.app:id/material_value_index = 0x7f0a0165
com.fishkaster.app:dimen/abc_control_padding_material = 0x7f07001a
com.fishkaster.app:id/material_timepicker_ok_button = 0x7f0a0163
com.fishkaster.app:attr/materialIconButtonFilledStyle = 0x7f040331
com.fishkaster.app:id/material_timepicker_container = 0x7f0a0161
com.fishkaster.app:attr/buttonBarStyle = 0x7f04009a
com.fishkaster.app:id/material_minute_text_input = 0x7f0a015d
com.fishkaster.app:style/TextAppearance.MaterialComponents.Headline1 = 0x7f14022f
com.fishkaster.app:id/material_label = 0x7f0a015c
com.fishkaster.app:color/m3_ref_palette_neutral_variant100 = 0x7f060142
com.fishkaster.app:id/material_hour_tv = 0x7f0a015b
com.fishkaster.app:id/material_hour_text_input = 0x7f0a015a
com.fishkaster.app:id/material_clock_period_am_button = 0x7f0a0157
com.fishkaster.app:id/material_clock_level = 0x7f0a0156
com.fishkaster.app:id/material_clock_hand = 0x7f0a0155
com.fishkaster.app:id/material_clock_display = 0x7f0a0152
com.fishkaster.app:id/marquee = 0x7f0a014f
com.fishkaster.app:id/ImageView_image = 0x7f0a0007
com.fishkaster.app:string/mtrl_picker_text_input_date_range_start_hint = 0x7f13010f
com.fishkaster.app:color/m3_ref_palette_neutral_variant10 = 0x7f060141
com.fishkaster.app:id/listMode = 0x7f0a014c
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1401ed
com.fishkaster.app:attr/materialIconButtonStyle = 0x7f040334
com.fishkaster.app:id/linear = 0x7f0a014b
com.fishkaster.app:style/TextAppearance.Material3.DisplayLarge = 0x7f140219
com.fishkaster.app:id/line1 = 0x7f0a0149
com.fishkaster.app:color/vector_tint_theme_color = 0x7f06032f
com.fishkaster.app:id/legacy = 0x7f0a0147
com.fishkaster.app:style/TextAppearance.Compat.Notification.Info.Media = 0x7f1401f2
com.fishkaster.app:attr/motionProgress = 0x7f040379
com.fishkaster.app:id/tag_transition_group = 0x7f0a0228
com.fishkaster.app:color/m3_sys_color_dark_surface_container_lowest = 0x7f06019d
com.fishkaster.app:id/leftToRight = 0x7f0a0146
com.fishkaster.app:attr/itemTextAppearanceActive = 0x7f040298
com.fishkaster.app:id/date_picker_actions = 0x7f0a00a2
com.fishkaster.app:id/labelled_by = 0x7f0a0143
com.fishkaster.app:attr/tabUnboundedRipple = 0x7f040486
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0600ee
com.fishkaster.app:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f14041d
com.fishkaster.app:string/mtrl_switch_track_decoration_path = 0x7f13011e
com.fishkaster.app:id/jumpToStart = 0x7f0a0141
com.fishkaster.app:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.fishkaster.app:dimen/design_navigation_elevation = 0x7f070078
com.fishkaster.app:id/is_pooling_container_tag = 0x7f0a013d
com.fishkaster.app:id/inward = 0x7f0a013c
com.fishkaster.app:integer/m3_sys_motion_path = 0x7f0b0022
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f140466
com.fishkaster.app:id/inspection_slot_table_set = 0x7f0a0139
com.fishkaster.app:id/info = 0x7f0a0138
com.fishkaster.app:id/indeterminate = 0x7f0a0137
com.fishkaster.app:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f140432
com.fishkaster.app:id/image = 0x7f0a0136
com.fishkaster.app:style/Platform.MaterialComponents = 0x7f140162
com.fishkaster.app:id/ignoreRequest = 0x7f0a0135
com.fishkaster.app:id/ifRoom = 0x7f0a0133
com.fishkaster.app:style/ExoStyledControls.Button.Center.FfwdWithAmount = 0x7f140140
com.fishkaster.app:id/icon_group = 0x7f0a0131
com.fishkaster.app:id/icon = 0x7f0a0130
com.fishkaster.app:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1403da
com.fishkaster.app:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0e0138
com.fishkaster.app:id/ic_rotate_left_24 = 0x7f0a012e
com.fishkaster.app:id/homeAsUp = 0x7f0a0128
com.fishkaster.app:id/hideable = 0x7f0a0126
com.fishkaster.app:attr/badgeRadius = 0x7f040060
com.fishkaster.app:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f130114
com.fishkaster.app:string/exo_track_unknown = 0x7f1300a3
com.fishkaster.app:style/Widget.MaterialComponents.Chip.Choice = 0x7f140451
com.fishkaster.app:style/Widget.Material3.Button.ElevatedButton = 0x7f140395
com.fishkaster.app:id/hide_in_inspector_tag = 0x7f0a0125
com.fishkaster.app:id/hide_ime_id = 0x7f0a0124
com.fishkaster.app:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f140456
com.fishkaster.app:id/group_divider = 0x7f0a0120
com.fishkaster.app:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0c0009
com.fishkaster.app:id/graph = 0x7f0a011e
com.fishkaster.app:attr/crossfade = 0x7f04018a
com.fishkaster.app:id/fullscreen_mode_button = 0x7f0a0119
com.fishkaster.app:id/fullscreen_header = 0x7f0a0118
com.fishkaster.app:id/fragment_container_view_tag = 0x7f0a0117
com.fishkaster.app:id/fps_text = 0x7f0a0116
com.fishkaster.app:id/forever = 0x7f0a0115
com.fishkaster.app:id/transition_image_transform = 0x7f0a024c
com.fishkaster.app:id/focusCrop = 0x7f0a0114
com.fishkaster.app:string/mtrl_picker_text_input_year_abbr = 0x7f130112
com.fishkaster.app:id/spherical_gl_surface_view = 0x7f0a0208
com.fishkaster.app:id/fitXY = 0x7f0a010e
com.fishkaster.app:id/fitToContents = 0x7f0a010d
com.fishkaster.app:id/fitEnd = 0x7f0a010b
com.fishkaster.app:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1402c7
com.fishkaster.app:attr/titleTextStyle = 0x7f0404eb
com.fishkaster.app:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0b0023
com.fishkaster.app:style/Widget.Material3.Chip.Assist = 0x7f1403ab
com.fishkaster.app:drawable/googleg_standard_color_18 = 0x7f0800f8
com.fishkaster.app:dimen/design_fab_image_size = 0x7f070073
com.fishkaster.app:id/fitCenter = 0x7f0a010a
com.fishkaster.app:id/fill_vertical = 0x7f0a0105
com.fishkaster.app:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f07012f
com.fishkaster.app:color/material_dynamic_secondary20 = 0x7f060272
com.fishkaster.app:id/exo_vr = 0x7f0a00fb
com.fishkaster.app:id/exo_track_selection_view = 0x7f0a00fa
com.fishkaster.app:color/m3_default_color_secondary_text = 0x7f0600a1
com.fishkaster.app:id/exo_time = 0x7f0a00f9
com.fishkaster.app:styleable/AnimatedStateListDrawableItem = 0x7f150009
com.fishkaster.app:id/exo_text = 0x7f0a00f8
com.fishkaster.app:id/exo_subtitles = 0x7f0a00f7
com.fishkaster.app:id/exo_sub_text = 0x7f0a00f5
com.fishkaster.app:id/left = 0x7f0a0145
com.fishkaster.app:attr/checkMarkCompat = 0x7f0400b9
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1403d6
com.fishkaster.app:id/exo_shutter = 0x7f0a00f4
com.fishkaster.app:id/exo_shuffle = 0x7f0a00f3
com.fishkaster.app:font/jetbrains_mono_medium = 0x7f090005
com.fishkaster.app:id/scroll = 0x7f0a01e6
com.fishkaster.app:attr/materialButtonStyle = 0x7f040317
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral96 = 0x7f0600d9
com.fishkaster.app:dimen/m3_comp_sheet_side_docked_container_width = 0x7f0701a4
com.fishkaster.app:id/exo_settings_listview = 0x7f0a00f2
com.fishkaster.app:dimen/mtrl_shape_corner_size_small_component = 0x7f07030a
com.fishkaster.app:id/exo_rew = 0x7f0a00ef
com.fishkaster.app:id/exo_progress_placeholder = 0x7f0a00ed
com.fishkaster.app:anim/rns_no_animation_250 = 0x7f010043
com.fishkaster.app:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0e0137
com.fishkaster.app:id/exo_prev = 0x7f0a00eb
com.fishkaster.app:id/exo_playback_speed = 0x7f0a00e9
com.fishkaster.app:id/exo_play_pause = 0x7f0a00e8
com.fishkaster.app:id/exo_play = 0x7f0a00e7
com.fishkaster.app:anim/catalyst_slide_up = 0x7f01001d
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f140035
com.fishkaster.app:id/exo_pause = 0x7f0a00e6
com.fishkaster.app:id/exo_overflow_show = 0x7f0a00e4
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1403d3
com.fishkaster.app:id/exo_minimal_fullscreen = 0x7f0a00e1
com.fishkaster.app:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f140425
com.fishkaster.app:attr/colorSurfaceContainerLowest = 0x7f04012c
com.fishkaster.app:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f14011b
com.fishkaster.app:id/notification_background = 0x7f0a0198
com.fishkaster.app:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080043
com.fishkaster.app:id/exo_icon = 0x7f0a00de
com.fishkaster.app:color/material_on_background_emphasis_high_type = 0x7f060295
com.fishkaster.app:id/exo_ffwd = 0x7f0a00db
com.fishkaster.app:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f070155
com.fishkaster.app:id/exo_error_message = 0x7f0a00d8
com.fishkaster.app:id/exo_controller = 0x7f0a00d4
com.fishkaster.app:id/exo_check = 0x7f0a00d2
com.fishkaster.app:id/exo_buffering = 0x7f0a00d0
com.fishkaster.app:id/accessibility_custom_action_5 = 0x7f0a0031
com.fishkaster.app:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0e0131
com.fishkaster.app:id/exo_basic_controls = 0x7f0a00ce
com.fishkaster.app:id/exo_artwork = 0x7f0a00cc
com.fishkaster.app:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402f7
com.fishkaster.app:id/open_search_view_header_container = 0x7f0a01a6
com.fishkaster.app:id/exo_ad_overlay = 0x7f0a00cb
com.fishkaster.app:color/m3_ref_palette_neutral50 = 0x7f060133
com.fishkaster.app:id/escape = 0x7f0a00c9
com.fishkaster.app:color/m3_sys_color_dark_on_primary_container = 0x7f06018a
com.fishkaster.app:id/fixed_width = 0x7f0a0111
com.fishkaster.app:id/enterAlwaysCollapsed = 0x7f0a00c8
com.fishkaster.app:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0e0174
com.fishkaster.app:id/endToStart = 0x7f0a00c4
com.fishkaster.app:id/end = 0x7f0a00c3
com.fishkaster.app:id/elastic = 0x7f0a00c1
com.fishkaster.app:id/edit_query = 0x7f0a00bf
com.fishkaster.app:color/material_dynamic_secondary60 = 0x7f060276
com.fishkaster.app:id/dropdown_menu = 0x7f0a00ba
com.fishkaster.app:id/pressed = 0x7f0a01c3
com.fishkaster.app:id/dragUp = 0x7f0a00b9
com.fishkaster.app:id/selected = 0x7f0a01f7
com.fishkaster.app:string/mtrl_picker_today_description = 0x7f130113
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0e0072
com.fishkaster.app:id/dragStart = 0x7f0a00b8
com.fishkaster.app:attr/textAppearanceBodySmall = 0x7f040491
com.fishkaster.app:id/dragEnd = 0x7f0a00b5
com.fishkaster.app:id/dragDown = 0x7f0a00b4
com.fishkaster.app:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0e00ad
com.fishkaster.app:id/disjoint = 0x7f0a00b3
com.fishkaster.app:id/disableScroll = 0x7f0a00b2
com.fishkaster.app:attr/colorOutline = 0x7f040116
com.fishkaster.app:string/exo_controls_previous_description = 0x7f13007c
com.fishkaster.app:attr/tabStyle = 0x7f040483
com.fishkaster.app:color/material_on_surface_emphasis_medium = 0x7f06029c
com.fishkaster.app:id/edge = 0x7f0a00be
com.fishkaster.app:id/disablePostScroll = 0x7f0a00b1
com.fishkaster.app:color/material_dynamic_color_dark_on_error_container = 0x7f060243
com.fishkaster.app:styleable/ShapeableImageView = 0x7f15008a
com.fishkaster.app:id/disableHome = 0x7f0a00b0
com.fishkaster.app:id/direct = 0x7f0a00af
com.fishkaster.app:id/dimensions = 0x7f0a00ae
com.fishkaster.app:color/m3_ref_palette_dynamic_primary90 = 0x7f0600fe
com.fishkaster.app:style/Widget.MaterialComponents.Chip.Filter = 0x7f140453
com.fishkaster.app:id/dialog_button = 0x7f0a00ad
com.fishkaster.app:id/design_menu_item_text = 0x7f0a00ab
com.fishkaster.app:id/design_menu_item_action_area = 0x7f0a00a9
com.fishkaster.app:id/decelerateAndComplete = 0x7f0a00a4
com.fishkaster.app:id/decelerate = 0x7f0a00a3
com.fishkaster.app:id/when_playing = 0x7f0a0266
com.fishkaster.app:id/cut = 0x7f0a00a0
com.fishkaster.app:id/customPanel = 0x7f0a009f
com.fishkaster.app:id/custom = 0x7f0a009e
com.fishkaster.app:styleable/NavHost = 0x7f150071
com.fishkaster.app:style/Widget.MaterialComponents.BottomAppBar = 0x7f14043a
com.fishkaster.app:id/current_time_text = 0x7f0a009d
com.fishkaster.app:id/crop_image_menu_crop = 0x7f0a009c
com.fishkaster.app:styleable/LinearProgressIndicator = 0x7f15004e
com.fishkaster.app:id/cropImageView = 0x7f0a009b
com.fishkaster.app:style/Base.Widget.AppCompat.ProgressBar = 0x7f1400f0
com.fishkaster.app:string/exo_controls_rewind_description = 0x7f130080
com.fishkaster.app:macro/m3_comp_search_view_docked_container_shape = 0x7f0e00f3
com.fishkaster.app:layout/exo_styled_player_view = 0x7f0d003c
com.fishkaster.app:id/counterclockwise = 0x7f0a0099
com.fishkaster.app:style/Widget.Material3.Snackbar.TextView = 0x7f140419
com.fishkaster.app:id/cos = 0x7f0a0098
com.fishkaster.app:id/coordinator = 0x7f0a0097
com.fishkaster.app:id/contentPanel = 0x7f0a0095
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar = 0x7f140463
com.fishkaster.app:id/content = 0x7f0a0094
com.fishkaster.app:id/consume_window_insets_tag = 0x7f0a0092
com.fishkaster.app:style/Widget.Material3.PopupMenu = 0x7f140404
com.fishkaster.app:id/compose_view_saveable_id_tag = 0x7f0a008f
com.fishkaster.app:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f1402a9
com.fishkaster.app:layout/mtrl_alert_select_dialog_item = 0x7f0d005b
com.fishkaster.app:id/coil3_request_manager = 0x7f0a008b
com.fishkaster.app:id/circle_center = 0x7f0a0086
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f140210
com.fishkaster.app:id/checked = 0x7f0a0084
com.fishkaster.app:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1402f1
com.fishkaster.app:id/chains = 0x7f0a0082
com.fishkaster.app:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f07015a
com.fishkaster.app:style/TextAppearance.Material3.HeadlineSmall = 0x7f14021e
com.fishkaster.app:id/center_vertical = 0x7f0a0080
com.fishkaster.app:id/center_horizontal = 0x7f0a007f
com.fishkaster.app:id/centerInside = 0x7f0a007e
com.fishkaster.app:id/center = 0x7f0a007c
com.fishkaster.app:style/Widget.AppCompat.ActionButton = 0x7f14032b
com.fishkaster.app:id/catalyst_redbox_title = 0x7f0a007b
com.fishkaster.app:layout/abc_screen_simple_overlay_action_mode = 0x7f0d0016
com.fishkaster.app:id/button = 0x7f0a0076
com.fishkaster.app:string/error_a11y_label = 0x7f13006c
com.fishkaster.app:id/browser_actions_menu_items = 0x7f0a0074
com.fishkaster.app:drawable/exo_ic_skip_previous = 0x7f0800c1
com.fishkaster.app:id/browser_actions_menu_item_text = 0x7f0a0073
com.fishkaster.app:id/browser_actions_menu_item_icon = 0x7f0a0072
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary20 = 0x7f060111
com.fishkaster.app:string/state_unselected_description = 0x7f130145
com.fishkaster.app:styleable/MaterialAlertDialogTheme = 0x7f150053
com.fishkaster.app:id/bounce = 0x7f0a0070
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1403ef
com.fishkaster.app:id/bottom = 0x7f0a006f
com.fishkaster.app:id/baseline = 0x7f0a006b
com.fishkaster.app:id/autofill_inline_suggestion_title = 0x7f0a0069
com.fishkaster.app:id/autoCompleteToStart = 0x7f0a0065
com.fishkaster.app:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f1401a8
com.fishkaster.app:attr/clockNumberTextColor = 0x7f0400e3
com.fishkaster.app:attr/gestureInsetBottomIgnored = 0x7f040248
com.fishkaster.app:id/autoCompleteToEnd = 0x7f0a0064
com.fishkaster.app:attr/drawableTopCompat = 0x7f0401c1
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0e008a
com.fishkaster.app:dimen/mtrl_chip_text_size = 0x7f0702c8
com.fishkaster.app:id/async = 0x7f0a0061
com.fishkaster.app:id/arc = 0x7f0a005f
com.fishkaster.app:styleable/RecyclerView = 0x7f150083
com.fishkaster.app:id/animateToStart = 0x7f0a005e
com.fishkaster.app:layout/autofill_inline_suggestion = 0x7f0d0020
com.fishkaster.app:id/androidx_compose_ui_view_composition_context = 0x7f0a005c
com.fishkaster.app:id/amu_text = 0x7f0a005b
com.fishkaster.app:id/always = 0x7f0a005a
com.fishkaster.app:id/mtrl_picker_text_input_range_end = 0x7f0a0187
com.fishkaster.app:style/Widget.Material3.Button.TextButton.Dialog = 0x7f14039f
com.fishkaster.app:id/all = 0x7f0a0059
com.fishkaster.app:dimen/m3_btn_padding_left = 0x7f070100
com.fishkaster.app:id/aligned = 0x7f0a0058
com.fishkaster.app:id/adjust_height = 0x7f0a0054
com.fishkaster.app:id/react_test_id = 0x7f0a01c8
com.fishkaster.app:id/action_text = 0x7f0a0050
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0e0089
com.fishkaster.app:string/ic_rotate_right_24 = 0x7f1300b3
com.fishkaster.app:id/action_mode_close_button = 0x7f0a004f
com.fishkaster.app:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1403f6
com.fishkaster.app:id/action_mode_bar_stub = 0x7f0a004e
com.fishkaster.app:attr/visibilityMode = 0x7f040526
com.fishkaster.app:id/action_mode_bar = 0x7f0a004d
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f14028a
com.fishkaster.app:macro/m3_comp_extended_fab_primary_container_color = 0x7f0e002c
com.fishkaster.app:id/action_menu_presenter = 0x7f0a004c
com.fishkaster.app:id/action_menu_divider = 0x7f0a004b
com.fishkaster.app:color/m3_ref_palette_dynamic_primary99 = 0x7f060100
com.fishkaster.app:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f140409
com.fishkaster.app:style/Theme.MaterialComponents.Bridge = 0x7f140283
com.fishkaster.app:color/m3_navigation_item_background_color = 0x7f0600b8
com.fishkaster.app:id/action_image = 0x7f0a004a
com.fishkaster.app:dimen/design_snackbar_min_width = 0x7f070087
com.fishkaster.app:color/ripple_material_dark = 0x7f06031f
com.fishkaster.app:style/Widget.Material3.CardView.Outlined = 0x7f1403a9
com.fishkaster.app:id/action_bar_title = 0x7f0a0046
com.fishkaster.app:id/action_bar_root = 0x7f0a0043
com.fishkaster.app:id/action_bar = 0x7f0a0040
com.fishkaster.app:id/action0 = 0x7f0a003f
com.fishkaster.app:id/oval = 0x7f0a01b0
com.fishkaster.app:id/accessibility_value = 0x7f0a003e
com.fishkaster.app:id/accessibility_state = 0x7f0a003c
com.fishkaster.app:macro/m3_comp_outlined_text_field_outline_color = 0x7f0e00c3
com.fishkaster.app:id/accessibility_role = 0x7f0a003b
com.fishkaster.app:string/exo_track_surround_5_point_1 = 0x7f1300a1
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0e00d0
com.fishkaster.app:id/accessibility_hint = 0x7f0a0036
com.fishkaster.app:id/accessibility_custom_action_9 = 0x7f0a0035
com.fishkaster.app:id/accessibility_custom_action_7 = 0x7f0a0033
com.fishkaster.app:id/accessibility_custom_action_6 = 0x7f0a0032
com.fishkaster.app:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f140186
com.fishkaster.app:id/accessibility_custom_action_4 = 0x7f0a0030
com.fishkaster.app:macro/m3_comp_navigation_drawer_headline_color = 0x7f0e0084
com.fishkaster.app:id/accessibility_custom_action_31 = 0x7f0a002f
com.fishkaster.app:id/accessibility_custom_action_29 = 0x7f0a002c
com.fishkaster.app:id/accessibility_custom_action_27 = 0x7f0a002a
com.fishkaster.app:id/accessibility_custom_action_26 = 0x7f0a0029
com.fishkaster.app:color/material_personalized_color_surface_dim = 0x7f0602c4
com.fishkaster.app:id/accessibility_custom_action_23 = 0x7f0a0026
com.fishkaster.app:id/accessibility_custom_action_22 = 0x7f0a0025
com.fishkaster.app:dimen/mtrl_switch_thumb_icon_size = 0x7f07031e
com.fishkaster.app:id/accessibility_custom_action_21 = 0x7f0a0024
com.fishkaster.app:style/Widget.Material3.ChipGroup = 0x7f1403b5
com.fishkaster.app:attr/minHeight = 0x7f04034e
com.fishkaster.app:id/accessibility_custom_action_14 = 0x7f0a001c
com.fishkaster.app:color/material_dynamic_tertiary99 = 0x7f060288
com.fishkaster.app:dimen/m3_navigation_rail_icon_size = 0x7f0701f1
com.fishkaster.app:id/accessibility_custom_action_13 = 0x7f0a001b
com.fishkaster.app:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0601c9
com.fishkaster.app:id/accessibility_custom_action_1 = 0x7f0a0017
com.fishkaster.app:color/m3_ref_palette_primary30 = 0x7f060151
com.fishkaster.app:id/accessibility_custom_action_0 = 0x7f0a0016
com.fishkaster.app:id/accessibility_collection_item = 0x7f0a0015
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f140313
com.fishkaster.app:id/accessibility_actions = 0x7f0a0013
com.fishkaster.app:style/Platform.V25.AppCompat = 0x7f14016b
com.fishkaster.app:id/TOP_START = 0x7f0a0010
com.fishkaster.app:id/SHOW_PROGRESS = 0x7f0a000d
com.fishkaster.app:color/m3_sys_color_light_on_primary = 0x7f0601fb
com.fishkaster.app:id/SHOW_PATH = 0x7f0a000c
com.fishkaster.app:id/SHIFT = 0x7f0a000a
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f140036
com.fishkaster.app:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1402e9
com.fishkaster.app:attr/materialCalendarHeaderConfirmButton = 0x7f04031d
com.fishkaster.app:layout/mtrl_calendar_year = 0x7f0d0068
com.fishkaster.app:id/META = 0x7f0a0008
com.fishkaster.app:drawable/ic_resume = 0x7f080110
com.fishkaster.app:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f140401
com.fishkaster.app:id/CropOverlayView = 0x7f0a0004
com.fishkaster.app:font/roboto_medium_numbers = 0x7f090007
com.fishkaster.app:font/jetbrains_mono_regular = 0x7f090006
com.fishkaster.app:layout/notification_template_part_chronometer = 0x7f0d0085
com.fishkaster.app:style/Widget.Material3.Tooltip = 0x7f14042c
com.fishkaster.app:font/jetbrains_mono_light = 0x7f090004
com.fishkaster.app:style/Widget.AppCompat.ActionBar = 0x7f140326
com.fishkaster.app:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700e4
com.fishkaster.app:font/inter_regular = 0x7f090002
com.fishkaster.app:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f140251
com.fishkaster.app:id/showHome = 0x7f0a01fb
com.fishkaster.app:font/inter_bold = 0x7f090000
com.fishkaster.app:styleable/KeyCycle = 0x7f150044
com.fishkaster.app:style/AppTheme = 0x7f14000d
com.fishkaster.app:color/material_personalized_color_surface_container_lowest = 0x7f0602c3
com.fishkaster.app:drawable/updates_nav = 0x7f080171
com.fishkaster.app:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f1403c2
com.fishkaster.app:drawable/update_icon = 0x7f080170
com.fishkaster.app:color/material_on_surface_stroke = 0x7f06029d
com.fishkaster.app:color/mtrl_fab_icon_text_color_selector = 0x7f0602f2
com.fishkaster.app:styleable/CircularProgressIndicator = 0x7f150022
com.fishkaster.app:attr/cropAutoZoomEnabled = 0x7f040165
com.fishkaster.app:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0e0142
com.fishkaster.app:drawable/tooltip_frame_light = 0x7f08016f
com.fishkaster.app:drawable/tooltip_frame_dark = 0x7f08016e
com.fishkaster.app:drawable/test_level_drawable = 0x7f08016d
com.fishkaster.app:dimen/exo_settings_sub_text_size = 0x7f0700a4
com.fishkaster.app:drawable/splashscreen_logo = 0x7f08016c
com.fishkaster.app:attr/cornerFamilyTopLeft = 0x7f040154
com.fishkaster.app:drawable/show_at_launch = 0x7f08016b
com.fishkaster.app:drawable/rns_rounder_top_corners_shape = 0x7f080168
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0e0086
com.fishkaster.app:drawable/rn_edit_text_material = 0x7f080167
com.fishkaster.app:drawable/ripple_effect = 0x7f080166
com.fishkaster.app:drawable/plus = 0x7f080162
com.fishkaster.app:drawable/performance = 0x7f080161
com.fishkaster.app:color/m3_sys_color_light_secondary_container = 0x7f060208
com.fishkaster.app:integer/m3_sys_motion_duration_extra_long1 = 0x7f0b0012
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f07022f
com.fishkaster.app:id/position = 0x7f0a01c1
com.fishkaster.app:drawable/notification_icon_background = 0x7f080159
com.fishkaster.app:dimen/material_clock_period_toggle_height = 0x7f070251
com.fishkaster.app:drawable/notification_bg_normal = 0x7f080157
com.fishkaster.app:drawable/notification_bg = 0x7f080153
com.fishkaster.app:id/hide_graphics_layer_in_inspector_tag = 0x7f0a0123
com.fishkaster.app:drawable/notification_action_background = 0x7f080152
com.fishkaster.app:id/exo_ffwd_with_amount = 0x7f0a00dc
com.fishkaster.app:layout/mtrl_calendar_month_labeled = 0x7f0d0064
com.fishkaster.app:drawable/navigation_empty_icon = 0x7f080151
com.fishkaster.app:drawable/mtrl_tabs_default_indicator = 0x7f080150
com.fishkaster.app:style/Widget.Autofill.InlineSuggestionChip = 0x7f140370
com.fishkaster.app:attr/minWidth = 0x7f040352
com.fishkaster.app:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0e0067
com.fishkaster.app:attr/showMotionSpec = 0x7f040419
com.fishkaster.app:drawable/mtrl_switch_track = 0x7f08014e
com.fishkaster.app:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f08014a
com.fishkaster.app:drawable/home = 0x7f0800f9
com.fishkaster.app:drawable/mtrl_switch_thumb_pressed = 0x7f080148
com.fishkaster.app:string/catalyst_change_bundle_location_cancel = 0x7f13002f
com.fishkaster.app:drawable/mtrl_switch_thumb_checked_pressed = 0x7f080146
com.fishkaster.app:drawable/mtrl_switch_thumb_checked = 0x7f080145
com.fishkaster.app:drawable/mtrl_switch_thumb = 0x7f080144
com.fishkaster.app:drawable/mtrl_popupmenu_background = 0x7f080142
com.fishkaster.app:drawable/mtrl_navigation_bar_item_background = 0x7f080141
com.fishkaster.app:drawable/mtrl_ic_indeterminate = 0x7f080140
com.fishkaster.app:drawable/mtrl_ic_checkbox_unchecked = 0x7f08013e
com.fishkaster.app:attr/keyPositionType = 0x7f04029e
com.fishkaster.app:drawable/mtrl_ic_arrow_drop_up = 0x7f08013a
com.fishkaster.app:color/m3_sys_color_tertiary_fixed_dim = 0x7f06021f
com.fishkaster.app:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f14045d
com.fishkaster.app:drawable/mtrl_dropdown_arrow = 0x7f080138
com.fishkaster.app:drawable/mtrl_dialog_background = 0x7f080137
com.fishkaster.app:id/navigation_bar_item_large_label_view = 0x7f0a0191
com.fishkaster.app:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f080135
com.fishkaster.app:dimen/disabled_alpha_material_dark = 0x7f070091
com.fishkaster.app:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0e0020
com.fishkaster.app:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f140408
com.fishkaster.app:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f080133
com.fishkaster.app:drawable/exo_icon_repeat_one = 0x7f0800cf
com.fishkaster.app:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f080132
com.fishkaster.app:styleable/FloatingActionButton_Behavior_Layout = 0x7f150037
com.fishkaster.app:dimen/m3_snackbar_margin = 0x7f070215
com.fishkaster.app:attr/retryImageScaleType = 0x7f0403df
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f14046e
com.fishkaster.app:id/open_search_view_toolbar = 0x7f0a01ab
com.fishkaster.app:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f080131
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600f3
com.fishkaster.app:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f080130
com.fishkaster.app:drawable/mtrl_checkbox_button_icon = 0x7f08012f
com.fishkaster.app:drawable/mtrl_checkbox_button = 0x7f08012d
com.fishkaster.app:drawable/mtrl_bottomsheet_drag_handle = 0x7f08012c
com.fishkaster.app:attr/tabPaddingBottom = 0x7f04047b
com.fishkaster.app:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f080126
com.fishkaster.app:drawable/material_ic_edit_black_24dp = 0x7f080125
com.fishkaster.app:id/material_timepicker_view = 0x7f0a0164
com.fishkaster.app:drawable/material_ic_clear_black_24dp = 0x7f080124
com.fishkaster.app:attr/constraint_referenced_ids = 0x7f04013a
com.fishkaster.app:drawable/material_cursor_drawable = 0x7f080122
com.fishkaster.app:drawable/m3_tabs_transparent_background = 0x7f080121
com.fishkaster.app:anim/mtrl_bottom_sheet_slide_out = 0x7f010030
com.fishkaster.app:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1402b6
com.fishkaster.app:macro/m3_comp_time_picker_clock_dial_color = 0x7f0e014c
com.fishkaster.app:drawable/m3_tabs_background = 0x7f08011e
com.fishkaster.app:attr/tooltipFrameBackground = 0x7f0404f2
com.fishkaster.app:color/material_dynamic_tertiary50 = 0x7f060282
com.fishkaster.app:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0e010a
com.fishkaster.app:attr/tabRippleColor = 0x7f04047f
com.fishkaster.app:drawable/m3_popupmenu_background_overlay = 0x7f08011b
com.fishkaster.app:drawable/m3_password_eye = 0x7f08011a
com.fishkaster.app:drawable/m3_avd_show_password = 0x7f080118
com.fishkaster.app:drawable/log_in = 0x7f080116
com.fishkaster.app:string/material_motion_easing_decelerated = 0x7f1300d3
com.fishkaster.app:id/on = 0x7f0a019c
com.fishkaster.app:attr/textAppearanceDisplayMedium = 0x7f040495
com.fishkaster.app:drawable/notification_bg_normal_pressed = 0x7f080158
com.fishkaster.app:drawable/indeterminate_static = 0x7f080114
com.fishkaster.app:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f140115
com.fishkaster.app:string/common_google_play_services_enable_button = 0x7f130053
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600e7
com.fishkaster.app:dimen/m3_card_elevated_disabled_z = 0x7f07010c
com.fishkaster.app:drawable/ic_rotate_left_24 = 0x7f080111
com.fishkaster.app:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0e0017
com.fishkaster.app:attr/region_heightLessThan = 0x7f0403d6
com.fishkaster.app:color/mtrl_tabs_icon_color_selector_colored = 0x7f06030b
com.fishkaster.app:drawable/ic_mtrl_chip_close_circle = 0x7f08010f
com.fishkaster.app:id/autofill_inline_suggestion_subtitle = 0x7f0a0068
com.fishkaster.app:attr/fontProviderFetchTimeout = 0x7f04023c
com.fishkaster.app:drawable/ic_mtrl_chip_checked_circle = 0x7f08010e
com.fishkaster.app:attr/uri = 0x7f04051a
com.fishkaster.app:drawable/ic_mtrl_chip_checked_black = 0x7f08010d
com.fishkaster.app:drawable/ic_mtrl_checked_circle = 0x7f08010c
com.fishkaster.app:dimen/abc_list_item_height_large_material = 0x7f070030
com.fishkaster.app:drawable/ic_m3_chip_close = 0x7f08010b
com.fishkaster.app:drawable/ic_m3_chip_check = 0x7f080109
com.fishkaster.app:drawable/ic_fullscreen_32dp = 0x7f080105
com.fishkaster.app:drawable/ic_flip_24 = 0x7f080104
com.fishkaster.app:color/design_bottom_navigation_shadow_color = 0x7f060042
com.fishkaster.app:drawable/ic_call_decline_low = 0x7f080101
com.fishkaster.app:integer/material_motion_path = 0x7f0b002f
com.fishkaster.app:drawable/ic_call_answer_video = 0x7f0800fe
com.fishkaster.app:drawable/ic_arrow_back_black_24 = 0x7f0800fb
com.fishkaster.app:styleable/MaterialToolbar = 0x7f150063
com.fishkaster.app:drawable/fast_refresh = 0x7f0800f6
com.fishkaster.app:drawable/expo_logo = 0x7f0800f4
com.fishkaster.app:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f140058
com.fishkaster.app:drawable/exo_styled_controls_vr = 0x7f0800f3
com.fishkaster.app:styleable/NavigationRailView = 0x7f150075
com.fishkaster.app:attr/fontWeight = 0x7f040242
com.fishkaster.app:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f070184
com.fishkaster.app:drawable/exo_styled_controls_subtitle_on = 0x7f0800f2
com.fishkaster.app:styleable/MaterialAutoCompleteTextView = 0x7f150054
com.fishkaster.app:drawable/exo_styled_controls_settings = 0x7f0800ed
com.fishkaster.app:dimen/m3_comp_fab_primary_container_elevation = 0x7f07013b
com.fishkaster.app:drawable/exo_styled_controls_rewind = 0x7f0800ec
com.fishkaster.app:drawable/exo_styled_controls_repeat_one = 0x7f0800eb
com.fishkaster.app:drawable/exo_styled_controls_repeat_off = 0x7f0800ea
com.fishkaster.app:drawable/exo_styled_controls_repeat_all = 0x7f0800e9
com.fishkaster.app:drawable/exo_styled_controls_play = 0x7f0800e7
com.fishkaster.app:drawable/btn_radio_on_mtrl = 0x7f080084
com.fishkaster.app:style/Base.Theme.MaterialComponents.Dialog = 0x7f14006b
com.fishkaster.app:drawable/exo_styled_controls_overflow_show = 0x7f0800e5
com.fishkaster.app:dimen/design_fab_border_width = 0x7f070071
com.fishkaster.app:style/TextAppearance.AppCompat.Tooltip = 0x7f1401dc
com.fishkaster.app:drawable/exo_styled_controls_overflow_hide = 0x7f0800e4
com.fishkaster.app:drawable/exo_styled_controls_next = 0x7f0800e3
com.fishkaster.app:drawable/exo_styled_controls_fullscreen_exit = 0x7f0800e2
com.fishkaster.app:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1401cd
com.fishkaster.app:color/m3_card_stroke_color = 0x7f060093
com.fishkaster.app:drawable/exo_styled_controls_fastforward = 0x7f0800e0
com.fishkaster.app:drawable/exo_styled_controls_audiotrack = 0x7f0800de
com.fishkaster.app:attr/iconSize = 0x7f040267
com.fishkaster.app:anim/rns_slide_out_to_left = 0x7f01004a
com.fishkaster.app:drawable/exo_notification_small_icon = 0x7f0800db
com.fishkaster.app:attr/motionDurationLong4 = 0x7f040361
com.fishkaster.app:drawable/exo_notification_previous = 0x7f0800d9
com.fishkaster.app:attr/ratingBarStyleSmall = 0x7f0403d4
com.fishkaster.app:drawable/exo_notification_play = 0x7f0800d8
com.fishkaster.app:drawable/exo_notification_next = 0x7f0800d6
com.fishkaster.app:drawable/exo_notification_fastforward = 0x7f0800d5
com.fishkaster.app:drawable/exo_icon_shuffle_on = 0x7f0800d2
com.fishkaster.app:style/DialogWindowTheme = 0x7f140129
com.fishkaster.app:string/copy_toast_msg = 0x7f130065
com.fishkaster.app:dimen/fastscroll_margin = 0x7f0700b7
com.fishkaster.app:string/exo_controls_settings_description = 0x7f130082
com.fishkaster.app:drawable/exo_icon_rewind = 0x7f0800d0
com.fishkaster.app:style/MaterialAlertDialog.Material3 = 0x7f14014b
com.fishkaster.app:drawable/exo_icon_repeat_off = 0x7f0800ce
com.fishkaster.app:attr/keylines = 0x7f0402a0
com.fishkaster.app:drawable/exo_icon_repeat_all = 0x7f0800cd
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600dd
com.fishkaster.app:drawable/exo_icon_previous = 0x7f0800cc
com.fishkaster.app:style/Theme.Catalyst.LogBox = 0x7f140257
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600f0
com.fishkaster.app:drawable/exo_icon_pause = 0x7f0800ca
com.fishkaster.app:drawable/exo_icon_next = 0x7f0800c9
com.fishkaster.app:drawable/exo_icon_fullscreen_exit = 0x7f0800c8
com.fishkaster.app:drawable/exo_icon_fullscreen_enter = 0x7f0800c7
com.fishkaster.app:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0e0121
com.fishkaster.app:drawable/exo_icon_fastforward = 0x7f0800c6
com.fishkaster.app:dimen/design_appbar_elevation = 0x7f070061
com.fishkaster.app:drawable/exo_ic_subtitle_on = 0x7f0800c4
com.fishkaster.app:drawable/exo_ic_subtitle_off = 0x7f0800c3
com.fishkaster.app:drawable/exo_ic_speed = 0x7f0800c2
com.fishkaster.app:drawable/exo_ic_skip_next = 0x7f0800c0
com.fishkaster.app:style/Theme.Catalyst.RedBox = 0x7f140258
com.fishkaster.app:attr/layout_constraintTop_toBottomOf = 0x7f0402d7
com.fishkaster.app:drawable/exo_ic_settings = 0x7f0800bf
com.fishkaster.app:id/fillStart = 0x7f0a0103
com.fishkaster.app:drawable/exo_ic_rewind = 0x7f0800be
com.fishkaster.app:drawable/exo_ic_pause_circle_filled = 0x7f0800bc
com.fishkaster.app:string/material_timepicker_hour = 0x7f1300dc
com.fishkaster.app:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f140109
com.fishkaster.app:color/m3_ref_palette_tertiary99 = 0x7f060173
com.fishkaster.app:drawable/exo_ic_fullscreen_exit = 0x7f0800bb
com.fishkaster.app:dimen/abc_text_size_caption_material = 0x7f070042
com.fishkaster.app:dimen/mtrl_badge_with_text_size = 0x7f070278
com.fishkaster.app:drawable/exo_ic_fullscreen_enter = 0x7f0800ba
com.fishkaster.app:drawable/exo_ic_forward = 0x7f0800b9
com.fishkaster.app:drawable/exo_ic_chevron_right = 0x7f0800b7
com.fishkaster.app:string/abc_search_hint = 0x7f130012
com.fishkaster.app:styleable/SwipeRefreshLayout = 0x7f150098
com.fishkaster.app:drawable/exo_ic_chevron_left = 0x7f0800b6
com.fishkaster.app:drawable/exo_controls_repeat_one = 0x7f0800ae
com.fishkaster.app:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0602c9
com.fishkaster.app:drawable/exo_controls_repeat_off = 0x7f0800ad
com.fishkaster.app:drawable/exo_controls_repeat_all = 0x7f0800ac
com.fishkaster.app:drawable/exo_controls_previous = 0x7f0800ab
com.fishkaster.app:integer/material_motion_duration_short_1 = 0x7f0b002d
com.fishkaster.app:anim/abc_tooltip_enter = 0x7f01000a
com.fishkaster.app:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0e0061
com.fishkaster.app:id/open_search_view_edit_text = 0x7f0a01a5
com.fishkaster.app:drawable/exo_controls_next = 0x7f0800a8
com.fishkaster.app:color/material_dynamic_secondary95 = 0x7f06027a
com.fishkaster.app:layout/abc_action_mode_close_item_material = 0x7f0d0005
com.fishkaster.app:attr/argType = 0x7f04003e
com.fishkaster.app:drawable/exo_controls_fullscreen_exit = 0x7f0800a7
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f140465
com.fishkaster.app:drawable/exo_controls_fullscreen_enter = 0x7f0800a6
com.fishkaster.app:drawable/exo_controls_fastforward = 0x7f0800a5
com.fishkaster.app:style/Theme.AppCompat.DialogWhenLarge = 0x7f14024b
com.fishkaster.app:drawable/download = 0x7f0800a3
com.fishkaster.app:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0e00d8
com.fishkaster.app:id/material_timepicker_cancel_button = 0x7f0a0160
com.fishkaster.app:drawable/design_snackbar_background = 0x7f0800a1
com.fishkaster.app:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f14006a
com.fishkaster.app:string/mtrl_checkbox_button_path_name = 0x7f1300eb
com.fishkaster.app:id/accessibility_custom_action_30 = 0x7f0a002e
com.fishkaster.app:attr/actualImageScaleType = 0x7f040028
com.fishkaster.app:drawable/design_password_eye = 0x7f0800a0
com.fishkaster.app:dimen/notification_large_icon_width = 0x7f070337
com.fishkaster.app:drawable/design_fab_background = 0x7f08009d
com.fishkaster.app:drawable/copy = 0x7f08009c
com.fishkaster.app:string/m3_ref_typeface_plain_medium = 0x7f1300be
com.fishkaster.app:id/src_over = 0x7f0a0210
com.fishkaster.app:drawable/common_google_signin_btn_text_light_normal_background = 0x7f08009b
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral4 = 0x7f0600cd
com.fishkaster.app:drawable/common_google_signin_btn_text_light = 0x7f080098
com.fishkaster.app:drawable/notification_tile_bg = 0x7f08015d
com.fishkaster.app:drawable/common_google_signin_btn_text_dark_focused = 0x7f080094
com.fishkaster.app:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f080092
com.fishkaster.app:drawable/common_google_signin_btn_icon_light = 0x7f08008f
com.fishkaster.app:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f08008d
com.fishkaster.app:drawable/common_google_signin_btn_icon_dark_focused = 0x7f08008b
com.fishkaster.app:drawable/common_full_open_on_phone = 0x7f080089
com.fishkaster.app:dimen/mtrl_alert_dialog_background_inset_top = 0x7f07026f
com.fishkaster.app:drawable/chevron_right = 0x7f080088
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f080016
com.fishkaster.app:drawable/check_circle = 0x7f080087
com.fishkaster.app:id/ic_flip_24 = 0x7f0a012b
com.fishkaster.app:drawable/bug = 0x7f080086
com.fishkaster.app:id/labeled = 0x7f0a0142
com.fishkaster.app:string/exo_track_role_commentary = 0x7f130098
com.fishkaster.app:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080081
com.fishkaster.app:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f080129
com.fishkaster.app:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f08007f
com.fishkaster.app:drawable/btn_checkbox_checked_mtrl = 0x7f08007e
com.fishkaster.app:id/performance = 0x7f0a01bc
com.fishkaster.app:drawable/avd_show_password = 0x7f08007c
com.fishkaster.app:drawable/amu_bubble_shadow = 0x7f080079
com.fishkaster.app:attr/listChoiceIndicatorSingleAnimated = 0x7f0402f8
com.fishkaster.app:drawable/alert = 0x7f080077
com.fishkaster.app:drawable/abc_textfield_search_material = 0x7f080075
com.fishkaster.app:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080074
com.fishkaster.app:id/tag_accessibility_heading = 0x7f0a0221
com.fishkaster.app:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080073
com.fishkaster.app:id/status_bar_latest_event_content = 0x7f0a0218
com.fishkaster.app:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080071
com.fishkaster.app:string/material_timepicker_am = 0x7f1300da
com.fishkaster.app:drawable/abc_text_select_handle_right_mtrl = 0x7f080070
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f140342
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1402ff
com.fishkaster.app:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.fishkaster.app:drawable/abc_text_cursor_material = 0x7f08006d
com.fishkaster.app:attr/colorPrimaryInverse = 0x7f04011d
com.fishkaster.app:macro/m3_comp_text_button_label_text_color = 0x7f0e0144
com.fishkaster.app:drawable/abc_spinner_textfield_background_material = 0x7f080066
com.fishkaster.app:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0e0135
com.fishkaster.app:color/abc_decor_view_status_guard_light = 0x7f060006
com.fishkaster.app:color/m3_sys_color_on_primary_fixed_variant = 0x7f060215
com.fishkaster.app:drawable/abc_seekbar_tick_mark_material = 0x7f080063
com.fishkaster.app:dimen/m3_comp_elevated_button_container_elevation = 0x7f07012e
com.fishkaster.app:drawable/abc_seekbar_thumb_material = 0x7f080062
com.fishkaster.app:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08005f
com.fishkaster.app:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08005e
com.fishkaster.app:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f080128
com.fishkaster.app:macro/m3_comp_navigation_bar_container_color = 0x7f0e006b
com.fishkaster.app:color/m3_ref_palette_secondary30 = 0x7f06015e
com.fishkaster.app:drawable/abc_ratingbar_small_material = 0x7f08005c
com.fishkaster.app:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0e0053
com.fishkaster.app:id/end_padder = 0x7f0a00c5
com.fishkaster.app:attr/layout_constraintLeft_toRightOf = 0x7f0402cf
com.fishkaster.app:drawable/abc_ratingbar_material = 0x7f08005b
com.fishkaster.app:drawable/abc_popup_background_mtrl_mult = 0x7f080059
com.fishkaster.app:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080058
com.fishkaster.app:drawable/abc_list_selector_background_transition_holo_light = 0x7f080053
com.fishkaster.app:attr/colorPrimarySurface = 0x7f04011e
com.fishkaster.app:attr/flow_padding = 0x7f040230
com.fishkaster.app:drawable/abc_list_focused_holo = 0x7f08004e
com.fishkaster.app:drawable/abc_list_divider_mtrl_alpha = 0x7f08004d
com.fishkaster.app:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080046
com.fishkaster.app:id/line3 = 0x7f0a014a
com.fishkaster.app:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080045
com.fishkaster.app:macro/m3_comp_outlined_text_field_container_shape = 0x7f0e00b1
com.fishkaster.app:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080042
com.fishkaster.app:drawable/abc_ic_ab_back_material = 0x7f08003d
com.fishkaster.app:string/mtrl_badge_numberless_content_description = 0x7f1300e4
com.fishkaster.app:drawable/abc_cab_background_top_material = 0x7f080038
com.fishkaster.app:style/AlertDialog.AppCompat.Light = 0x7f140001
com.fishkaster.app:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.fishkaster.app:dimen/mtrl_navigation_rail_margin = 0x7f0702f7
com.fishkaster.app:attr/dragThreshold = 0x7f0401b7
com.fishkaster.app:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080035
com.fishkaster.app:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080033
com.fishkaster.app:dimen/m3_simple_item_color_hovered_alpha = 0x7f07020f
com.fishkaster.app:styleable/NavigationBarView = 0x7f150074
com.fishkaster.app:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f140494
com.fishkaster.app:drawable/abc_btn_radio_material = 0x7f080031
com.fishkaster.app:attr/subheaderColor = 0x7f040455
com.fishkaster.app:drawable/abc_btn_default_mtrl_shape = 0x7f080030
com.fishkaster.app:attr/imageAspectRatio = 0x7f04026c
com.fishkaster.app:attr/itemSpacing = 0x7f040294
com.fishkaster.app:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08002e
com.fishkaster.app:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08002d
com.fishkaster.app:id/navigation_bar_item_icon_view = 0x7f0a018f
com.fishkaster.app:attr/transitionEasing = 0x7f040509
com.fishkaster.app:attr/touchAnchorSide = 0x7f0404f7
com.fishkaster.app:macro/m3_comp_badge_large_label_text_color = 0x7f0e0003
com.fishkaster.app:drawable/btn_checkbox_unchecked_mtrl = 0x7f080080
com.fishkaster.app:dimen/design_tab_scrollable_min_width = 0x7f07008d
com.fishkaster.app:color/m3_sys_color_dark_surface_container_high = 0x7f06019a
com.fishkaster.app:drawable/abc_text_select_handle_left_mtrl = 0x7f08006e
com.fishkaster.app:attr/actionBarTabTextStyle = 0x7f040009
com.fishkaster.app:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f080024
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar = 0x7f140340
com.fishkaster.app:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f080023
com.fishkaster.app:attr/altSrc = 0x7f040034
com.fishkaster.app:dimen/m3_comp_filled_card_icon_size = 0x7f07014e
com.fishkaster.app:attr/materialSearchViewStyle = 0x7f040337
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f080018
com.fishkaster.app:attr/theme = 0x7f0404bf
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f14003e
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f080017
com.fishkaster.app:attr/flow_verticalBias = 0x7f040232
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f080010
com.fishkaster.app:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f08000d
com.fishkaster.app:drawable/$m3_avd_hide_password__1 = 0x7f080007
com.fishkaster.app:drawable/$avd_show_password__2 = 0x7f080005
com.fishkaster.app:dimen/tooltip_y_offset_touch = 0x7f070348
com.fishkaster.app:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f14006e
com.fishkaster.app:styleable/AppBarLayoutStates = 0x7f15000c
com.fishkaster.app:attr/textAppearanceHeadlineSmall = 0x7f04049f
com.fishkaster.app:dimen/tooltip_vertical_padding = 0x7f070346
com.fishkaster.app:dimen/tooltip_precise_anchor_extra_offset = 0x7f070344
com.fishkaster.app:color/m3_ref_palette_dynamic_primary80 = 0x7f0600fd
com.fishkaster.app:dimen/notification_small_icon_size_as_large = 0x7f07033d
com.fishkaster.app:dimen/notification_small_icon_background_padding = 0x7f07033c
com.fishkaster.app:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f14033a
com.fishkaster.app:style/TextAppearance.AppCompat.Menu = 0x7f1401d3
com.fishkaster.app:dimen/notification_right_side_padding_top = 0x7f07033b
com.fishkaster.app:attr/closeIcon = 0x7f0400e4
com.fishkaster.app:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0e009d
com.fishkaster.app:dimen/mtrl_textinput_start_icon_margin_end = 0x7f07032a
com.fishkaster.app:dimen/mtrl_textinput_box_stroke_width_default = 0x7f070325
com.fishkaster.app:attr/actionModeSelectAllDrawable = 0x7f04001a
com.fishkaster.app:dimen/mtrl_textinput_box_corner_radius_small = 0x7f070323
com.fishkaster.app:id/search_bar = 0x7f0a01ec
com.fishkaster.app:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f070322
com.fishkaster.app:dimen/mtrl_switch_track_width = 0x7f070321
com.fishkaster.app:attr/elevation = 0x7f0401cd
com.fishkaster.app:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f04036d
com.fishkaster.app:dimen/mtrl_switch_thumb_size = 0x7f07031f
com.fishkaster.app:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0e011c
com.fishkaster.app:dimen/mtrl_snackbar_padding_horizontal = 0x7f07031b
com.fishkaster.app:attr/touchRegionId = 0x7f0404f8
com.fishkaster.app:dimen/mtrl_slider_widget_height = 0x7f070315
com.fishkaster.app:id/navigation_bar_item_labels_group = 0x7f0a0190
com.fishkaster.app:dimen/mtrl_slider_track_height = 0x7f070313
com.fishkaster.app:id/fitBottomStart = 0x7f0a0109
com.fishkaster.app:dimen/mtrl_slider_tick_radius = 0x7f070312
com.fishkaster.app:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0e00d9
com.fishkaster.app:dimen/mtrl_slider_tick_min_spacing = 0x7f070311
com.fishkaster.app:layout/mtrl_picker_dialog = 0x7f0d006d
com.fishkaster.app:dimen/mtrl_slider_label_padding = 0x7f07030c
com.fishkaster.app:attr/materialSearchViewToolbarHeight = 0x7f040338
com.fishkaster.app:attr/chipIconEnabled = 0x7f0400cd
com.fishkaster.app:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0601e5
com.fishkaster.app:dimen/abc_dialog_title_divider_material = 0x7f070026
com.fishkaster.app:macro/m3_comp_search_bar_input_text_color = 0x7f0e00e9
com.fishkaster.app:attr/progressBarImage = 0x7f0403c9
com.fishkaster.app:attr/collapseIcon = 0x7f0400ed
com.fishkaster.app:dimen/mtrl_slider_halo_radius = 0x7f07030b
com.fishkaster.app:color/material_dynamic_primary99 = 0x7f06026e
com.fishkaster.app:style/Base.Widget.AppCompat.SeekBar = 0x7f1400f7
com.fishkaster.app:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1400e1
com.fishkaster.app:style/Widget.Material3.TabLayout.Secondary = 0x7f14041c
com.fishkaster.app:id/top = 0x7f0a0243
com.fishkaster.app:color/dev_launcher_secondaryBackgroundColor = 0x7f06006c
com.fishkaster.app:dimen/mtrl_shape_corner_size_medium_component = 0x7f070309
com.fishkaster.app:color/material_dynamic_primary10 = 0x7f060263
com.fishkaster.app:dimen/mtrl_progress_track_thickness = 0x7f070307
com.fishkaster.app:attr/menuAlignmentMode = 0x7f04034b
com.fishkaster.app:id/accessibility_collection = 0x7f0a0014
com.fishkaster.app:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f070306
com.fishkaster.app:color/design_dark_default_color_on_secondary = 0x7f060049
com.fishkaster.app:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f070304
com.fishkaster.app:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f070303
com.fishkaster.app:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0601c2
com.fishkaster.app:dimen/mtrl_progress_circular_size_medium = 0x7f070301
com.fishkaster.app:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0e001b
com.fishkaster.app:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f140088
com.fishkaster.app:string/switch_role = 0x7f130148
com.fishkaster.app:color/m3_ref_palette_neutral17 = 0x7f06012c
com.fishkaster.app:dimen/mtrl_progress_circular_size = 0x7f0702ff
com.fishkaster.app:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.fishkaster.app:dimen/mtrl_progress_circular_radius = 0x7f0702fe
com.fishkaster.app:attr/barLength = 0x7f04006f
com.fishkaster.app:style/Widget.AppCompat.ActionMode = 0x7f14032e
com.fishkaster.app:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0700d5
com.fishkaster.app:id/window = 0x7f0a0268
com.fishkaster.app:dimen/mtrl_navigation_rail_text_size = 0x7f0702f9
com.fishkaster.app:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0702f8
com.fishkaster.app:dimen/mtrl_navigation_rail_elevation = 0x7f0702f4
com.fishkaster.app:id/search_src_text = 0x7f0a01f3
com.fishkaster.app:attr/backgroundInsetStart = 0x7f040057
com.fishkaster.app:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f07018b
com.fishkaster.app:dimen/mtrl_navigation_rail_compact_width = 0x7f0702f2
com.fishkaster.app:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0702f0
com.fishkaster.app:dimen/mtrl_navigation_item_icon_padding = 0x7f0702ed
com.fishkaster.app:bool/abc_action_bar_embed_tabs = 0x7f050000
com.fishkaster.app:attr/errorIconDrawable = 0x7f0401e3
com.fishkaster.app:dimen/mtrl_btn_padding_right = 0x7f07028d
com.fishkaster.app:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0702ea
com.fishkaster.app:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f14007c
com.fishkaster.app:id/alertTitle = 0x7f0a0056
com.fishkaster.app:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0e0118
com.fishkaster.app:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0702e7
com.fishkaster.app:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0e00f7
com.fishkaster.app:dimen/mtrl_low_ripple_focused_alpha = 0x7f0702e5
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f140205
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary0 = 0x7f06010e
com.fishkaster.app:dimen/mtrl_low_ripple_default_alpha = 0x7f0702e4
com.fishkaster.app:drawable/abc_star_half_black_48dp = 0x7f080068
com.fishkaster.app:attr/errorContentDescription = 0x7f0401e1
com.fishkaster.app:dimen/mtrl_high_ripple_focused_alpha = 0x7f0702e1
com.fishkaster.app:attr/colorOnPrimaryFixedVariant = 0x7f040109
com.fishkaster.app:attr/shapeAppearanceCornerSmall = 0x7f04040c
com.fishkaster.app:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0702da
com.fishkaster.app:dimen/mtrl_extended_fab_start_padding = 0x7f0702d6
com.fishkaster.app:dimen/mtrl_extended_fab_min_width = 0x7f0702d5
com.fishkaster.app:macro/m3_comp_snackbar_supporting_text_color = 0x7f0e0115
com.fishkaster.app:animator/m3_appbar_state_list_animator = 0x7f020009
com.fishkaster.app:attr/fabCustomSize = 0x7f040209
com.fishkaster.app:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0702d1
com.fishkaster.app:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f140268
com.fishkaster.app:drawable/ic_m3_chip_checked_circle = 0x7f08010a
com.fishkaster.app:dimen/mtrl_card_dragged_z = 0x7f0702c4
com.fishkaster.app:attr/flow_wrapMode = 0x7f040235
com.fishkaster.app:drawable/mtrl_ic_arrow_drop_down = 0x7f080139
com.fishkaster.app:dimen/mtrl_card_corner_radius = 0x7f0702c3
com.fishkaster.app:attr/floatingActionButtonSecondaryStyle = 0x7f04021a
com.fishkaster.app:attr/titleMarginStart = 0x7f0404e4
com.fishkaster.app:dimen/exo_styled_minimal_controls_margin_bottom = 0x7f0700af
com.fishkaster.app:dimen/mtrl_card_checked_icon_size = 0x7f0702c2
com.fishkaster.app:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f070290
com.fishkaster.app:style/ThemeOverlay.MaterialComponents = 0x7f1402fd
com.fishkaster.app:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0700d6
com.fishkaster.app:color/design_fab_shadow_start_color = 0x7f060061
com.fishkaster.app:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0702de
com.fishkaster.app:drawable/exo_edit_mode_logo = 0x7f0800b3
com.fishkaster.app:attr/materialCalendarHeaderToggleButton = 0x7f040322
com.fishkaster.app:drawable/abc_switch_track_mtrl_alpha = 0x7f08006a
com.fishkaster.app:dimen/mtrl_calendar_year_vertical_padding = 0x7f0702bf
com.fishkaster.app:id/mtrl_calendar_frame = 0x7f0a0177
com.fishkaster.app:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0702ba
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0600df
com.fishkaster.app:id/action_divider = 0x7f0a0049
com.fishkaster.app:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.fishkaster.app:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1402b8
com.fishkaster.app:attr/resize_mode = 0x7f0403dc
com.fishkaster.app:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f070183
com.fishkaster.app:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0702b7
com.fishkaster.app:color/m3_ref_palette_secondary50 = 0x7f060160
com.fishkaster.app:color/m3_ref_palette_primary95 = 0x7f060158
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f08001c
com.fishkaster.app:string/mtrl_picker_range_header_selected = 0x7f130108
com.fishkaster.app:id/row_index_key = 0x7f0a01df
com.fishkaster.app:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f0702b1
com.fishkaster.app:dimen/abc_switch_padding = 0x7f07003e
com.fishkaster.app:color/m3_sys_color_primary_fixed = 0x7f06021a
com.fishkaster.app:dimen/mtrl_calendar_month_vertical_padding = 0x7f0702b0
com.fishkaster.app:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f0702ac
com.fishkaster.app:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f0702ab
com.fishkaster.app:dimen/mtrl_calendar_header_selection_line_height = 0x7f0702a9
com.fishkaster.app:dimen/mtrl_textinput_end_icon_margin_start = 0x7f070328
com.fishkaster.app:drawable/abc_edit_text_material = 0x7f08003c
com.fishkaster.app:style/Widget.Material3.Button.IconButton.Filled = 0x7f140399
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0e00c7
com.fishkaster.app:color/secondary_text_disabled_material_dark = 0x7f060323
com.fishkaster.app:id/notification_main_column_container = 0x7f0a019a
com.fishkaster.app:dimen/mtrl_calendar_header_height_fullscreen = 0x7f0702a8
com.fishkaster.app:dimen/m3_card_elevated_hovered_z = 0x7f07010f
com.fishkaster.app:string/path_password_eye_mask_strike_through = 0x7f130126
com.fishkaster.app:dimen/mtrl_calendar_header_divider_thickness = 0x7f0702a6
com.fishkaster.app:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f0702a5
com.fishkaster.app:styleable/SignInButton = 0x7f15008c
com.fishkaster.app:string/exo_track_stereo = 0x7f13009f
com.fishkaster.app:dimen/mtrl_calendar_header_content_padding = 0x7f0702a4
com.fishkaster.app:attr/flow_firstVerticalStyle = 0x7f040226
com.fishkaster.app:dimen/notification_main_column_padding_top = 0x7f070338
com.fishkaster.app:id/autofill_inline_suggestion_end_icon = 0x7f0a0066
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f140314
com.fishkaster.app:id/easeOut = 0x7f0a00bd
com.fishkaster.app:dimen/mtrl_calendar_dialog_background_inset = 0x7f0702a3
com.fishkaster.app:layout/material_chip_input_combo = 0x7f0d004a
com.fishkaster.app:attr/colorPrimaryVariant = 0x7f04011f
com.fishkaster.app:dimen/mtrl_calendar_day_width = 0x7f0702a1
com.fishkaster.app:dimen/mtrl_calendar_day_vertical_padding = 0x7f0702a0
com.fishkaster.app:dimen/mtrl_calendar_day_corner = 0x7f07029c
com.fishkaster.app:attr/customNavigationLayout = 0x7f040196
com.fishkaster.app:attr/autoAdjustToWithinGrandparentBounds = 0x7f040042
com.fishkaster.app:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f070147
com.fishkaster.app:dimen/mtrl_calendar_content_padding = 0x7f07029b
com.fishkaster.app:dimen/mtrl_btn_text_size = 0x7f070295
com.fishkaster.app:color/material_dynamic_tertiary40 = 0x7f060281
com.fishkaster.app:color/material_harmonized_color_error_container = 0x7f060291
com.fishkaster.app:dimen/mtrl_btn_text_btn_padding_right = 0x7f070294
com.fishkaster.app:attr/paddingLeftSystemWindowInsets = 0x7f04039a
com.fishkaster.app:integer/material_motion_duration_long_2 = 0x7f0b002a
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600cc
com.fishkaster.app:attr/onNegativeCross = 0x7f040390
com.fishkaster.app:dimen/mtrl_btn_text_btn_padding_left = 0x7f070293
com.fishkaster.app:layout/amu_webview = 0x7f0d001f
com.fishkaster.app:dimen/mtrl_btn_text_btn_icon_padding = 0x7f070292
com.fishkaster.app:dimen/mtrl_btn_stroke_size = 0x7f070291
com.fishkaster.app:dimen/mtrl_btn_padding_left = 0x7f07028c
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Display2 = 0x7f14001f
com.fishkaster.app:id/tag_on_receive_content_mime_types = 0x7f0a0225
com.fishkaster.app:dimen/mtrl_btn_padding_bottom = 0x7f07028b
com.fishkaster.app:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.fishkaster.app:dimen/mtrl_btn_corner_radius = 0x7f07027f
com.fishkaster.app:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07027c
com.fishkaster.app:animator/mtrl_chip_state_list_anim = 0x7f020018
com.fishkaster.app:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f07027a
com.fishkaster.app:dimen/m3_appbar_size_large = 0x7f0700d0
com.fishkaster.app:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f080021
com.fishkaster.app:macro/m3_comp_input_chip_container_shape = 0x7f0e005b
com.fishkaster.app:dimen/mtrl_badge_text_size = 0x7f070275
com.fishkaster.app:drawable/mtrl_ic_error = 0x7f08013f
com.fishkaster.app:dimen/mtrl_badge_size = 0x7f070273
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0601dc
com.fishkaster.app:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070271
com.fishkaster.app:style/TextAppearance.Material3.ActionBar.Title = 0x7f140215
com.fishkaster.app:dimen/material_time_picker_minimum_screen_height = 0x7f07026a
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0e006d
com.fishkaster.app:attr/layout_constraintBottom_creator = 0x7f0402ba
com.fishkaster.app:dimen/material_input_text_to_prefix_suffix_padding = 0x7f070266
com.fishkaster.app:dimen/material_clock_number_text_size = 0x7f070250
com.fishkaster.app:attr/buttonTint = 0x7f0400a5
com.fishkaster.app:dimen/design_bottom_navigation_shadow_height = 0x7f07006c
com.fishkaster.app:color/material_grey_850 = 0x7f06028e
com.fishkaster.app:dimen/material_helper_text_default_padding_top = 0x7f070263
com.fishkaster.app:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0e0153
com.fishkaster.app:color/material_harmonized_color_on_error = 0x7f060292
com.fishkaster.app:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0e0127
com.fishkaster.app:string/material_motion_easing_accelerated = 0x7f1300d2
com.fishkaster.app:attr/cardForegroundColor = 0x7f0400b1
com.fishkaster.app:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f070129
com.fishkaster.app:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0700d2
com.fishkaster.app:drawable/$avd_hide_password__0 = 0x7f080000
com.fishkaster.app:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f070262
com.fishkaster.app:style/Widget.AppCompat.ListPopupWindow = 0x7f140357
com.fishkaster.app:attr/dividerThickness = 0x7f0401b3
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0e0081
com.fishkaster.app:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f07012c
com.fishkaster.app:animator/fragment_open_exit = 0x7f020008
com.fishkaster.app:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f070261
com.fishkaster.app:id/ghost_view = 0x7f0a011a
com.fishkaster.app:id/exo_content_frame = 0x7f0a00d3
com.fishkaster.app:color/m3_sys_color_light_surface_bright = 0x7f06020a
com.fishkaster.app:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f070260
com.fishkaster.app:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f07025d
com.fishkaster.app:attr/popupTheme = 0x7f0403c0
com.fishkaster.app:string/mtrl_picker_day_of_week_column_header = 0x7f1300fd
com.fishkaster.app:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700ed
com.fishkaster.app:dimen/material_emphasis_disabled = 0x7f070259
com.fishkaster.app:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f14043b
com.fishkaster.app:attr/navigationContentDescription = 0x7f040381
com.fishkaster.app:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f070189
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0601bf
com.fishkaster.app:dimen/material_divider_thickness = 0x7f070258
com.fishkaster.app:dimen/material_clock_period_toggle_width = 0x7f070254
com.fishkaster.app:string/catalyst_perf_monitor = 0x7f130043
com.fishkaster.app:layout/mtrl_layout_snackbar_include = 0x7f0d006a
com.fishkaster.app:dimen/material_clock_hand_stroke_width = 0x7f07024f
com.fishkaster.app:dimen/material_clock_hand_center_dot_radius = 0x7f07024d
com.fishkaster.app:id/accessibility_custom_action_15 = 0x7f0a001d
com.fishkaster.app:id/dragRight = 0x7f0a00b7
com.fishkaster.app:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f070241
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Small = 0x7f14002e
com.fishkaster.app:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f070240
com.fishkaster.app:attr/contentScrim = 0x7f04014b
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f07023e
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f07023d
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f07023a
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f070239
com.fishkaster.app:attr/collapseContentDescription = 0x7f0400ec
com.fishkaster.app:dimen/notification_content_margin_start = 0x7f070335
com.fishkaster.app:attr/stackFromEnd = 0x7f04043c
com.fishkaster.app:id/chain = 0x7f0a0081
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f070238
com.fishkaster.app:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070232
com.fishkaster.app:string/androidx_startup = 0x7f13001c
com.fishkaster.app:id/accessibility_links = 0x7f0a0038
com.fishkaster.app:attr/indicatorSize = 0x7f040276
com.fishkaster.app:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f070230
com.fishkaster.app:attr/buttonBarNeutralButtonStyle = 0x7f040098
com.fishkaster.app:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f07025f
com.fishkaster.app:color/m3_textfield_indicator_text_color = 0x7f06022a
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f07022e
com.fishkaster.app:attr/checkedChip = 0x7f0400be
com.fishkaster.app:macro/m3_comp_extended_fab_surface_container_color = 0x7f0e0032
com.fishkaster.app:layout/exo_player_view = 0x7f0d0038
com.fishkaster.app:string/bottomsheet_drag_handle_clicked = 0x7f130024
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f070227
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f070226
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f070224
com.fishkaster.app:string/material_clock_display_divider = 0x7f1300cb
com.fishkaster.app:string/m3_sys_motion_easing_legacy_accelerate = 0x7f1300c5
com.fishkaster.app:id/unlabeled = 0x7f0a0255
com.fishkaster.app:dimen/m3_timepicker_display_stroke_width = 0x7f070244
com.fishkaster.app:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070222
com.fishkaster.app:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f070221
com.fishkaster.app:style/Widget.Material3.BottomSheet.Modal = 0x7f140393
com.fishkaster.app:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f070220
com.fishkaster.app:styleable/MaterialShape = 0x7f15005e
com.fishkaster.app:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0e0056
com.fishkaster.app:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f07021c
com.fishkaster.app:color/m3_timepicker_display_text_color = 0x7f060234
com.fishkaster.app:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0e013f
com.fishkaster.app:color/m3_dynamic_dark_default_color_secondary_text = 0x7f0600a3
com.fishkaster.app:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f070318
com.fishkaster.app:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0e0125
com.fishkaster.app:id/action_container = 0x7f0a0047
com.fishkaster.app:attr/layout_constraintGuide_begin = 0x7f0402c3
com.fishkaster.app:dimen/m3_sys_elevation_level3 = 0x7f070219
com.fishkaster.app:id/exo_subtitle = 0x7f0a00f6
com.fishkaster.app:attr/iconPadding = 0x7f040266
com.fishkaster.app:dimen/m3_sys_elevation_level2 = 0x7f070218
com.fishkaster.app:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1403cb
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f140037
com.fishkaster.app:attr/arrowHeadLength = 0x7f04003f
com.fishkaster.app:dimen/m3_sys_elevation_level1 = 0x7f070217
com.fishkaster.app:attr/motionDurationExtraLong1 = 0x7f04035a
com.fishkaster.app:dimen/m3_sys_elevation_level0 = 0x7f070216
com.fishkaster.app:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0e00b4
com.fishkaster.app:dimen/material_clock_face_margin_bottom = 0x7f07024b
com.fishkaster.app:attr/badgeWithTextHeight = 0x7f04006a
com.fishkaster.app:dimen/m3_snackbar_action_text_color_alpha = 0x7f070214
com.fishkaster.app:layout/select_dialog_singlechoice_material = 0x7f0d008d
com.fishkaster.app:id/mtrl_internal_children_alpha_tag = 0x7f0a017f
com.fishkaster.app:dimen/m3_small_fab_size = 0x7f070213
com.fishkaster.app:string/m3_sys_motion_easing_standard = 0x7f1300c8
com.fishkaster.app:dimen/m3_searchview_height = 0x7f07020a
com.fishkaster.app:dimen/m3_searchview_elevation = 0x7f070209
com.fishkaster.app:attr/thumbElevation = 0x7f0404c2
com.fishkaster.app:drawable/exo_controls_rewind = 0x7f0800af
com.fishkaster.app:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f070206
com.fishkaster.app:dimen/m3_comp_progress_indicator_track_thickness = 0x7f07018a
com.fishkaster.app:color/m3_sys_color_dynamic_light_error_container = 0x7f0601c6
com.fishkaster.app:attr/motionEasingStandard = 0x7f040372
com.fishkaster.app:dimen/m3_searchbar_margin_horizontal = 0x7f070202
com.fishkaster.app:id/save_overlay_view = 0x7f0a01e2
com.fishkaster.app:dimen/m3_searchbar_height = 0x7f070201
com.fishkaster.app:color/vector_tint_color = 0x7f06032e
com.fishkaster.app:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0701ff
com.fishkaster.app:dimen/mtrl_extended_fab_icon_size = 0x7f0702d2
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f140468
com.fishkaster.app:attr/cropMaxCropResultHeightPX = 0x7f040177
com.fishkaster.app:dimen/design_snackbar_padding_vertical = 0x7f070089
com.fishkaster.app:dimen/m3_ripple_default_alpha = 0x7f0701fb
com.fishkaster.app:attr/ttcIndex = 0x7f040510
com.fishkaster.app:styleable/AppCompatSeekBar = 0x7f150010
com.fishkaster.app:attr/closeIconSize = 0x7f0400e7
com.fishkaster.app:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0701f9
com.fishkaster.app:dimen/m3_navigation_rail_item_min_height = 0x7f0701f5
com.fishkaster.app:integer/m3_sys_motion_duration_short3 = 0x7f0b0020
com.fishkaster.app:drawable/abc_textfield_default_mtrl_alpha = 0x7f080072
com.fishkaster.app:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0701f4
com.fishkaster.app:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0701f2
com.fishkaster.app:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0e0122
com.fishkaster.app:attr/badgeWithTextRadius = 0x7f04006b
com.fishkaster.app:xml/file_system_provider_paths = 0x7f160001
com.fishkaster.app:attr/latLngBoundsNorthEastLatitude = 0x7f0402a8
com.fishkaster.app:style/TextAppearance.Compat.Notification.Title.Media = 0x7f1401f9
com.fishkaster.app:id/ignore = 0x7f0a0134
com.fishkaster.app:dimen/m3_navigation_rail_elevation = 0x7f0701f0
com.fishkaster.app:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0701ed
com.fishkaster.app:string/common_google_play_services_update_button = 0x7f13005d
com.fishkaster.app:dimen/m3_navigation_item_shape_inset_end = 0x7f0701e9
com.fishkaster.app:dimen/m3_navigation_item_icon_padding = 0x7f0701e7
com.fishkaster.app:attr/trackColorInactive = 0x7f0404fd
com.fishkaster.app:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f140151
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1403e9
com.fishkaster.app:style/Widget.Material3.Button.TonalButton.Icon = 0x7f1403a5
com.fishkaster.app:drawable/$m3_avd_hide_password__2 = 0x7f080008
com.fishkaster.app:animator/design_fab_show_motion_spec = 0x7f020002
com.fishkaster.app:attr/percentHeight = 0x7f0403aa
com.fishkaster.app:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0701e5
com.fishkaster.app:color/m3_ref_palette_dynamic_primary70 = 0x7f0600fc
com.fishkaster.app:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0701e3
com.fishkaster.app:macro/m3_comp_radio_button_selected_icon_color = 0x7f0e00dc
com.fishkaster.app:attr/simpleItemSelectedColor = 0x7f04042b
com.fishkaster.app:style/Widget.AppCompat.RatingBar.Small = 0x7f140362
com.fishkaster.app:attr/motionInterpolator = 0x7f040376
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600db
com.fishkaster.app:dimen/m3_menu_elevation = 0x7f0701e2
com.fishkaster.app:dimen/m3_large_fab_size = 0x7f0701e0
com.fishkaster.app:dimen/m3_large_fab_max_image_size = 0x7f0701df
com.fishkaster.app:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1402c1
com.fishkaster.app:attr/latLngBoundsNorthEastLongitude = 0x7f0402a9
com.fishkaster.app:layout/design_layout_tab_text = 0x7f0d002b
com.fishkaster.app:dimen/m3_fab_border_width = 0x7f0701db
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f14015d
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0601de
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.CC = 0x7f140136
com.fishkaster.app:dimen/m3_extended_fab_icon_padding = 0x7f0701d7
com.fishkaster.app:dimen/m3_divider_heavy_thickness = 0x7f0701d4
com.fishkaster.app:attr/buttonIconTintMode = 0x7f0400a0
com.fishkaster.app:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1400a0
com.fishkaster.app:dimen/m3_datepicker_elevation = 0x7f0701d3
com.fishkaster.app:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1400aa
com.fishkaster.app:style/Widget.Material3.Button.IconButton = 0x7f140398
com.fishkaster.app:dimen/tooltip_margin = 0x7f070343
com.fishkaster.app:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f140299
com.fishkaster.app:attr/dropDownListViewStyle = 0x7f0401c6
com.fishkaster.app:drawable/mtrl_ic_check_mark = 0x7f08013c
com.fishkaster.app:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0701d0
com.fishkaster.app:color/material_dynamic_neutral70 = 0x7f060250
com.fishkaster.app:color/m3_card_foreground_color = 0x7f060091
com.fishkaster.app:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0701ce
com.fishkaster.app:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0701ca
com.fishkaster.app:attr/layout_editor_absoluteX = 0x7f0402e1
com.fishkaster.app:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0701cc
com.fishkaster.app:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0702b6
com.fishkaster.app:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0701c5
com.fishkaster.app:attr/motionPathRotate = 0x7f040378
com.fishkaster.app:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f0701c1
com.fishkaster.app:attr/shapeAppearanceCornerExtraSmall = 0x7f040409
com.fishkaster.app:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f0701c0
com.fishkaster.app:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f07021d
com.fishkaster.app:color/m3_sys_color_light_surface = 0x7f060209
com.fishkaster.app:color/m3_tabs_ripple_color = 0x7f060222
com.fishkaster.app:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f0701bf
com.fishkaster.app:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f0701b8
com.fishkaster.app:styleable/CustomAttribute = 0x7f150030
com.fishkaster.app:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f0701b5
com.fishkaster.app:string/snackbar_pane_title = 0x7f13013a
com.fishkaster.app:color/material_dynamic_primary30 = 0x7f060266
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0e00fb
com.fishkaster.app:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f07031a
com.fishkaster.app:dimen/m3_comp_suggestion_chip_container_height = 0x7f0701b0
com.fishkaster.app:attr/svg = 0x7f040464
com.fishkaster.app:dimen/m3_comp_slider_stop_indicator_size = 0x7f0701ae
com.fishkaster.app:attr/cropScaleType = 0x7f040180
com.fishkaster.app:attr/flow_lastVerticalBias = 0x7f04022d
com.fishkaster.app:attr/helperTextEnabled = 0x7f040250
com.fishkaster.app:attr/startIconMinSize = 0x7f040442
com.fishkaster.app:dimen/m3_comp_slider_inactive_track_height = 0x7f0701ad
com.fishkaster.app:string/m3_sys_motion_easing_legacy_decelerate = 0x7f1300c6
com.fishkaster.app:dimen/material_textinput_default_width = 0x7f070267
com.fishkaster.app:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f0701ab
com.fishkaster.app:color/m3_ref_palette_error100 = 0x7f06011d
com.fishkaster.app:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f07017a
com.fishkaster.app:attr/tickVisible = 0x7f0404d8
com.fishkaster.app:dimen/m3_comp_slider_active_handle_height = 0x7f0701a7
com.fishkaster.app:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f0701b3
com.fishkaster.app:color/m3_ref_palette_tertiary80 = 0x7f060170
com.fishkaster.app:layout/mtrl_auto_complete_simple_item = 0x7f0d005e
com.fishkaster.app:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f0701a2
com.fishkaster.app:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f140244
com.fishkaster.app:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f0701a1
com.fishkaster.app:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f07019f
com.fishkaster.app:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f07019b
com.fishkaster.app:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f070170
com.fishkaster.app:color/switch_thumb_normal_material_light = 0x7f06032b
com.fishkaster.app:attr/cameraMaxZoomPreference = 0x7f0400a8
com.fishkaster.app:dimen/m3_comp_search_bar_container_height = 0x7f070196
com.fishkaster.app:attr/layout_constraintBaseline_toBaselineOf = 0x7f0402b9
com.fishkaster.app:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f140051
com.fishkaster.app:attr/materialButtonToggleGroupStyle = 0x7f040318
com.fishkaster.app:dimen/m3_comp_search_bar_avatar_size = 0x7f070194
com.fishkaster.app:dimen/m3_alert_dialog_icon_margin = 0x7f0700c7
com.fishkaster.app:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1400dd
com.fishkaster.app:attr/cropCornerCircleFillColor = 0x7f04016e
com.fishkaster.app:dimen/m3_badge_size = 0x7f0700db
com.fishkaster.app:dimen/m3_comp_navigation_bar_container_elevation = 0x7f07015e
com.fishkaster.app:dimen/m3_alert_dialog_corner_size = 0x7f0700c5
com.fishkaster.app:dimen/m3_comp_scrim_container_opacity = 0x7f070193
com.fishkaster.app:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f070192
com.fishkaster.app:attr/behavior_saveFlags = 0x7f04007e
com.fishkaster.app:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f070191
com.fishkaster.app:integer/mtrl_switch_track_viewport_width = 0x7f0b0040
com.fishkaster.app:attr/checkedIconGravity = 0x7f0400c1
com.fishkaster.app:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080040
com.fishkaster.app:dimen/hint_alpha_material_light = 0x7f0700bd
com.fishkaster.app:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f07018f
com.fishkaster.app:color/m3_ref_palette_neutral95 = 0x7f06013c
com.fishkaster.app:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f070185
com.fishkaster.app:id/withText = 0x7f0a0269
com.fishkaster.app:string/catalyst_debug_error = 0x7f130035
com.fishkaster.app:dimen/notification_media_narrow_margin = 0x7f070339
com.fishkaster.app:dimen/m3_comp_switch_track_width = 0x7f0701be
com.fishkaster.app:dimen/design_navigation_padding_bottom = 0x7f07007f
com.fishkaster.app:attr/contentInsetEndWithActions = 0x7f04013f
com.fishkaster.app:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f070182
com.fishkaster.app:dimen/m3_comp_outlined_text_field_outline_width = 0x7f07017f
com.fishkaster.app:style/Widget.MaterialComponents.BottomNavigationView = 0x7f14043d
com.fishkaster.app:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f07017e
com.fishkaster.app:dimen/mtrl_fab_translation_z_pressed = 0x7f0702df
com.fishkaster.app:dimen/m3_simple_item_color_selected_alpha = 0x7f070210
com.fishkaster.app:attr/checkedIconTint = 0x7f0400c4
com.fishkaster.app:attr/percentWidth = 0x7f0403ab
com.fishkaster.app:layout/abc_select_dialog_material = 0x7f0d001a
com.fishkaster.app:id/SHOW_ALL = 0x7f0a000b
com.fishkaster.app:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f07017c
com.fishkaster.app:dimen/mtrl_calendar_text_input_padding_top = 0x7f0702b9
com.fishkaster.app:attr/roundWithOverlayColor = 0x7f0403ed
com.fishkaster.app:dimen/m3_comp_outlined_card_icon_size = 0x7f070178
com.fishkaster.app:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1400d1
com.fishkaster.app:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0e016e
com.fishkaster.app:dimen/m3_comp_outlined_button_outline_width = 0x7f070175
com.fishkaster.app:dimen/m3_bottomappbar_height = 0x7f0700ef
com.fishkaster.app:id/expanded_menu = 0x7f0a00fd
com.fishkaster.app:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080036
com.fishkaster.app:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f070173
com.fishkaster.app:dimen/m3_comp_navigation_rail_icon_size = 0x7f070171
com.fishkaster.app:style/ThemeOverlay.AppCompat = 0x7f1402b3
com.fishkaster.app:integer/config_tooltipAnimTime = 0x7f0b0005
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f0601ba
com.fishkaster.app:dimen/m3_comp_navigation_rail_container_width = 0x7f07016e
com.fishkaster.app:style/Base.TextAppearance.MaterialComponents.Button = 0x7f140048
com.fishkaster.app:color/material_blue_grey_800 = 0x7f060239
com.fishkaster.app:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f070169
com.fishkaster.app:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f140267
com.fishkaster.app:dimen/m3_comp_navigation_drawer_icon_size = 0x7f070167
com.fishkaster.app:color/m3_sys_color_dynamic_dark_secondary = 0x7f0601b7
com.fishkaster.app:attr/actionModeCloseButtonStyle = 0x7f040012
com.fishkaster.app:drawable/$m3_avd_show_password__0 = 0x7f080009
com.fishkaster.app:id/browser_actions_header_text = 0x7f0a0071
com.fishkaster.app:color/exo_white_opacity_70 = 0x7f06007b
com.fishkaster.app:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f070166
com.fishkaster.app:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f1402a2
com.fishkaster.app:dimen/m3_comp_navigation_bar_icon_size = 0x7f070162
com.fishkaster.app:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f070161
com.fishkaster.app:attr/flow_firstVerticalBias = 0x7f040225
com.fishkaster.app:color/switch_thumb_normal_material_dark = 0x7f06032a
com.fishkaster.app:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f070160
com.fishkaster.app:color/m3_textfield_input_text_color = 0x7f06022b
com.fishkaster.app:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f07015c
com.fishkaster.app:dimen/mtrl_chip_pressed_translation_z = 0x7f0702c7
com.fishkaster.app:attr/queryPatterns = 0x7f0403cf
com.fishkaster.app:attr/tickMark = 0x7f0404d3
com.fishkaster.app:dimen/mtrl_btn_icon_padding = 0x7f070287
com.fishkaster.app:attr/actionModeStyle = 0x7f04001d
com.fishkaster.app:dimen/m3_comp_menu_container_elevation = 0x7f07015b
com.fishkaster.app:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0e00d7
com.fishkaster.app:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0702e9
com.fishkaster.app:attr/selectionRequired = 0x7f040405
com.fishkaster.app:attr/actionBarSize = 0x7f040004
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Button = 0x7f14001c
com.fishkaster.app:color/m3_dynamic_default_color_primary_text = 0x7f0600a7
com.fishkaster.app:color/common_google_signin_btn_text_dark_focused = 0x7f06003a
com.fishkaster.app:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f070158
com.fishkaster.app:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f070154
com.fishkaster.app:attr/layout_goneMarginBottom = 0x7f0402e3
com.fishkaster.app:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f070150
com.fishkaster.app:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f07014f
com.fishkaster.app:styleable/MotionScene = 0x7f15006b
com.fishkaster.app:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1400f6
com.fishkaster.app:attr/barrierAllowsGoneWidgets = 0x7f040072
com.fishkaster.app:dimen/design_bottom_navigation_label_padding = 0x7f07006a
com.fishkaster.app:color/primary_text_disabled_material_dark = 0x7f06031d
com.fishkaster.app:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f07014c
com.fishkaster.app:style/Widget.Material3.NavigationRailView = 0x7f140400
com.fishkaster.app:attr/colorOnPrimary = 0x7f040106
com.fishkaster.app:color/m3_sys_color_dark_background = 0x7f060180
com.fishkaster.app:color/m3_sys_color_light_outline_variant = 0x7f060204
com.fishkaster.app:dimen/m3_comp_filled_card_container_elevation = 0x7f07014a
com.fishkaster.app:attr/materialSwitchStyle = 0x7f04033a
com.fishkaster.app:dimen/m3_comp_filled_button_container_elevation = 0x7f070148
com.fishkaster.app:id/accessibility_custom_action_19 = 0x7f0a0021
com.fishkaster.app:macro/m3_comp_snackbar_supporting_text_type = 0x7f0e0116
com.fishkaster.app:dimen/m3_comp_fab_primary_small_icon_size = 0x7f070146
com.fishkaster.app:color/material_personalized_color_surface_container_high = 0x7f0602c0
com.fishkaster.app:styleable/OnClick = 0x7f150078
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f070225
com.fishkaster.app:id/CropProgressBar = 0x7f0a0005
com.fishkaster.app:dimen/m3_btn_icon_only_min_width = 0x7f0700fc
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f070136
com.fishkaster.app:attr/telltales_tailColor = 0x7f040489
com.fishkaster.app:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f070187
com.fishkaster.app:dimen/m3_comp_fab_primary_small_container_height = 0x7f070145
com.fishkaster.app:string/path_password_strike_through = 0x7f130128
com.fishkaster.app:anim/abc_slide_out_bottom = 0x7f010008
com.fishkaster.app:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0702ce
com.fishkaster.app:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f070152
com.fishkaster.app:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f070143
com.fishkaster.app:dimen/m3_comp_fab_primary_large_container_height = 0x7f070141
com.fishkaster.app:color/material_on_background_emphasis_medium = 0x7f060296
com.fishkaster.app:dimen/m3_comp_fab_primary_icon_size = 0x7f070140
com.fishkaster.app:style/Theme.AppCompat.Dialog.MinWidth = 0x7f14024a
com.fishkaster.app:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f07013d
com.fishkaster.app:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f0701bb
com.fishkaster.app:attr/title = 0x7f0404dd
com.fishkaster.app:dimen/m3_comp_fab_primary_container_height = 0x7f07013c
com.fishkaster.app:dimen/m3_comp_elevated_card_icon_size = 0x7f070131
com.fishkaster.app:drawable/exo_controls_play = 0x7f0800aa
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0601d2
com.fishkaster.app:attr/autoTransition = 0x7f04004a
com.fishkaster.app:dimen/m3_btn_padding_bottom = 0x7f0700ff
com.fishkaster.app:dimen/m3_comp_elevated_card_container_elevation = 0x7f070130
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f07022d
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1403f2
com.fishkaster.app:dimen/design_bottom_navigation_icon_size = 0x7f070067
com.fishkaster.app:dimen/m3_comp_bottom_app_bar_container_height = 0x7f070128
com.fishkaster.app:color/abc_hint_foreground_material_dark = 0x7f060007
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0e0165
com.fishkaster.app:id/accessibility_custom_action_16 = 0x7f0a001e
com.fishkaster.app:attr/backgroundInsetEnd = 0x7f040056
com.fishkaster.app:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f07026c
com.fishkaster.app:dimen/m3_comp_badge_large_size = 0x7f070125
com.fishkaster.app:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f140047
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral6 = 0x7f0600d0
com.fishkaster.app:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f070121
com.fishkaster.app:id/clockwise = 0x7f0a008a
com.fishkaster.app:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f070123
com.fishkaster.app:id/SYM = 0x7f0a000e
com.fishkaster.app:color/design_dark_default_color_on_error = 0x7f060047
com.fishkaster.app:dimen/fastscroll_default_thickness = 0x7f0700b6
com.fishkaster.app:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f14044d
com.fishkaster.app:dimen/m3_chip_disabled_translation_z = 0x7f07011b
com.fishkaster.app:attr/flow_verticalAlign = 0x7f040231
com.fishkaster.app:dimen/m3_btn_text_btn_icon_padding_right = 0x7f070105
com.fishkaster.app:dimen/m3_chip_corner_size = 0x7f07011a
com.fishkaster.app:attr/mock_labelBackgroundColor = 0x7f040355
com.fishkaster.app:dimen/m3_chip_checked_hovered_translation_z = 0x7f070119
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker = 0x7f14049c
com.fishkaster.app:dimen/m3_carousel_gone_size = 0x7f070115
com.fishkaster.app:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1404a2
com.fishkaster.app:dimen/m3_carousel_debug_keyline_width = 0x7f070113
com.fishkaster.app:attr/colorSurfaceDim = 0x7f04012d
com.fishkaster.app:dimen/m3_card_stroke_width = 0x7f070112
com.fishkaster.app:color/material_personalized_color_on_error = 0x7f0602a7
com.fishkaster.app:dimen/m3_card_elevated_dragged_z = 0x7f07010d
com.fishkaster.app:drawable/exo_ic_audiotrack = 0x7f0800b4
com.fishkaster.app:layout/design_layout_tab_icon = 0x7f0d002a
com.fishkaster.app:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0e00a5
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f140158
com.fishkaster.app:layout/design_text_input_end_icon = 0x7f0d0033
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f08000f
com.fishkaster.app:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0e00d6
com.fishkaster.app:attr/lastItemDecorated = 0x7f0402a7
com.fishkaster.app:dimen/m3_btn_translation_z_hovered = 0x7f070109
com.fishkaster.app:attr/iconTint = 0x7f040269
com.fishkaster.app:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070243
com.fishkaster.app:color/material_personalized__highlighted_text = 0x7f06029e
com.fishkaster.app:attr/searchIcon = 0x7f0403ff
com.fishkaster.app:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0e0009
com.fishkaster.app:dimen/m3_btn_translation_z_base = 0x7f070108
com.fishkaster.app:dimen/m3_btn_text_btn_padding_left = 0x7f070106
com.fishkaster.app:attr/autoShowKeyboard = 0x7f040044
com.fishkaster.app:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0601ee
com.fishkaster.app:style/Widget.Material3.Slider.Label = 0x7f140414
com.fishkaster.app:dimen/m3_extended_fab_min_height = 0x7f0701d8
com.fishkaster.app:string/mtrl_switch_thumb_path_checked = 0x7f130119
com.fishkaster.app:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1402d1
com.fishkaster.app:dimen/m3_btn_padding_top = 0x7f070102
com.fishkaster.app:dimen/m3_btn_padding_right = 0x7f070101
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary100 = 0x7f060110
com.fishkaster.app:macro/m3_comp_filled_button_container_color = 0x7f0e0043
com.fishkaster.app:style/Widget.Design.FloatingActionButton = 0x7f14037b
com.fishkaster.app:id/asConfigured = 0x7f0a0060
com.fishkaster.app:dimen/m3_btn_icon_only_default_size = 0x7f0700fa
com.fishkaster.app:attr/popupWindowStyle = 0x7f0403c1
com.fishkaster.app:dimen/mtrl_calendar_action_padding = 0x7f070299
com.fishkaster.app:dimen/design_snackbar_text_size = 0x7f07008b
com.fishkaster.app:animator/design_fab_hide_motion_spec = 0x7f020001
com.fishkaster.app:dimen/m3_btn_icon_btn_padding_left = 0x7f0700f7
com.fishkaster.app:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0e0010
com.fishkaster.app:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f140405
com.fishkaster.app:attr/cornerSizeBottomRight = 0x7f04015a
com.fishkaster.app:color/m3_tabs_text_color = 0x7f060224
com.fishkaster.app:dimen/m3_btn_elevated_btn_elevation = 0x7f0700f5
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1403eb
com.fishkaster.app:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f14034c
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Body2 = 0x7f14001b
com.fishkaster.app:string/autofill = 0x7f13001f
com.fishkaster.app:dimen/m3_btn_dialog_btn_min_width = 0x7f0700f1
com.fishkaster.app:styleable/ShapeAppearance = 0x7f150089
com.fishkaster.app:attr/fabAnimationMode = 0x7f040205
com.fishkaster.app:dimen/m3_bottomappbar_fab_end_margin = 0x7f0700ee
com.fishkaster.app:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.fishkaster.app:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700ec
com.fishkaster.app:dimen/m3_bottom_sheet_elevation = 0x7f0700e9
com.fishkaster.app:attr/colorControlHighlight = 0x7f0400fd
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary0 = 0x7f060101
com.fishkaster.app:macro/m3_comp_fab_surface_container_color = 0x7f0e003d
com.fishkaster.app:style/Widget.Material3.SideSheet.Modal = 0x7f140411
com.fishkaster.app:attr/textAppearanceDisplayLarge = 0x7f040494
com.fishkaster.app:dimen/m3_bottom_nav_min_height = 0x7f0700e7
com.fishkaster.app:style/Widget.Material3.Button.TextButton = 0x7f14039e
com.fishkaster.app:attr/sideSheetDialogTheme = 0x7f040428
com.fishkaster.app:color/material_dynamic_primary80 = 0x7f06026b
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f140173
com.fishkaster.app:attr/colorErrorContainer = 0x7f040100
com.fishkaster.app:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700e5
com.fishkaster.app:bool/mtrl_btn_textappearance_all_caps = 0x7f050005
com.fishkaster.app:style/Platform.MaterialComponents.Dialog = 0x7f140163
com.fishkaster.app:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f0701c2
com.fishkaster.app:styleable/GradientColorItem = 0x7f150040
com.fishkaster.app:attr/statusBarBackground = 0x7f04044f
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0e0087
com.fishkaster.app:dimen/material_clock_hand_padding = 0x7f07024e
com.fishkaster.app:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700e2
com.fishkaster.app:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f140480
com.fishkaster.app:drawable/exo_rounded_rectangle = 0x7f0800dd
com.fishkaster.app:dimen/m3_badge_with_text_size = 0x7f0700df
com.fishkaster.app:attr/textColorSearchUrl = 0x7f0404b3
com.fishkaster.app:attr/endIconMode = 0x7f0401d7
com.fishkaster.app:dimen/m3_badge_horizontal_offset = 0x7f0700d9
com.fishkaster.app:styleable/ExtendedFloatingActionButton = 0x7f150034
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0601c1
com.fishkaster.app:dimen/abc_list_item_height_small_material = 0x7f070032
com.fishkaster.app:dimen/m3_appbar_scrim_height_trigger = 0x7f0700cc
com.fishkaster.app:dimen/m3_alert_dialog_elevation = 0x7f0700c6
com.fishkaster.app:dimen/m3_alert_dialog_action_top_padding = 0x7f0700c4
com.fishkaster.app:attr/boxStrokeWidth = 0x7f040092
com.fishkaster.app:dimen/hint_pressed_alpha_material_light = 0x7f0700bf
com.fishkaster.app:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f14006c
com.fishkaster.app:color/material_personalized_color_primary_text = 0x7f0602b7
com.fishkaster.app:dimen/hint_pressed_alpha_material_dark = 0x7f0700be
com.fishkaster.app:attr/colorSurface = 0x7f040126
com.fishkaster.app:attr/colorOnPrimarySurface = 0x7f04010a
com.fishkaster.app:style/Widget.MaterialComponents.Snackbar = 0x7f140489
com.fishkaster.app:attr/textInputOutlinedDenseStyle = 0x7f0404b9
com.fishkaster.app:dimen/exo_styled_progress_touch_target_height = 0x7f0700b5
com.fishkaster.app:dimen/exo_styled_progress_bar_height = 0x7f0700b0
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f07023b
com.fishkaster.app:string/catalyst_dev_menu_sub_header = 0x7f130039
com.fishkaster.app:dimen/exo_styled_controls_padding = 0x7f0700ae
com.fishkaster.app:attr/actionModeCutDrawable = 0x7f040016
com.fishkaster.app:attr/drawableLeftCompat = 0x7f0401bb
com.fishkaster.app:dimen/exo_styled_bottom_bar_margin_top = 0x7f0700ac
com.fishkaster.app:color/mtrl_chip_surface_color = 0x7f0602eb
com.fishkaster.app:dimen/exo_styled_bottom_bar_height = 0x7f0700ab
com.fishkaster.app:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1401cc
com.fishkaster.app:attr/mapId = 0x7f04030a
com.fishkaster.app:style/Animation.AppCompat.DropDownUp = 0x7f140003
com.fishkaster.app:dimen/exo_small_icon_width = 0x7f0700aa
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.AudioTrack = 0x7f140135
com.fishkaster.app:dimen/exo_small_icon_padding_vertical = 0x7f0700a9
com.fishkaster.app:dimen/exo_small_icon_padding_horizontal = 0x7f0700a8
com.fishkaster.app:drawable/x_close = 0x7f080173
com.fishkaster.app:dimen/mtrl_calendar_navigation_top_padding = 0x7f0702b3
com.fishkaster.app:attr/bottomAppBarStyle = 0x7f040083
com.fishkaster.app:anim/catalyst_push_up_out = 0x7f01001b
com.fishkaster.app:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f07012a
com.fishkaster.app:dimen/exo_small_icon_horizontal_margin = 0x7f0700a7
com.fishkaster.app:color/material_deep_teal_200 = 0x7f06023d
com.fishkaster.app:dimen/exo_settings_offset = 0x7f0700a3
com.fishkaster.app:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f140184
com.fishkaster.app:string/common_google_play_services_wear_update_text = 0x7f130061
com.fishkaster.app:attr/motionEasingStandardDecelerateInterpolator = 0x7f040374
com.fishkaster.app:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f070279
com.fishkaster.app:dimen/item_touch_helper_swipe_escape_velocity = 0x7f0700c2
com.fishkaster.app:attr/labelBehavior = 0x7f0402a2
com.fishkaster.app:id/navigation_header_container = 0x7f0a0193
com.fishkaster.app:dimen/abc_control_inset_material = 0x7f070019
com.fishkaster.app:dimen/exo_settings_main_text_size = 0x7f0700a2
com.fishkaster.app:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0701c7
com.fishkaster.app:dimen/exo_settings_icon_size = 0x7f0700a1
com.fishkaster.app:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1402e1
com.fishkaster.app:dimen/mtrl_navigation_rail_icon_margin = 0x7f0702f5
com.fishkaster.app:dimen/exo_setting_width = 0x7f07009f
com.fishkaster.app:id/textinput_helper_text = 0x7f0a0239
com.fishkaster.app:string/pick_image_gallery = 0x7f13012b
com.fishkaster.app:color/material_dynamic_tertiary90 = 0x7f060286
com.fishkaster.app:dimen/exo_media_button_width = 0x7f07009e
com.fishkaster.app:attr/tickColor = 0x7f0404d0
com.fishkaster.app:dimen/exo_media_button_height = 0x7f07009d
com.fishkaster.app:attr/dialogTheme = 0x7f0401ab
com.fishkaster.app:dimen/exo_icon_padding_bottom = 0x7f07009a
com.fishkaster.app:style/Theme.MaterialComponents.NoActionBar = 0x7f1402ae
com.fishkaster.app:dimen/abc_star_big = 0x7f07003b
com.fishkaster.app:style/MaterialAlertDialog.Material3.Body.Text = 0x7f14014d
com.fishkaster.app:dimen/exo_icon_padding = 0x7f070099
com.fishkaster.app:attr/trackHeight = 0x7f040502
com.fishkaster.app:dimen/exo_error_message_text_padding_horizontal = 0x7f070095
com.fishkaster.app:style/Widget.Design.TextInputEditText = 0x7f140380
com.fishkaster.app:dimen/exo_error_message_margin_bottom = 0x7f070094
com.fishkaster.app:dimen/design_textinput_caption_translate_y = 0x7f070090
com.fishkaster.app:color/mtrl_switch_thumb_tint = 0x7f060306
com.fishkaster.app:dimen/design_snackbar_padding_vertical_2lines = 0x7f07008a
com.fishkaster.app:integer/m3_card_anim_duration_ms = 0x7f0b0010
com.fishkaster.app:color/m3_tabs_icon_color_secondary = 0x7f060221
com.fishkaster.app:dimen/design_snackbar_padding_horizontal = 0x7f070088
com.fishkaster.app:dimen/design_snackbar_max_width = 0x7f070086
com.fishkaster.app:attr/boxStrokeColor = 0x7f040090
com.fishkaster.app:drawable/common_google_signin_btn_icon_light_normal = 0x7f080091
com.fishkaster.app:id/accessibility_state_expanded = 0x7f0a003d
com.fishkaster.app:dimen/mtrl_fab_elevation = 0x7f0702dc
com.fishkaster.app:attr/errorTextColor = 0x7f0401e8
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.Settings = 0x7f14013c
com.fishkaster.app:dimen/design_snackbar_action_text_color_alpha = 0x7f070082
com.fishkaster.app:color/abc_tint_edittext = 0x7f060015
com.fishkaster.app:dimen/design_navigation_separator_vertical_padding = 0x7f070080
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f140301
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f140097
com.fishkaster.app:string/mtrl_picker_toggle_to_text_input_mode = 0x7f130116
com.fishkaster.app:string/timer_description = 0x7f13014c
com.fishkaster.app:dimen/design_navigation_max_width = 0x7f07007e
com.fishkaster.app:dimen/design_navigation_item_vertical_padding = 0x7f07007d
com.fishkaster.app:layout/abc_popup_menu_item_layout = 0x7f0d0013
com.fishkaster.app:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0702be
com.fishkaster.app:dimen/m3_comp_slider_active_handle_width = 0x7f0701a9
com.fishkaster.app:string/radiogroup_description = 0x7f13012d
com.fishkaster.app:attr/touch_target_height = 0x7f0404f9
com.fishkaster.app:dimen/design_navigation_item_icon_padding = 0x7f07007c
com.fishkaster.app:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.fishkaster.app:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f080127
com.fishkaster.app:dimen/design_navigation_icon_size = 0x7f07007a
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents = 0x7f140155
com.fishkaster.app:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0702fb
com.fishkaster.app:color/m3_sys_color_dark_on_tertiary = 0x7f06018f
com.fishkaster.app:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0601ed
com.fishkaster.app:dimen/design_fab_translation_z_pressed = 0x7f070077
com.fishkaster.app:attr/backgroundInsetBottom = 0x7f040055
com.fishkaster.app:dimen/m3_extended_fab_start_padding = 0x7f0701d9
com.fishkaster.app:string/common_google_play_services_unknown_issue = 0x7f13005b
com.fishkaster.app:attr/transitionShapeAppearance = 0x7f04050c
com.fishkaster.app:dimen/design_fab_elevation = 0x7f070072
com.fishkaster.app:dimen/tooltip_horizontal_padding = 0x7f070342
com.fishkaster.app:string/mtrl_timepicker_cancel = 0x7f130120
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0e00ca
com.fishkaster.app:dimen/design_bottom_sheet_peek_height_min = 0x7f070070
com.fishkaster.app:attr/expandedTitleMarginStart = 0x7f0401f1
com.fishkaster.app:dimen/design_bottom_sheet_modal_elevation = 0x7f07006f
com.fishkaster.app:dimen/design_bottom_sheet_elevation = 0x7f07006e
com.fishkaster.app:dimen/design_bottom_navigation_text_size = 0x7f07006d
com.fishkaster.app:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0e00f6
com.fishkaster.app:color/abc_tint_seek_thumb = 0x7f060016
com.fishkaster.app:dimen/design_bottom_navigation_margin = 0x7f07006b
com.fishkaster.app:styleable/MotionTelltales = 0x7f15006c
com.fishkaster.app:color/design_dark_default_color_primary_dark = 0x7f06004c
com.fishkaster.app:dimen/m3_side_sheet_width = 0x7f07020e
com.fishkaster.app:attr/thumbIconTint = 0x7f0404c6
com.fishkaster.app:dimen/design_bottom_navigation_elevation = 0x7f070065
com.fishkaster.app:attr/progressBarImageScaleType = 0x7f0403ca
com.fishkaster.app:dimen/m3_comp_search_view_container_elevation = 0x7f070199
com.fishkaster.app:dimen/compat_notification_large_icon_max_width = 0x7f07005f
com.fishkaster.app:styleable/AppCompatTextHelper = 0x7f150011
com.fishkaster.app:dimen/m3_comp_input_chip_container_elevation = 0x7f070156
com.fishkaster.app:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0701f3
com.fishkaster.app:dimen/compat_notification_large_icon_max_height = 0x7f07005e
com.fishkaster.app:attr/textInputStyle = 0x7f0404bc
com.fishkaster.app:id/none = 0x7f0a0196
com.fishkaster.app:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0700e8
com.fishkaster.app:dimen/m3_extended_fab_end_padding = 0x7f0701d6
com.fishkaster.app:dimen/compat_control_corner_material = 0x7f07005d
com.fishkaster.app:attr/materialCalendarMonth = 0x7f040323
com.fishkaster.app:style/Theme.Material3.DynamicColors.Dark = 0x7f140273
com.fishkaster.app:dimen/compat_button_inset_vertical_material = 0x7f07005a
com.fishkaster.app:style/Theme.AppCompat.Light = 0x7f14024d
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0e006f
com.fishkaster.app:dimen/compat_button_inset_horizontal_material = 0x7f070059
com.fishkaster.app:attr/cropSnapRadius = 0x7f040185
com.fishkaster.app:attr/passwordToggleEnabled = 0x7f0403a5
com.fishkaster.app:style/ShapeAppearance.MaterialComponents.Badge = 0x7f1401a6
com.fishkaster.app:attr/iconEndPadding = 0x7f040264
com.fishkaster.app:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f07019d
com.fishkaster.app:style/Widget.Design.BottomSheet.Modal = 0x7f140379
com.fishkaster.app:attr/motionEasingEmphasizedInterpolator = 0x7f04036f
com.fishkaster.app:dimen/cardview_default_radius = 0x7f070057
com.fishkaster.app:attr/collapsedTitleTextColor = 0x7f0400f1
com.fishkaster.app:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f140190
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f07013a
com.fishkaster.app:dimen/cardview_default_elevation = 0x7f070056
com.fishkaster.app:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f0701ba
com.fishkaster.app:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.fishkaster.app:dimen/cardview_compat_inset_shadow = 0x7f070055
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0e007f
com.fishkaster.app:dimen/browser_actions_context_menu_max_width = 0x7f070053
com.fishkaster.app:color/m3_sys_color_dynamic_primary_fixed = 0x7f0601ec
com.fishkaster.app:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f070331
com.fishkaster.app:style/Platform.V21.AppCompat = 0x7f140169
com.fishkaster.app:attr/expandedTitleGravity = 0x7f0401ed
com.fishkaster.app:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.fishkaster.app:attr/itemShapeAppearance = 0x7f04028d
com.fishkaster.app:color/cardview_shadow_start_color = 0x7f060032
com.fishkaster.app:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.fishkaster.app:attr/layout_constraintWidth_percent = 0x7f0402df
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f140306
com.fishkaster.app:dimen/abc_text_size_large_material = 0x7f070048
com.fishkaster.app:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f140445
com.fishkaster.app:id/adjust_width = 0x7f0a0055
com.fishkaster.app:dimen/abc_text_size_button_material = 0x7f070041
com.fishkaster.app:id/fitStart = 0x7f0a010c
com.fishkaster.app:dimen/abc_text_size_body_1_material = 0x7f07003f
com.fishkaster.app:anim/rns_no_animation_medium = 0x7f010045
com.fishkaster.app:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.fishkaster.app:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.fishkaster.app:dimen/abc_search_view_preferred_width = 0x7f070037
com.fishkaster.app:string/state_busy_description = 0x7f13013c
com.fishkaster.app:dimen/abc_search_view_preferred_height = 0x7f070036
com.fishkaster.app:dimen/m3_side_sheet_margin_detached = 0x7f07020b
com.fishkaster.app:style/ThemeOverlay.Material3 = 0x7f1402bd
com.fishkaster.app:attr/saturation = 0x7f0403f3
com.fishkaster.app:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.fishkaster.app:id/view_tag_native_id = 0x7f0a025d
com.fishkaster.app:dimen/mtrl_switch_track_height = 0x7f070320
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0600e2
com.fishkaster.app:dimen/design_snackbar_background_corner_radius = 0x7f070083
com.fishkaster.app:layout/abc_dialog_title_material = 0x7f0d000c
com.fishkaster.app:color/material_dynamic_neutral_variant60 = 0x7f06025c
com.fishkaster.app:dimen/abc_list_item_height_material = 0x7f070031
com.fishkaster.app:string/material_minute_selection = 0x7f1300d0
com.fishkaster.app:color/m3_sys_color_dark_tertiary_container = 0x7f0601a1
com.fishkaster.app:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600b5
com.fishkaster.app:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.fishkaster.app:dimen/mtrl_calendar_year_height = 0x7f0702bd
com.fishkaster.app:attr/ratingBarStyleIndicator = 0x7f0403d3
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_container_height = 0x7f070133
com.fishkaster.app:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.fishkaster.app:style/Widget.Material3.Button.Icon = 0x7f140397
com.fishkaster.app:styleable/Slider = 0x7f15008e
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f140172
com.fishkaster.app:dimen/m3_bottom_nav_item_padding_top = 0x7f0700e6
com.fishkaster.app:id/exo_minimal_controls = 0x7f0a00e0
com.fishkaster.app:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.fishkaster.app:attr/expandedTitleMarginTop = 0x7f0401f2
com.fishkaster.app:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.fishkaster.app:color/mtrl_indicator_text_color = 0x7f0602f7
com.fishkaster.app:dimen/m3_comp_navigation_drawer_container_width = 0x7f070164
com.fishkaster.app:dimen/abc_dialog_min_width_major = 0x7f070022
com.fishkaster.app:style/Widget.Material3.Button.OutlinedButton = 0x7f14039c
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_primary = 0x7f0601cd
com.fishkaster.app:style/TextAppearance.MaterialComponents.Caption = 0x7f14022d
com.fishkaster.app:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.fishkaster.app:animator/m3_btn_state_list_anim = 0x7f02000b
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f080019
com.fishkaster.app:attr/boxBackgroundMode = 0x7f04008a
com.fishkaster.app:dimen/abc_config_prefDialogWidth = 0x7f070017
com.fishkaster.app:style/redboxButton = 0x7f1404b0
com.fishkaster.app:attr/textStartPadding = 0x7f0404be
com.fishkaster.app:layout/notification_template_media_custom = 0x7f0d0084
com.fishkaster.app:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.fishkaster.app:attr/minHideDelay = 0x7f04034f
com.fishkaster.app:dimen/mtrl_calendar_bottom_padding = 0x7f07029a
com.fishkaster.app:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.fishkaster.app:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.fishkaster.app:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.fishkaster.app:attr/layout_constraintGuide_percent = 0x7f0402c5
com.fishkaster.app:attr/thumbHeight = 0x7f0404c3
com.fishkaster.app:color/m3_sys_color_dark_on_error_container = 0x7f060188
com.fishkaster.app:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.fishkaster.app:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.fishkaster.app:layout/abc_alert_dialog_button_bar_material = 0x7f0d0008
com.fishkaster.app:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.fishkaster.app:style/Widget.MaterialComponents.TextView = 0x7f14049b
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f140206
com.fishkaster.app:attr/liftOnScrollColor = 0x7f0402f0
com.fishkaster.app:dimen/abc_action_bar_elevation_material = 0x7f070005
com.fishkaster.app:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.fishkaster.app:color/accent_material_light = 0x7f06001a
com.fishkaster.app:drawable/$avd_show_password__1 = 0x7f080004
com.fishkaster.app:color/m3_sys_color_dynamic_light_tertiary = 0x7f0601e4
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f140475
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary99 = 0x7f06011a
com.fishkaster.app:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.fishkaster.app:id/one = 0x7f0a019e
com.fishkaster.app:id/scrollView = 0x7f0a01e9
com.fishkaster.app:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f14031f
com.fishkaster.app:color/tooltip_background_dark = 0x7f06032c
com.fishkaster.app:color/switch_thumb_material_dark = 0x7f060328
com.fishkaster.app:color/splashscreen_background = 0x7f060325
com.fishkaster.app:color/secondary_text_disabled_material_light = 0x7f060324
com.fishkaster.app:color/secondary_text_default_material_light = 0x7f060322
com.fishkaster.app:font/inter_semibold = 0x7f090003
com.fishkaster.app:dimen/mtrl_progress_circular_track_thickness_small = 0x7f070305
com.fishkaster.app:color/primary_text_disabled_material_light = 0x7f06031e
com.fishkaster.app:color/primary_material_light = 0x7f06031a
com.fishkaster.app:color/primary_dark_material_light = 0x7f060318
com.fishkaster.app:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f140355
com.fishkaster.app:color/notification_material_background_media_default_color = 0x7f060316
com.fishkaster.app:color/notification_icon_bg_color = 0x7f060315
com.fishkaster.app:color/mtrl_textinput_hovered_box_stroke_color = 0x7f060313
com.fishkaster.app:id/ic_flip_24_vertically = 0x7f0a012d
com.fishkaster.app:dimen/exo_error_message_text_size = 0x7f070097
com.fishkaster.app:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f07018c
com.fishkaster.app:color/mtrl_textinput_disabled_color = 0x7f060310
com.fishkaster.app:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f140118
com.fishkaster.app:color/mtrl_switch_track_decoration_tint = 0x7f060307
com.fishkaster.app:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0e0095
com.fishkaster.app:color/mtrl_switch_thumb_icon_tint = 0x7f060305
com.fishkaster.app:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f140056
com.fishkaster.app:dimen/abc_text_size_body_2_material = 0x7f070040
com.fishkaster.app:dimen/mtrl_calendar_day_today_stroke = 0x7f07029f
com.fishkaster.app:color/mtrl_navigation_bar_item_tint = 0x7f0602fa
com.fishkaster.app:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f140119
com.fishkaster.app:attr/motionDebug = 0x7f040359
com.fishkaster.app:dimen/material_clock_display_padding = 0x7f070249
com.fishkaster.app:color/mtrl_outlined_icon_tint = 0x7f060301
com.fishkaster.app:attr/materialCalendarHeaderCancelButton = 0x7f04031c
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0e015d
com.fishkaster.app:dimen/mtrl_navigation_item_icon_size = 0x7f0702ee
com.fishkaster.app:style/Base.Widget.AppCompat.PopupMenu = 0x7f1400ed
com.fishkaster.app:drawable/exo_controls_vr = 0x7f0800b2
com.fishkaster.app:attr/shapeAppearanceMediumComponent = 0x7f04040e
com.fishkaster.app:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0602ff
com.fishkaster.app:layout/notification_action = 0x7f0d0078
com.fishkaster.app:color/mtrl_navigation_item_background_color = 0x7f0602fc
com.fishkaster.app:attr/roundBottomEnd = 0x7f0403e4
com.fishkaster.app:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1400c7
com.fishkaster.app:color/m3_ref_palette_primary60 = 0x7f060154
com.fishkaster.app:color/mtrl_navigation_bar_ripple_color = 0x7f0602fb
com.fishkaster.app:styleable/SearchBar = 0x7f150087
com.fishkaster.app:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0e00e4
com.fishkaster.app:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f080083
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600c4
com.fishkaster.app:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0602f9
com.fishkaster.app:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f140089
com.fishkaster.app:color/mtrl_filled_stroke_color = 0x7f0602f6
com.fishkaster.app:attr/titleTextColor = 0x7f0404e9
com.fishkaster.app:color/call_notification_decline_color = 0x7f06002e
com.fishkaster.app:attr/chipBackgroundColor = 0x7f0400c8
com.fishkaster.app:color/mtrl_filled_icon_tint = 0x7f0602f5
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600e1
com.fishkaster.app:style/Base.Widget.AppCompat.ImageButton = 0x7f1400df
com.fishkaster.app:color/mtrl_filled_background_color = 0x7f0602f4
com.fishkaster.app:style/Theme.MaterialComponents.Light = 0x7f14029e
com.fishkaster.app:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0601ef
com.fishkaster.app:attr/cropMinCropResultHeightPX = 0x7f04017a
com.fishkaster.app:drawable/abc_dialog_material_background = 0x7f08003b
com.fishkaster.app:macro/m3_comp_outlined_text_field_caret_color = 0x7f0e00b0
com.fishkaster.app:drawable/mtrl_ic_cancel = 0x7f08013b
com.fishkaster.app:attr/cursorErrorColor = 0x7f04018e
com.fishkaster.app:color/mtrl_error = 0x7f0602f0
com.fishkaster.app:color/background_material_dark = 0x7f06001f
com.fishkaster.app:color/mtrl_fab_ripple_color = 0x7f0602f3
com.fishkaster.app:color/mtrl_choice_chip_ripple_color = 0x7f0602ee
com.fishkaster.app:color/mtrl_choice_chip_background_color = 0x7f0602ed
com.fishkaster.app:color/mtrl_chip_background_color = 0x7f0602e9
com.fishkaster.app:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0e0139
com.fishkaster.app:attr/yearTodayStyle = 0x7f04053a
com.fishkaster.app:attr/gapBetweenBars = 0x7f040247
com.fishkaster.app:color/mtrl_card_view_ripple = 0x7f0602e8
com.fishkaster.app:color/mtrl_btn_text_color_disabled = 0x7f0602e2
com.fishkaster.app:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1402eb
com.fishkaster.app:color/mtrl_btn_text_btn_ripple_color = 0x7f0602e1
com.fishkaster.app:style/Base.AlertDialog.AppCompat.Light = 0x7f14000f
com.fishkaster.app:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f070159
com.fishkaster.app:color/mtrl_btn_stroke_color_selector = 0x7f0602df
com.fishkaster.app:attr/materialClockStyle = 0x7f04032d
com.fishkaster.app:styleable/CropImageView = 0x7f15002f
com.fishkaster.app:color/mtrl_btn_bg_color_selector = 0x7f0602dd
com.fishkaster.app:color/material_timepicker_modebutton_tint = 0x7f0602dc
com.fishkaster.app:styleable/ActionMenuView = 0x7f150003
com.fishkaster.app:style/Widget.Material3.BottomNavigation.Badge = 0x7f14038e
com.fishkaster.app:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f0404ba
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f080015
com.fishkaster.app:string/mtrl_switch_thumb_path_morphing = 0x7f13011a
com.fishkaster.app:color/material_timepicker_button_stroke = 0x7f0602d9
com.fishkaster.app:drawable/exo_notification_rewind = 0x7f0800da
com.fishkaster.app:color/material_timepicker_button_background = 0x7f0602d8
com.fishkaster.app:macro/m3_comp_fab_primary_large_container_shape = 0x7f0e0039
com.fishkaster.app:color/material_slider_inactive_tick_marks_color = 0x7f0602d5
com.fishkaster.app:string/catalyst_debug_connecting = 0x7f130034
com.fishkaster.app:color/material_slider_active_tick_marks_color = 0x7f0602d2
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f140323
com.fishkaster.app:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f07019c
com.fishkaster.app:color/material_personalized_primary_text_disable_only = 0x7f0602d1
com.fishkaster.app:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1401cf
com.fishkaster.app:color/material_personalized_primary_inverse_text_disable_only = 0x7f0602d0
com.fishkaster.app:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0701d2
com.fishkaster.app:color/material_personalized_hint_foreground_inverse = 0x7f0602cf
com.fishkaster.app:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0601c7
com.fishkaster.app:color/exo_error_message_background_color = 0x7f060078
com.fishkaster.app:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0602cd
com.fishkaster.app:attr/floatingActionButtonLargeSecondaryStyle = 0x7f040215
com.fishkaster.app:dimen/mtrl_calendar_landscape_header_width = 0x7f0702ad
com.fishkaster.app:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0602cb
com.fishkaster.app:drawable/mtrl_switch_thumb_unchecked = 0x7f08014b
com.fishkaster.app:dimen/highlight_alpha_material_dark = 0x7f0700ba
com.fishkaster.app:color/m3_sys_color_light_surface_dim = 0x7f060210
com.fishkaster.app:attr/textAppearanceSubtitle2 = 0x7f0404ae
com.fishkaster.app:color/mtrl_navigation_bar_colored_item_tint = 0x7f0602f8
com.fishkaster.app:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0e011e
com.fishkaster.app:macro/m3_comp_progress_indicator_track_color = 0x7f0e00d5
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600e9
com.fishkaster.app:color/material_personalized_color_text_primary_inverse = 0x7f0602ca
com.fishkaster.app:dimen/m3_appbar_size_medium = 0x7f0700d1
com.fishkaster.app:color/material_personalized_color_tertiary = 0x7f0602c7
com.fishkaster.app:dimen/notification_large_icon_height = 0x7f070336
com.fishkaster.app:drawable/$avd_show_password__0 = 0x7f080003
com.fishkaster.app:color/material_personalized_color_surface_variant = 0x7f0602c6
com.fishkaster.app:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f14009d
com.fishkaster.app:attr/actionTextColorAlpha = 0x7f040023
com.fishkaster.app:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1402d4
com.fishkaster.app:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0e0106
com.fishkaster.app:dimen/m3_btn_dialog_btn_spacing = 0x7f0700f2
com.fishkaster.app:color/material_personalized_color_surface_container_low = 0x7f0602c2
com.fishkaster.app:attr/contentPaddingStart = 0x7f040149
com.fishkaster.app:color/material_personalized_color_surface_container_highest = 0x7f0602c1
com.fishkaster.app:attr/buffered_color = 0x7f040095
com.fishkaster.app:color/material_personalized_color_surface = 0x7f0602bd
com.fishkaster.app:color/material_personalized_color_secondary_text_inverse = 0x7f0602bc
com.fishkaster.app:style/TextAppearance.Material3.TitleSmall = 0x7f140228
com.fishkaster.app:color/material_personalized_color_secondary_text = 0x7f0602bb
com.fishkaster.app:attr/flow_horizontalAlign = 0x7f040227
com.fishkaster.app:color/material_personalized_color_secondary_container = 0x7f0602ba
com.fishkaster.app:color/material_personalized_color_secondary = 0x7f0602b9
com.fishkaster.app:color/material_personalized_color_primary_inverse = 0x7f0602b6
com.fishkaster.app:string/bottomsheet_drag_handle_content_description = 0x7f130025
com.fishkaster.app:dimen/design_bottom_navigation_active_item_min_width = 0x7f070063
com.fishkaster.app:color/material_personalized_color_primary_container = 0x7f0602b5
com.fishkaster.app:layout/select_dialog_multichoice_material = 0x7f0d008c
com.fishkaster.app:color/material_dynamic_tertiary30 = 0x7f060280
com.fishkaster.app:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700ea
com.fishkaster.app:color/material_personalized_color_primary = 0x7f0602b4
com.fishkaster.app:attr/actionModeCopyDrawable = 0x7f040015
com.fishkaster.app:id/groups = 0x7f0a0121
com.fishkaster.app:color/m3_ref_palette_neutral10 = 0x7f060129
com.fishkaster.app:color/material_personalized_color_outline_variant = 0x7f0602b3
com.fishkaster.app:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0e009e
com.fishkaster.app:color/material_personalized_color_outline = 0x7f0602b2
com.fishkaster.app:color/material_personalized_color_on_tertiary = 0x7f0602b0
com.fishkaster.app:color/m3_sys_color_dark_on_secondary = 0x7f06018b
com.fishkaster.app:color/material_personalized_color_on_surface_inverse = 0x7f0602ae
com.fishkaster.app:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f07019e
com.fishkaster.app:color/material_personalized_color_on_secondary_container = 0x7f0602ac
com.fishkaster.app:color/material_personalized_color_on_secondary = 0x7f0602ab
com.fishkaster.app:id/CTRL = 0x7f0a0003
com.fishkaster.app:id/view_tree_lifecycle_owner = 0x7f0a025f
com.fishkaster.app:color/material_personalized_color_on_primary = 0x7f0602a9
com.fishkaster.app:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f0701b4
com.fishkaster.app:dimen/m3_btn_text_btn_padding_right = 0x7f070107
com.fishkaster.app:color/material_personalized_color_on_background = 0x7f0602a6
com.fishkaster.app:id/submenuarrow = 0x7f0a021b
com.fishkaster.app:dimen/m3_badge_with_text_offset = 0x7f0700de
com.fishkaster.app:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f14005b
com.fishkaster.app:layout/notification_template_part_time = 0x7f0d0086
com.fishkaster.app:layout/notification_media_action = 0x7f0d007a
com.fishkaster.app:layout/material_clock_display_divider = 0x7f0d004c
com.fishkaster.app:drawable/googleg_disabled_color_18 = 0x7f0800f7
com.fishkaster.app:color/material_personalized_color_error_container = 0x7f0602a5
com.fishkaster.app:attr/showAnimationBehavior = 0x7f040414
com.fishkaster.app:color/material_personalized_color_error = 0x7f0602a4
com.fishkaster.app:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f070326
com.fishkaster.app:dimen/hint_alpha_material_dark = 0x7f0700bc
com.fishkaster.app:color/material_personalized_color_control_activated = 0x7f0602a1
com.fishkaster.app:color/material_personalized_color_background = 0x7f0602a0
com.fishkaster.app:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0e00f8
com.fishkaster.app:color/material_personalized__highlighted_text_inverse = 0x7f06029f
com.fishkaster.app:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0e0063
com.fishkaster.app:dimen/abc_text_size_display_4_material = 0x7f070046
com.fishkaster.app:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f140192
com.fishkaster.app:string/material_hour_selection = 0x7f1300ce
com.fishkaster.app:dimen/material_clock_size = 0x7f070255
com.fishkaster.app:dimen/exo_styled_progress_margin_bottom = 0x7f0700b4
com.fishkaster.app:color/material_on_surface_emphasis_high_type = 0x7f06029b
com.fishkaster.app:layout/mtrl_picker_header_title_text = 0x7f0d0072
com.fishkaster.app:color/material_on_surface_disabled = 0x7f06029a
com.fishkaster.app:color/material_on_primary_emphasis_medium = 0x7f060299
com.fishkaster.app:dimen/mtrl_navigation_rail_active_text_size = 0x7f0702f1
com.fishkaster.app:color/material_on_primary_disabled = 0x7f060297
com.fishkaster.app:color/material_on_background_disabled = 0x7f060294
com.fishkaster.app:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0701fa
com.fishkaster.app:color/material_harmonized_color_on_error_container = 0x7f060293
com.fishkaster.app:string/mtrl_picker_cancel = 0x7f1300f8
com.fishkaster.app:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0e00b5
com.fishkaster.app:color/material_grey_800 = 0x7f06028d
com.fishkaster.app:dimen/abc_progress_bar_height_material = 0x7f070035
com.fishkaster.app:style/Theme.MaterialComponents.Dialog = 0x7f140295
com.fishkaster.app:color/material_dynamic_tertiary95 = 0x7f060287
com.fishkaster.app:attr/played_ad_marker_color = 0x7f0403b6
com.fishkaster.app:attr/layout_constraintRight_toRightOf = 0x7f0402d2
com.fishkaster.app:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1403dc
com.fishkaster.app:color/m3_ref_palette_dynamic_primary100 = 0x7f0600f6
com.fishkaster.app:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f14002c
com.fishkaster.app:attr/state_lifted = 0x7f04044d
com.fishkaster.app:color/material_dynamic_tertiary20 = 0x7f06027f
com.fishkaster.app:macro/m3_comp_time_picker_headline_type = 0x7f0e0151
com.fishkaster.app:styleable/TextInputEditText = 0x7f15009e
com.fishkaster.app:color/material_dynamic_tertiary100 = 0x7f06027e
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0e0090
com.fishkaster.app:color/material_dynamic_secondary99 = 0x7f06027b
com.fishkaster.app:attr/roundBottomRight = 0x7f0403e6
com.fishkaster.app:dimen/material_emphasis_disabled_background = 0x7f07025a
com.fishkaster.app:color/material_dynamic_secondary80 = 0x7f060278
com.fishkaster.app:color/material_grey_300 = 0x7f06028a
com.fishkaster.app:dimen/autofill_inline_suggestion_icon_size = 0x7f070052
com.fishkaster.app:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0e0130
com.fishkaster.app:attr/liftOnScroll = 0x7f0402ef
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600c9
com.fishkaster.app:layout/alert_title_layout = 0x7f0d001c
com.fishkaster.app:color/material_dynamic_secondary50 = 0x7f060275
com.fishkaster.app:id/accessibility_custom_action_3 = 0x7f0a002d
com.fishkaster.app:color/material_dynamic_secondary100 = 0x7f060271
com.fishkaster.app:color/material_dynamic_secondary0 = 0x7f06026f
com.fishkaster.app:attr/floatingActionButtonSmallSecondaryStyle = 0x7f04021c
com.fishkaster.app:color/m3_popupmenu_overlay_color = 0x7f0600bf
com.fishkaster.app:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f0700c1
com.fishkaster.app:color/material_dynamic_primary20 = 0x7f060265
com.fishkaster.app:color/material_dynamic_primary100 = 0x7f060264
com.fishkaster.app:integer/mtrl_view_visible = 0x7f0b0044
com.fishkaster.app:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0601f0
com.fishkaster.app:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f08012a
com.fishkaster.app:color/material_dynamic_neutral_variant95 = 0x7f060260
com.fishkaster.app:color/material_dynamic_neutral_variant99 = 0x7f060261
com.fishkaster.app:color/material_dynamic_tertiary60 = 0x7f060283
com.fishkaster.app:color/material_dynamic_neutral_variant90 = 0x7f06025f
com.fishkaster.app:styleable/AppBarLayout = 0x7f15000b
com.fishkaster.app:style/ExoStyledControls.Button.Bottom.FullScreen = 0x7f140137
com.fishkaster.app:color/material_dynamic_neutral_variant80 = 0x7f06025e
com.fishkaster.app:color/material_personalized_color_on_surface = 0x7f0602ad
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f140179
com.fishkaster.app:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0e0007
com.fishkaster.app:id/accessibility_custom_action_28 = 0x7f0a002b
com.fishkaster.app:color/material_dynamic_neutral_variant100 = 0x7f060257
com.fishkaster.app:id/right_side = 0x7f0a01d3
com.fishkaster.app:id/mtrl_picker_header_title_and_selection = 0x7f0a0184
com.fishkaster.app:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f07017b
com.fishkaster.app:color/material_dynamic_neutral_variant0 = 0x7f060255
com.fishkaster.app:attr/coplanarSiblingViewId = 0x7f040150
com.fishkaster.app:layout/notification_template_custom_big = 0x7f0d0080
com.fishkaster.app:id/clear_text = 0x7f0a0087
com.fishkaster.app:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0701ee
com.fishkaster.app:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0e0055
com.fishkaster.app:color/material_dynamic_neutral95 = 0x7f060253
com.fishkaster.app:layout/design_layout_snackbar = 0x7f0d0028
com.fishkaster.app:attr/drawerArrowStyle = 0x7f0401c2
com.fishkaster.app:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0701c3
com.fishkaster.app:dimen/m3_chip_hovered_translation_z = 0x7f07011e
com.fishkaster.app:string/imagebutton_description = 0x7f1300b6
com.fishkaster.app:color/material_dynamic_neutral60 = 0x7f06024f
com.fishkaster.app:attr/circleRadius = 0x7f0400dd
com.fishkaster.app:color/material_dynamic_neutral50 = 0x7f06024e
com.fishkaster.app:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.fishkaster.app:macro/m3_comp_outlined_card_container_color = 0x7f0e00a8
com.fishkaster.app:id/blocking = 0x7f0a006e
com.fishkaster.app:color/m3_timepicker_button_ripple_color = 0x7f06022f
com.fishkaster.app:color/material_dynamic_neutral10 = 0x7f060249
com.fishkaster.app:layout/abc_expanded_menu_layout = 0x7f0d000d
com.fishkaster.app:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0702e6
com.fishkaster.app:color/material_grey_900 = 0x7f06028f
com.fishkaster.app:color/material_dynamic_color_light_error_container = 0x7f060245
com.fishkaster.app:style/TextAppearance.AppCompat.Caption = 0x7f1401c4
com.fishkaster.app:anim/rns_fade_out = 0x7f010038
com.fishkaster.app:color/material_dynamic_color_light_error = 0x7f060244
com.fishkaster.app:color/material_dynamic_color_dark_error = 0x7f060240
com.fishkaster.app:attr/startIconContentDescription = 0x7f040440
com.fishkaster.app:style/Widget.AppCompat.Spinner.Underlined = 0x7f14036a
com.fishkaster.app:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080034
com.fishkaster.app:color/material_deep_teal_500 = 0x7f06023e
com.fishkaster.app:drawable/exo_styled_controls_shuffle_on = 0x7f0800ef
com.fishkaster.app:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f0701b1
com.fishkaster.app:macro/m3_comp_dialog_container_shape = 0x7f0e0023
com.fishkaster.app:id/nav_controller_view_tag = 0x7f0a018c
com.fishkaster.app:id/exitUntilCollapsed = 0x7f0a00ca
com.fishkaster.app:drawable/exo_styled_controls_check = 0x7f0800df
com.fishkaster.app:drawable/exo_notification_pause = 0x7f0800d7
com.fishkaster.app:attr/layout_constraintCircleRadius = 0x7f0402bf
com.fishkaster.app:color/m3_sys_color_dark_on_tertiary_container = 0x7f060190
com.fishkaster.app:attr/circleCrop = 0x7f0400dc
com.fishkaster.app:integer/material_motion_duration_long_1 = 0x7f0b0029
com.fishkaster.app:dimen/mtrl_shape_corner_size_large_component = 0x7f070308
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f14047a
com.fishkaster.app:color/material_blue_grey_950 = 0x7f06023b
com.fishkaster.app:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1400eb
com.fishkaster.app:dimen/m3_comp_navigation_bar_container_height = 0x7f07015f
com.fishkaster.app:dimen/material_clock_display_height = 0x7f070248
com.fishkaster.app:dimen/m3_sys_elevation_level4 = 0x7f07021a
com.fishkaster.app:attr/layout_dodgeInsetEdges = 0x7f0402e0
com.fishkaster.app:color/m3_tonal_button_ripple_color_selector = 0x7f060238
com.fishkaster.app:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f070297
com.fishkaster.app:color/mtrl_calendar_item_stroke_color = 0x7f0602e5
com.fishkaster.app:attr/itemIconSize = 0x7f040285
com.fishkaster.app:color/m3_timepicker_time_input_stroke_color = 0x7f060237
com.fishkaster.app:string/mtrl_picker_start_date_description = 0x7f13010c
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f140322
com.fishkaster.app:color/m3_timepicker_secondary_text_button_text_color = 0x7f060236
com.fishkaster.app:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.fishkaster.app:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1403c3
com.fishkaster.app:attr/thumbRadius = 0x7f0404c8
com.fishkaster.app:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f060235
com.fishkaster.app:color/mtrl_tabs_ripple_color = 0x7f06030d
com.fishkaster.app:string/mtrl_switch_thumb_path_pressed = 0x7f13011c
com.fishkaster.app:attr/borderlessButtonStyle = 0x7f040082
com.fishkaster.app:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1402c0
com.fishkaster.app:attr/listPreferredItemHeightSmall = 0x7f040300
com.fishkaster.app:color/m3_textfield_label_color = 0x7f06022c
com.fishkaster.app:string/state_on_description = 0x7f130144
com.fishkaster.app:drawable/m3_selection_control_ripple = 0x7f08011d
com.fishkaster.app:dimen/m3_comp_fab_primary_large_icon_size = 0x7f070142
com.fishkaster.app:color/m3_textfield_filled_background_color = 0x7f060229
com.fishkaster.app:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f140353
com.fishkaster.app:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0702ca
com.fishkaster.app:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f14010c
com.fishkaster.app:color/m3_timepicker_display_ripple_color = 0x7f060233
com.fishkaster.app:anim/rns_standard_accelerate_interpolator = 0x7f01004c
com.fishkaster.app:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.fishkaster.app:color/m3_tabs_text_color_secondary = 0x7f060225
com.fishkaster.app:color/m3_tabs_icon_color = 0x7f060220
com.fishkaster.app:attr/closeIconEndPadding = 0x7f0400e6
com.fishkaster.app:color/m3_sys_color_tertiary_fixed = 0x7f06021e
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0e00c8
com.fishkaster.app:color/m3_sys_color_primary_fixed_dim = 0x7f06021b
com.fishkaster.app:drawable/$m3_avd_show_password__1 = 0x7f08000a
com.fishkaster.app:style/Widget.MaterialComponents.ChipGroup = 0x7f140454
com.fishkaster.app:string/catalyst_settings = 0x7f13004a
com.fishkaster.app:color/m3_sys_color_on_secondary_fixed_variant = 0x7f060217
com.fishkaster.app:color/m3_sys_color_on_secondary_fixed = 0x7f060216
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1403e6
com.fishkaster.app:style/ThemeOverlay.AppCompat.Dialog = 0x7f1402b9
com.fishkaster.app:id/exo_fullscreen = 0x7f0a00dd
com.fishkaster.app:style/Base.Widget.AppCompat.ListMenuView = 0x7f1400e8
com.fishkaster.app:color/m3_sys_color_dark_secondary = 0x7f060195
com.fishkaster.app:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0701c8
com.fishkaster.app:attr/colorOnErrorContainer = 0x7f040105
com.fishkaster.app:attr/pressedStateOverlayImage = 0x7f0403c6
com.fishkaster.app:dimen/abc_text_size_display_2_material = 0x7f070044
com.fishkaster.app:color/m3_sys_color_light_surface_container_lowest = 0x7f06020f
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f14020c
com.fishkaster.app:style/Theme.Material3.DynamicColors.DayNight = 0x7f140275
com.fishkaster.app:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f140189
com.fishkaster.app:attr/endIconCheckable = 0x7f0401d3
com.fishkaster.app:attr/customStringValue = 0x7f040198
com.fishkaster.app:attr/colorOnSurface = 0x7f04010f
com.fishkaster.app:color/m3_sys_color_light_surface_container_low = 0x7f06020e
com.fishkaster.app:color/m3_sys_color_light_surface_container_highest = 0x7f06020d
com.fishkaster.app:id/month_navigation_fragment_toggle = 0x7f0a016f
com.fishkaster.app:attr/dragDirection = 0x7f0401b5
com.fishkaster.app:color/m3_ref_palette_neutral90 = 0x7f060139
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f14003c
com.fishkaster.app:style/Base.Widget.AppCompat.ListView = 0x7f1400ea
com.fishkaster.app:style/TextAppearance.Material3.SearchView.Prefix = 0x7f140225
com.fishkaster.app:id/dark = 0x7f0a00a1
com.fishkaster.app:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f060219
com.fishkaster.app:attr/singleChoiceItemLayout = 0x7f04042e
com.fishkaster.app:dimen/mtrl_slider_thumb_elevation = 0x7f07030f
com.fishkaster.app:attr/subMenuArrow = 0x7f040454
com.fishkaster.app:color/m3_sys_color_light_outline = 0x7f060203
com.fishkaster.app:style/Widget.Material3.MaterialDivider = 0x7f1403f5
com.fishkaster.app:style/Base.Widget.Material3.Chip = 0x7f140104
com.fishkaster.app:dimen/mtrl_extended_fab_min_height = 0x7f0702d4
com.fishkaster.app:color/m3_sys_color_light_on_tertiary_container = 0x7f060202
com.fishkaster.app:styleable/MaterialCardView = 0x7f150059
com.fishkaster.app:color/material_dynamic_neutral_variant50 = 0x7f06025b
com.fishkaster.app:integer/material_motion_duration_short_2 = 0x7f0b002e
com.fishkaster.app:color/m3_sys_color_light_on_tertiary = 0x7f060201
com.fishkaster.app:color/m3_sys_color_light_on_surface = 0x7f0601ff
com.fishkaster.app:id/design_bottom_sheet = 0x7f0a00a8
com.fishkaster.app:color/m3_sys_color_light_on_secondary_container = 0x7f0601fe
com.fishkaster.app:dimen/exo_settings_text_height = 0x7f0700a5
com.fishkaster.app:color/m3_sys_color_light_on_secondary = 0x7f0601fd
com.fishkaster.app:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f0702b4
com.fishkaster.app:dimen/m3_searchbar_outlined_stroke_width = 0x7f070204
com.fishkaster.app:color/m3_sys_color_dark_secondary_container = 0x7f060196
com.fishkaster.app:integer/m3_badge_max_number = 0x7f0b000c
com.fishkaster.app:color/m3_sys_color_light_on_primary_container = 0x7f0601fc
com.fishkaster.app:color/material_dynamic_primary90 = 0x7f06026c
com.fishkaster.app:style/Base.V21.Theme.MaterialComponents = 0x7f1400a8
com.fishkaster.app:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f080134
com.fishkaster.app:id/layout = 0x7f0a0144
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f14030e
com.fishkaster.app:color/m3_sys_color_light_on_error_container = 0x7f0601fa
com.fishkaster.app:attr/useViewLifecycle = 0x7f04051e
com.fishkaster.app:color/m3_sys_color_light_on_background = 0x7f0601f8
com.fishkaster.app:id/progress_circular = 0x7f0a01c4
com.fishkaster.app:color/m3_sys_color_light_inverse_surface = 0x7f0601f7
com.fishkaster.app:attr/materialTimePickerTitleStyle = 0x7f04033e
com.fishkaster.app:color/m3_sys_color_light_inverse_primary = 0x7f0601f6
com.fishkaster.app:color/m3_sys_color_light_inverse_on_surface = 0x7f0601f5
com.fishkaster.app:style/TextAppearance.MaterialComponents.Badge = 0x7f140229
com.fishkaster.app:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0601f1
com.fishkaster.app:attr/liftOnScrollTargetViewId = 0x7f0402f1
com.fishkaster.app:styleable/AnimatedStateListDrawableCompat = 0x7f150008
com.fishkaster.app:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f070174
com.fishkaster.app:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0601eb
com.fishkaster.app:attr/pressedTranslationZ = 0x7f0403c7
com.fishkaster.app:dimen/m3_comp_time_picker_container_elevation = 0x7f0701c6
com.fishkaster.app:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0601ea
com.fishkaster.app:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0601e8
com.fishkaster.app:dimen/mtrl_btn_disabled_elevation = 0x7f070281
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f070235
com.fishkaster.app:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0601e7
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0601e2
com.fishkaster.app:attr/showAsAction = 0x7f040415
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0601e0
com.fishkaster.app:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0e016d
com.fishkaster.app:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0601da
com.fishkaster.app:dimen/tooltip_y_offset_non_touch = 0x7f070347
com.fishkaster.app:id/fixed = 0x7f0a010f
com.fishkaster.app:color/m3_sys_color_dynamic_light_primary_container = 0x7f0601d8
com.fishkaster.app:color/material_dynamic_neutral80 = 0x7f060251
com.fishkaster.app:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700ce
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f140303
com.fishkaster.app:color/m3_sys_color_dynamic_light_outline = 0x7f0601d5
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f0601aa
com.fishkaster.app:attr/roundingBorderColor = 0x7f0403ef
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1403ea
com.fishkaster.app:attr/windowMinWidthMajor = 0x7f040535
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_surface = 0x7f0601d1
com.fishkaster.app:attr/subheaderInsetEnd = 0x7f040456
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0601d0
com.fishkaster.app:anim/linear_indeterminate_line1_head_interpolator = 0x7f010023
com.fishkaster.app:id/edit_text_id = 0x7f0a00c0
com.fishkaster.app:attr/drawableSize = 0x7f0401bd
com.fishkaster.app:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0702cd
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0601ce
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f140075
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_background = 0x7f0601ca
com.fishkaster.app:attr/autofillInlineSuggestionChip = 0x7f04004c
com.fishkaster.app:attr/badgeWithTextShapeAppearanceOverlay = 0x7f04006d
com.fishkaster.app:color/m3_sys_color_light_background = 0x7f0601f2
com.fishkaster.app:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.fishkaster.app:styleable/Layout = 0x7f15004b
com.fishkaster.app:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.fishkaster.app:color/m3_sys_color_dynamic_light_error = 0x7f0601c5
com.fishkaster.app:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1400a9
com.fishkaster.app:color/m3_sys_color_dynamic_light_background = 0x7f0601c4
com.fishkaster.app:dimen/mtrl_extended_fab_bottom_padding = 0x7f0702cc
com.fishkaster.app:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0601c3
com.fishkaster.app:dimen/m3_badge_with_text_vertical_offset = 0x7f0700e0
com.fishkaster.app:attr/splitTrack = 0x7f04043a
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f0601c0
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f0601bd
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1403e2
com.fishkaster.app:id/checkbox = 0x7f0a0083
com.fishkaster.app:attr/helperTextTextAppearance = 0x7f040251
com.fishkaster.app:color/mtrl_text_btn_text_color_selector = 0x7f06030e
com.fishkaster.app:string/exo_track_selection_title_text = 0x7f13009d
com.fishkaster.app:attr/tabIndicatorFullWidth = 0x7f040473
com.fishkaster.app:attr/tabPaddingTop = 0x7f04047e
com.fishkaster.app:style/ExoMediaButton = 0x7f14012a
com.fishkaster.app:attr/itemBackground = 0x7f040280
com.fishkaster.app:dimen/m3_comp_badge_size = 0x7f070126
com.fishkaster.app:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0701f6
com.fishkaster.app:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f0601b4
com.fishkaster.app:string/mtrl_picker_range_header_only_end_selected = 0x7f130106
com.fishkaster.app:id/exo_center_controls = 0x7f0a00d1
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f0601b0
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f140076
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f0601ac
com.fishkaster.app:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f0601a7
com.fishkaster.app:attr/showTitle = 0x7f04041c
com.fishkaster.app:attr/materialCalendarHeaderSelection = 0x7f040320
com.fishkaster.app:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f14038c
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f07023f
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f14046f
com.fishkaster.app:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f0601a6
com.fishkaster.app:id/transition_clip = 0x7f0a024a
com.fishkaster.app:attr/roundBottomStart = 0x7f0403e7
com.fishkaster.app:id/cancel_action = 0x7f0a0079
com.fishkaster.app:id/auto = 0x7f0a0062
com.fishkaster.app:id/tag_unhandled_key_event_manager = 0x7f0a0229
com.fishkaster.app:color/m3_sys_color_dynamic_dark_background = 0x7f0601a2
com.fishkaster.app:attr/behavior_hideable = 0x7f04007b
com.fishkaster.app:color/m3_sys_color_dark_tertiary = 0x7f0601a0
com.fishkaster.app:macro/m3_comp_filled_text_field_input_text_type = 0x7f0e0050
com.fishkaster.app:interpolator/fast_out_slow_in = 0x7f0c0006
com.fishkaster.app:attr/scrimBackground = 0x7f0403f7
com.fishkaster.app:color/m3_sys_color_dark_surface_container_highest = 0x7f06019b
com.fishkaster.app:dimen/exo_error_message_height = 0x7f070093
com.fishkaster.app:color/m3_sys_color_dark_surface_bright = 0x7f060198
com.fishkaster.app:color/m3_sys_color_dark_primary_container = 0x7f060194
com.fishkaster.app:macro/m3_comp_outlined_card_outline_color = 0x7f0e00ae
com.fishkaster.app:color/m3_sys_color_dark_primary = 0x7f060193
com.fishkaster.app:color/m3_sys_color_dark_on_surface_variant = 0x7f06018e
com.fishkaster.app:color/m3_sys_color_dark_on_surface = 0x7f06018d
com.fishkaster.app:attr/defaultState = 0x7f0401a3
com.fishkaster.app:color/m3_sys_color_dark_on_secondary_container = 0x7f06018c
com.fishkaster.app:color/m3_text_button_ripple_color_selector = 0x7f060228
com.fishkaster.app:styleable/GradientColor = 0x7f15003f
com.fishkaster.app:color/m3_ref_palette_dynamic_primary95 = 0x7f0600ff
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f08001a
com.fishkaster.app:color/m3_sys_color_dark_on_error = 0x7f060187
com.fishkaster.app:drawable/common_google_signin_btn_icon_light_focused = 0x7f080090
com.fishkaster.app:color/m3_sys_color_dark_on_background = 0x7f060186
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600d8
com.fishkaster.app:color/m3_sys_color_dark_inverse_surface = 0x7f060185
com.fishkaster.app:macro/m3_comp_search_bar_container_color = 0x7f0e00e6
com.fishkaster.app:color/m3_slider_thumb_color_legacy = 0x7f06017d
com.fishkaster.app:color/m3_slider_inactive_track_color_legacy = 0x7f06017b
com.fishkaster.app:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f070127
com.fishkaster.app:dimen/abc_text_size_title_material = 0x7f07004f
com.fishkaster.app:color/m3_slider_inactive_track_color = 0x7f06017a
com.fishkaster.app:id/fade = 0x7f0a00fe
com.fishkaster.app:color/m3_ref_palette_white = 0x7f060174
com.fishkaster.app:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f140484
com.fishkaster.app:color/dim_foreground_material_dark = 0x7f060070
com.fishkaster.app:drawable/ic_clock_black_24dp = 0x7f080103
com.fishkaster.app:dimen/design_fab_size_normal = 0x7f070075
com.fishkaster.app:color/m3_ref_palette_tertiary95 = 0x7f060172
com.fishkaster.app:color/m3_ref_palette_tertiary90 = 0x7f060171
com.fishkaster.app:dimen/abc_dialog_min_width_minor = 0x7f070023
com.fishkaster.app:color/m3_ref_palette_tertiary70 = 0x7f06016f
com.fishkaster.app:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f140422
com.fishkaster.app:color/m3_ref_palette_tertiary60 = 0x7f06016e
com.fishkaster.app:color/m3_ref_palette_tertiary50 = 0x7f06016d
com.fishkaster.app:attr/framePosition = 0x7f040246
com.fishkaster.app:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f070177
com.fishkaster.app:dimen/m3_card_disabled_z = 0x7f07010a
com.fishkaster.app:attr/floatingActionButtonLargePrimaryStyle = 0x7f040214
com.fishkaster.app:color/m3_ref_palette_neutral30 = 0x7f060130
com.fishkaster.app:color/m3_ref_palette_tertiary30 = 0x7f06016b
com.fishkaster.app:color/m3_ref_palette_tertiary100 = 0x7f060169
com.fishkaster.app:color/m3_ref_palette_tertiary20 = 0x7f06016a
com.fishkaster.app:style/Base.AlertDialog.AppCompat = 0x7f14000e
com.fishkaster.app:drawable/abc_btn_borderless_material = 0x7f08002a
com.fishkaster.app:color/m3_ref_palette_tertiary10 = 0x7f060168
com.fishkaster.app:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f14017e
com.fishkaster.app:dimen/notification_top_pad = 0x7f07033f
com.fishkaster.app:attr/indicatorDirectionLinear = 0x7f040274
com.fishkaster.app:dimen/mtrl_min_touch_target_size = 0x7f0702e8
com.fishkaster.app:id/jumpToEnd = 0x7f0a0140
com.fishkaster.app:attr/useMaterialThemeColors = 0x7f04051d
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f070134
com.fishkaster.app:color/browser_actions_divider_color = 0x7f060028
com.fishkaster.app:color/m3_ref_palette_secondary99 = 0x7f060166
com.fishkaster.app:color/m3_ref_palette_secondary80 = 0x7f060163
com.fishkaster.app:anim/mtrl_card_lowers_interpolator = 0x7f010031
com.fishkaster.app:attr/linearProgressIndicatorStyle = 0x7f0402f5
com.fishkaster.app:color/m3_ref_palette_secondary60 = 0x7f060161
com.fishkaster.app:macro/m3_comp_dialog_headline_color = 0x7f0e0024
com.fishkaster.app:color/material_dynamic_primary95 = 0x7f06026d
com.fishkaster.app:style/ExoStyledControls.Button.Center = 0x7f14013f
com.fishkaster.app:dimen/mtrl_calendar_year_width = 0x7f0702c0
com.fishkaster.app:color/m3_ref_palette_primary50 = 0x7f060153
com.fishkaster.app:attr/chipIconVisible = 0x7f0400d0
com.fishkaster.app:attr/tabIndicator = 0x7f04046f
com.fishkaster.app:color/m3_ref_palette_primary20 = 0x7f060150
com.fishkaster.app:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f1403b4
com.fishkaster.app:color/m3_ref_palette_primary100 = 0x7f06014f
com.fishkaster.app:styleable/ChipGroup = 0x7f150021
com.fishkaster.app:color/m3_ref_palette_primary0 = 0x7f06014d
com.fishkaster.app:attr/backgroundOverlayColorAlpha = 0x7f040059
com.fishkaster.app:color/material_dynamic_primary50 = 0x7f060268
com.fishkaster.app:color/m3_ref_palette_neutral_variant99 = 0x7f06014c
com.fishkaster.app:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f14029f
com.fishkaster.app:color/m3_ref_palette_neutral_variant70 = 0x7f060148
com.fishkaster.app:color/m3_ref_palette_neutral_variant50 = 0x7f060146
com.fishkaster.app:style/Theme.AppCompat.DayNight.Dialog = 0x7f140243
com.fishkaster.app:color/m3_navigation_item_text_color = 0x7f0600bb
com.fishkaster.app:attr/closeIconVisible = 0x7f0400ea
com.fishkaster.app:color/m3_ref_palette_neutral_variant40 = 0x7f060145
com.fishkaster.app:color/m3_ref_palette_neutral_variant30 = 0x7f060144
com.fishkaster.app:color/m3_timepicker_button_text_color = 0x7f060230
com.fishkaster.app:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.fishkaster.app:color/m3_ref_palette_neutral94 = 0x7f06013b
com.fishkaster.app:attr/floatingActionButtonSmallTertiaryStyle = 0x7f04021f
com.fishkaster.app:style/Theme.Material3.DayNight.NoActionBar = 0x7f140271
com.fishkaster.app:style/TextAppearance.Design.Error = 0x7f1401fd
com.fishkaster.app:attr/layout_constraintBaseline_creator = 0x7f0402b8
com.fishkaster.app:dimen/m3_slider_thumb_elevation = 0x7f070211
com.fishkaster.app:attr/motionEasingLinear = 0x7f040370
com.fishkaster.app:color/m3_sys_color_dark_surface_dim = 0x7f06019e
com.fishkaster.app:attr/actionMenuTextColor = 0x7f040010
com.fishkaster.app:color/m3_ref_palette_neutral70 = 0x7f060136
com.fishkaster.app:color/m3_ref_palette_secondary70 = 0x7f060162
com.fishkaster.app:style/Theme.ReactNative.AppCompat.Light = 0x7f1402b0
com.fishkaster.app:color/m3_ref_palette_neutral60 = 0x7f060135
com.fishkaster.app:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.fishkaster.app:color/m3_ref_palette_neutral12 = 0x7f06012b
com.fishkaster.app:color/m3_ref_palette_neutral100 = 0x7f06012a
com.fishkaster.app:color/material_dynamic_tertiary70 = 0x7f060284
com.fishkaster.app:color/m3_ref_palette_error50 = 0x7f060121
com.fishkaster.app:dimen/mtrl_btn_hovered_z = 0x7f070285
com.fishkaster.app:id/transitionToStart = 0x7f0a0249
com.fishkaster.app:dimen/design_snackbar_elevation = 0x7f070084
com.fishkaster.app:color/m3_ref_palette_error20 = 0x7f06011e
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary90 = 0x7f060118
com.fishkaster.app:color/material_dynamic_neutral_variant20 = 0x7f060258
com.fishkaster.app:style/Base.Widget.AppCompat.ButtonBar = 0x7f1400d6
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary60 = 0x7f060115
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f140312
com.fishkaster.app:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700c9
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f07023c
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary50 = 0x7f060114
com.fishkaster.app:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f14018c
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary40 = 0x7f060113
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary30 = 0x7f060112
com.fishkaster.app:id/graph_wrap = 0x7f0a011f
com.fishkaster.app:attr/carousel_alignment = 0x7f0400b6
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary10 = 0x7f06010f
com.fishkaster.app:style/Platform.MaterialComponents.Light.Dialog = 0x7f140165
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary99 = 0x7f06010d
com.fishkaster.app:attr/listPreferredItemHeight = 0x7f0402fe
com.fishkaster.app:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f070277
com.fishkaster.app:color/m3_calendar_item_disabled_text = 0x7f06008f
com.fishkaster.app:style/Widget.Autofill.InlineSuggestionSubtitle = 0x7f140373
com.fishkaster.app:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0701d1
com.fishkaster.app:attr/titleTextEllipsize = 0x7f0404ea
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary95 = 0x7f06010c
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary80 = 0x7f06010a
com.fishkaster.app:color/material_dynamic_tertiary80 = 0x7f060285
com.fishkaster.app:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0e0066
com.fishkaster.app:attr/track = 0x7f0404fa
com.fishkaster.app:animator/fragment_close_exit = 0x7f020004
com.fishkaster.app:attr/region_heightMoreThan = 0x7f0403d7
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary70 = 0x7f060109
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary30 = 0x7f060105
com.fishkaster.app:attr/itemMinHeight = 0x7f040288
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary20 = 0x7f060104
com.fishkaster.app:color/m3_ref_palette_primary80 = 0x7f060156
com.fishkaster.app:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.fishkaster.app:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f140412
com.fishkaster.app:drawable/abc_list_selector_disabled_holo_light = 0x7f080055
com.fishkaster.app:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f140070
com.fishkaster.app:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070242
com.fishkaster.app:string/character_counter_overflowed_content_description = 0x7f13004d
com.fishkaster.app:drawable/notification_bg_low_pressed = 0x7f080156
com.fishkaster.app:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f0701a0
com.fishkaster.app:color/m3_ref_palette_dynamic_primary60 = 0x7f0600fb
com.fishkaster.app:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f140195
com.fishkaster.app:drawable/abc_btn_check_material = 0x7f08002b
com.fishkaster.app:attr/layout = 0x7f0402ad
com.fishkaster.app:color/material_dynamic_neutral30 = 0x7f06024c
com.fishkaster.app:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f070149
com.fishkaster.app:id/design_navigation_view = 0x7f0a00ac
com.fishkaster.app:attr/progressBarStyle = 0x7f0403cc
com.fishkaster.app:color/m3_ref_palette_dynamic_primary40 = 0x7f0600f9
com.fishkaster.app:color/m3_button_ripple_color_selector = 0x7f06008e
com.fishkaster.app:integer/m3_sys_motion_duration_medium3 = 0x7f0b001c
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary100 = 0x7f060103
com.fishkaster.app:id/rn_redbox_loading_indicator = 0x7f0a01d8
com.fishkaster.app:attr/textAppearanceHeadline3 = 0x7f040499
com.fishkaster.app:color/m3_ref_palette_dynamic_primary30 = 0x7f0600f8
com.fishkaster.app:color/m3_ref_palette_dynamic_primary20 = 0x7f0600f7
com.fishkaster.app:color/m3_ref_palette_dynamic_primary10 = 0x7f0600f5
com.fishkaster.app:attr/cropMinCropResultWidthPX = 0x7f04017b
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0600f2
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0600f1
com.fishkaster.app:drawable/refresh = 0x7f080164
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0600ef
com.fishkaster.app:attr/colorOnBackground = 0x7f040101
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1401e3
com.fishkaster.app:styleable/ViewBackgroundHelper = 0x7f1500a7
com.fishkaster.app:attr/animateMenuItems = 0x7f040036
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0600e8
com.fishkaster.app:style/Animation.Material3.SideSheetDialog = 0x7f140009
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600e6
com.fishkaster.app:id/month_title = 0x7f0a0172
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0600e5
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0600e3
com.fishkaster.app:attr/use_artwork = 0x7f04051f
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f140321
com.fishkaster.app:attr/show_previous_button = 0x7f040420
com.fishkaster.app:attr/floatingActionButtonSmallStyle = 0x7f04021d
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0600e0
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral98 = 0x7f0600da
com.fishkaster.app:color/mtrl_choice_chip_text_color = 0x7f0602ef
com.fishkaster.app:dimen/material_emphasis_medium = 0x7f07025c
com.fishkaster.app:string/alert_description = 0x7f13001b
com.fishkaster.app:drawable/exo_styled_controls_subtitle_off = 0x7f0800f1
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral94 = 0x7f0600d7
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral92 = 0x7f0600d6
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600d5
com.fishkaster.app:dimen/exo_icon_horizontal_margin = 0x7f070098
com.fishkaster.app:dimen/mtrl_btn_icon_btn_padding_left = 0x7f070286
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600d2
com.fishkaster.app:drawable/abc_seekbar_track_material = 0x7f080064
com.fishkaster.app:id/TOP_END = 0x7f0a000f
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600cf
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600ce
com.fishkaster.app:attr/cardMaxElevation = 0x7f0400b2
com.fishkaster.app:attr/colorOnSecondaryFixedVariant = 0x7f04010e
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral24 = 0x7f0600cb
com.fishkaster.app:attr/itemVerticalPadding = 0x7f04029c
com.fishkaster.app:id/cancel_button = 0x7f0a007a
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral22 = 0x7f0600ca
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral17 = 0x7f0600c8
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600c6
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600c5
com.fishkaster.app:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f140182
com.fishkaster.app:string/bottomsheet_action_expand_halfway = 0x7f130023
com.fishkaster.app:color/m3_radiobutton_ripple_tint = 0x7f0600c2
com.fishkaster.app:style/ExoMediaButton.Next = 0x7f14012c
com.fishkaster.app:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f0701ac
com.fishkaster.app:color/m3_navigation_rail_ripple_color_selector = 0x7f0600be
com.fishkaster.app:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0600bc
com.fishkaster.app:color/m3_navigation_item_ripple_color = 0x7f0600ba
com.fishkaster.app:color/mtrl_chip_close_icon_tint = 0x7f0602ea
com.fishkaster.app:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005
com.fishkaster.app:color/m3_navigation_item_icon_tint = 0x7f0600b9
com.fishkaster.app:attr/headerLayout = 0x7f04024d
com.fishkaster.app:color/m3_navigation_bar_ripple_color_selector = 0x7f0600b7
com.fishkaster.app:style/Base.Widget.Design.TabLayout = 0x7f1400ff
com.fishkaster.app:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600b6
com.fishkaster.app:attr/textInputFilledStyle = 0x7f0404b7
com.fishkaster.app:color/m3_icon_button_icon_color_selector = 0x7f0600b4
com.fishkaster.app:color/m3_fab_ripple_color_selector = 0x7f0600b0
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f080012
com.fishkaster.app:attr/overlayImage = 0x7f040396
com.fishkaster.app:style/Widget.Material3.SearchView.Toolbar = 0x7f14040e
com.fishkaster.app:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0e00c1
com.fishkaster.app:dimen/material_clock_face_margin_top = 0x7f07024c
com.fishkaster.app:style/Widget.MaterialComponents.Button.Icon = 0x7f140443
com.fishkaster.app:attr/clickAction = 0x7f0400df
com.fishkaster.app:attr/repeat_toggle_modes = 0x7f0403db
com.fishkaster.app:color/m3_elevated_chip_background_color = 0x7f0600ad
com.fishkaster.app:id/glide_custom_view_target_tag = 0x7f0a011c
com.fishkaster.app:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0e0041
com.fishkaster.app:attr/onShow = 0x7f040392
com.fishkaster.app:dimen/mtrl_card_spacing = 0x7f0702c6
com.fishkaster.app:attr/tabIconTintMode = 0x7f04046e
com.fishkaster.app:id/reverseSawtooth = 0x7f0a01ce
com.fishkaster.app:color/m3_dynamic_hint_foreground = 0x7f0600aa
com.fishkaster.app:color/m3_dynamic_dark_primary_text_disable_only = 0x7f0600a6
com.fishkaster.app:style/ShapeAppearance.Material3.Corner.Large = 0x7f14019c
com.fishkaster.app:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.fishkaster.app:color/m3_dynamic_dark_hint_foreground = 0x7f0600a5
com.fishkaster.app:style/Base.Theme.AppCompat.Light = 0x7f140055
com.fishkaster.app:attr/cameraZoom = 0x7f0400ad
com.fishkaster.app:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0700d7
com.fishkaster.app:color/m3_dynamic_dark_highlighted_text = 0x7f0600a4
com.fishkaster.app:dimen/exo_settings_height = 0x7f0700a0
com.fishkaster.app:id/exo_overlay = 0x7f0a00e5
com.fishkaster.app:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.fishkaster.app:dimen/mtrl_calendar_navigation_height = 0x7f0702b2
com.fishkaster.app:dimen/notification_right_icon_size = 0x7f07033a
com.fishkaster.app:attr/cornerSizeBottomLeft = 0x7f040159
com.fishkaster.app:styleable/ButtonBarLayout = 0x7f15001b
com.fishkaster.app:attr/cropBorderLineColor = 0x7f04016b
com.fishkaster.app:attr/maxCharacterCount = 0x7f040342
com.fishkaster.app:color/m3_default_color_primary_text = 0x7f0600a0
com.fishkaster.app:attr/buttonStyle = 0x7f0400a3
com.fishkaster.app:color/m3_dark_primary_text_disable_only = 0x7f06009f
com.fishkaster.app:color/m3_ref_palette_neutral0 = 0x7f060128
com.fishkaster.app:color/m3_dark_hint_foreground = 0x7f06009e
com.fishkaster.app:drawable/m3_bottom_sheet_drag_handle = 0x7f080119
com.fishkaster.app:dimen/m3_comp_outlined_card_container_elevation = 0x7f070176
com.fishkaster.app:color/m3_dark_highlighted_text = 0x7f06009d
com.fishkaster.app:color/m3_sys_color_light_primary_container = 0x7f060206
com.fishkaster.app:color/m3_dark_default_color_secondary_text = 0x7f06009c
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f14009c
com.fishkaster.app:attr/layout_constraintRight_toLeftOf = 0x7f0402d1
com.fishkaster.app:color/m3_chip_text_color = 0x7f06009a
com.fishkaster.app:id/open_search_view_dummy_toolbar = 0x7f0a01a4
com.fishkaster.app:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0e0133
com.fishkaster.app:color/m3_chip_stroke_color = 0x7f060099
com.fishkaster.app:color/m3_chip_ripple_color = 0x7f060098
com.fishkaster.app:color/m3_chip_background_color = 0x7f060097
com.fishkaster.app:attr/simpleItems = 0x7f04042d
com.fishkaster.app:styleable/SnackbarLayout = 0x7f150090
com.fishkaster.app:color/m3_button_background_color_selector = 0x7f06008a
com.fishkaster.app:attr/thumbTextPadding = 0x7f0404cb
com.fishkaster.app:attr/buttonCompat = 0x7f04009b
com.fishkaster.app:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f1403a1
com.fishkaster.app:color/m3_bottom_sheet_drag_handle_color = 0x7f060089
com.fishkaster.app:dimen/mtrl_btn_disabled_z = 0x7f070282
com.fishkaster.app:color/androidx_core_ripple_material_light = 0x7f06001b
com.fishkaster.app:color/m3_assist_chip_icon_tint_color = 0x7f060087
com.fishkaster.app:color/iconBackground = 0x7f060085
com.fishkaster.app:string/mtrl_checkbox_button_icon_path_group_name = 0x7f1300e6
com.fishkaster.app:color/highlighted_text_material_light = 0x7f060084
com.fishkaster.app:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700ca
com.fishkaster.app:attr/emojiCompatEnabled = 0x7f0401d1
com.fishkaster.app:attr/checkedIconVisible = 0x7f0400c5
com.fishkaster.app:color/foreground_material_light = 0x7f060082
com.fishkaster.app:string/exo_track_role_alternate = 0x7f130096
com.fishkaster.app:id/confirm_button = 0x7f0a0091
com.fishkaster.app:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f08001e
com.fishkaster.app:macro/m3_comp_menu_container_color = 0x7f0e005d
com.fishkaster.app:attr/haloColor = 0x7f04024b
com.fishkaster.app:attr/hideOnContentScroll = 0x7f040256
com.fishkaster.app:color/expoCropToolbarIconColor = 0x7f060080
com.fishkaster.app:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0e00aa
com.fishkaster.app:color/exo_edit_mode_background_color = 0x7f060077
com.fishkaster.app:attr/strokeWidth = 0x7f040453
com.fishkaster.app:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1400f8
com.fishkaster.app:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0701cf
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1403d8
com.fishkaster.app:color/m3_sys_color_dark_surface_container = 0x7f060199
com.fishkaster.app:id/easeIn = 0x7f0a00bb
com.fishkaster.app:attr/extraMultilineHeightEnabled = 0x7f040201
com.fishkaster.app:dimen/exo_error_message_text_padding_vertical = 0x7f070096
com.fishkaster.app:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f140191
com.fishkaster.app:color/exo_bottom_bar_background = 0x7f060076
com.fishkaster.app:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f1402a4
com.fishkaster.app:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f140222
com.fishkaster.app:attr/suggestionRowLayout = 0x7f040462
com.fishkaster.app:color/m3_ref_palette_error95 = 0x7f060126
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1401e2
com.fishkaster.app:string/expo_splash_screen_resize_mode = 0x7f1300a4
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0e0168
com.fishkaster.app:color/dim_foreground_material_light = 0x7f060071
com.fishkaster.app:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0702d7
com.fishkaster.app:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f14033d
com.fishkaster.app:dimen/exo_styled_progress_dragged_thumb_size = 0x7f0700b1
com.fishkaster.app:attr/placeholderTextAppearance = 0x7f0403b3
com.fishkaster.app:attr/behavior_draggable = 0x7f040077
com.fishkaster.app:attr/latLngBoundsSouthWestLatitude = 0x7f0402aa
com.fishkaster.app:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.fishkaster.app:color/dim_foreground_disabled_material_dark = 0x7f06006e
com.fishkaster.app:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f14041e
com.fishkaster.app:color/dev_launcher_backgroundColor = 0x7f060068
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0601d3
com.fishkaster.app:dimen/mtrl_calendar_header_height = 0x7f0702a7
com.fishkaster.app:dimen/m3_comp_assist_chip_container_height = 0x7f070120
com.fishkaster.app:string/catalyst_debug_open_disabled = 0x7f130037
com.fishkaster.app:attr/floatingActionButtonLargeSurfaceStyle = 0x7f040217
com.fishkaster.app:attr/layout_constraintEnd_toStartOf = 0x7f0402c2
com.fishkaster.app:color/m3_ref_palette_primary70 = 0x7f060155
com.fishkaster.app:color/design_snackbar_background_color = 0x7f060067
com.fishkaster.app:dimen/m3_navigation_item_shape_inset_start = 0x7f0701ea
com.fishkaster.app:id/selection_type = 0x7f0a01f8
com.fishkaster.app:color/design_fab_stroke_end_outer_color = 0x7f060063
com.fishkaster.app:attr/badgeStyle = 0x7f040063
com.fishkaster.app:style/TextAppearance.Material3.LabelSmall = 0x7f140221
com.fishkaster.app:anim/m3_bottom_sheet_slide_out = 0x7f010028
com.fishkaster.app:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0702e2
com.fishkaster.app:attr/textAppearanceSearchResultSubtitle = 0x7f0404aa
com.fishkaster.app:dimen/m3_searchbar_text_size = 0x7f070207
com.fishkaster.app:color/design_fab_shadow_mid_color = 0x7f060060
com.fishkaster.app:color/design_fab_shadow_end_color = 0x7f06005f
com.fishkaster.app:attr/textAppearanceBody2 = 0x7f04048e
com.fishkaster.app:color/material_personalized_color_on_error_container = 0x7f0602a8
com.fishkaster.app:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f14044a
com.fishkaster.app:color/design_default_color_secondary_variant = 0x7f06005c
com.fishkaster.app:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0e005e
com.fishkaster.app:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f0701aa
com.fishkaster.app:color/design_default_color_primary = 0x7f060058
com.fishkaster.app:string/default_media_controller_time = 0x7f130068
com.fishkaster.app:color/design_default_color_on_secondary = 0x7f060056
com.fishkaster.app:color/m3_sys_color_dynamic_dark_outline = 0x7f0601b3
com.fishkaster.app:id/accessibility_action_clickable_span = 0x7f0a0012
com.fishkaster.app:attr/uiCompass = 0x7f040511
com.fishkaster.app:color/design_default_color_on_error = 0x7f060054
com.fishkaster.app:color/design_default_color_background = 0x7f060051
com.fishkaster.app:styleable/CoordinatorLayout = 0x7f15002d
com.fishkaster.app:attr/roundBottomLeft = 0x7f0403e5
com.fishkaster.app:color/design_dark_default_color_secondary_variant = 0x7f06004f
com.fishkaster.app:attr/motionDurationExtraLong3 = 0x7f04035c
com.fishkaster.app:attr/layout_keyline = 0x7f0402ea
com.fishkaster.app:string/exo_download_completed = 0x7f130089
com.fishkaster.app:attr/chipSpacingVertical = 0x7f0400d5
com.fishkaster.app:id/accessibility_custom_action_17 = 0x7f0a001f
com.fishkaster.app:attr/buttonBarButtonStyle = 0x7f040096
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f140308
com.fishkaster.app:color/design_dark_default_color_primary_variant = 0x7f06004d
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f070132
com.fishkaster.app:dimen/mtrl_btn_elevation = 0x7f070283
com.fishkaster.app:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.fishkaster.app:color/highlighted_text_material_dark = 0x7f060083
com.fishkaster.app:color/design_dark_default_color_on_primary = 0x7f060048
com.fishkaster.app:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1400f1
com.fishkaster.app:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f07027b
com.fishkaster.app:attr/textAppearanceDisplaySmall = 0x7f040496
com.fishkaster.app:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f140390
com.fishkaster.app:color/common_google_signin_btn_tint = 0x7f060041
com.fishkaster.app:color/m3_sys_color_dynamic_dark_primary_container = 0x7f0601b6
com.fishkaster.app:color/common_google_signin_btn_text_light_disabled = 0x7f06003e
com.fishkaster.app:attr/colorSecondary = 0x7f040121
com.fishkaster.app:anim/catalyst_fade_in = 0x7f010018
com.fishkaster.app:color/common_google_signin_btn_text_dark_disabled = 0x7f060039
com.fishkaster.app:color/m3_ref_palette_secondary90 = 0x7f060164
com.fishkaster.app:integer/m3_sys_shape_corner_large_corner_family = 0x7f0b0026
com.fishkaster.app:dimen/m3_chip_elevated_elevation = 0x7f07011d
com.fishkaster.app:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f07018d
com.fishkaster.app:attr/paddingRightSystemWindowInsets = 0x7f04039b
com.fishkaster.app:color/common_google_signin_btn_text_dark_default = 0x7f060038
com.fishkaster.app:macro/m3_comp_outlined_button_outline_color = 0x7f0e00a6
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0e0075
com.fishkaster.app:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0601c8
com.fishkaster.app:attr/tint = 0x7f0404da
com.fishkaster.app:color/common_google_signin_btn_text_dark = 0x7f060037
com.fishkaster.app:color/colorPrimaryDark = 0x7f060036
com.fishkaster.app:attr/endIconTintMode = 0x7f0401da
com.fishkaster.app:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0e00a4
com.fishkaster.app:drawable/common_google_signin_btn_text_light_focused = 0x7f080099
com.fishkaster.app:color/design_dark_default_color_background = 0x7f060044
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f14017c
com.fishkaster.app:color/catalyst_logbox_background = 0x7f060033
com.fishkaster.app:attr/tickMarkTint = 0x7f0404d4
com.fishkaster.app:color/call_notification_answer_color = 0x7f06002d
com.fishkaster.app:color/material_dynamic_neutral_variant30 = 0x7f060259
com.fishkaster.app:string/exo_controls_repeat_off_description = 0x7f13007e
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary90 = 0x7f06010b
com.fishkaster.app:color/button_material_light = 0x7f06002c
com.fishkaster.app:id/material_clock_face = 0x7f0a0154
com.fishkaster.app:color/material_personalized_color_primary_text_inverse = 0x7f0602b8
com.fishkaster.app:color/browser_actions_title_color = 0x7f06002a
com.fishkaster.app:layout/mtrl_alert_select_dialog_multichoice = 0x7f0d005c
com.fishkaster.app:attr/layout_scrollEffect = 0x7f0402ec
com.fishkaster.app:dimen/mtrl_slider_label_square_side = 0x7f07030e
com.fishkaster.app:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f14037d
com.fishkaster.app:drawable/abc_text_select_handle_middle_mtrl = 0x7f08006f
com.fishkaster.app:attr/exitAnim = 0x7f0401e9
com.fishkaster.app:attr/waveDecay = 0x7f040529
com.fishkaster.app:attr/flow_horizontalStyle = 0x7f04022a
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_surface = 0x7f0601af
com.fishkaster.app:drawable/autofill_inline_suggestion_chip_background = 0x7f08007a
com.fishkaster.app:attr/logoDescription = 0x7f040308
com.fishkaster.app:color/bright_foreground_disabled_material_light = 0x7f060022
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0e00cf
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f070234
com.fishkaster.app:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f0601a5
com.fishkaster.app:color/abc_tint_btn_checkable = 0x7f060013
com.fishkaster.app:attr/moveWhenScrollAtTop = 0x7f04037e
com.fishkaster.app:dimen/mtrl_navigation_rail_icon_size = 0x7f0702f6
com.fishkaster.app:attr/trackColorActive = 0x7f0404fc
com.fishkaster.app:attr/arcMode = 0x7f04003d
com.fishkaster.app:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f07016b
com.fishkaster.app:string/catalyst_sample_profiler_toggle = 0x7f130049
com.fishkaster.app:color/abc_search_url_text_selected = 0x7f060010
com.fishkaster.app:color/abc_search_url_text_pressed = 0x7f06000f
com.fishkaster.app:attr/tickRadiusInactive = 0x7f0404d7
com.fishkaster.app:attr/viewAspectRatio = 0x7f040524
com.fishkaster.app:color/abc_primary_text_material_dark = 0x7f06000b
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f140072
com.fishkaster.app:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0e00a3
com.fishkaster.app:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.fishkaster.app:styleable/MenuItem = 0x7f150065
com.fishkaster.app:attr/layout_constraintWidth_max = 0x7f0402dd
com.fishkaster.app:attr/motionDurationMedium2 = 0x7f040363
com.fishkaster.app:color/mtrl_popupmenu_overlay_color = 0x7f060303
com.fishkaster.app:attr/listMenuViewStyle = 0x7f0402fc
com.fishkaster.app:attr/counterMaxLength = 0x7f04015e
com.fishkaster.app:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f140091
com.fishkaster.app:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0701e8
com.fishkaster.app:drawable/abc_ratingbar_indicator_material = 0x7f08005a
com.fishkaster.app:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1403d4
com.fishkaster.app:color/material_divider_color = 0x7f06023f
com.fishkaster.app:layout/select_dialog_item_material = 0x7f0d008b
com.fishkaster.app:attr/height = 0x7f04024e
com.fishkaster.app:style/DialogAnimationFade = 0x7f140127
com.fishkaster.app:id/tag_screen_reader_focusable = 0x7f0a0226
com.fishkaster.app:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f14031c
com.fishkaster.app:bool/workmanager_test_configuration = 0x7f050006
com.fishkaster.app:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f140193
com.fishkaster.app:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004
com.fishkaster.app:bool/enable_system_alarm_service_default = 0x7f050002
com.fishkaster.app:styleable/MockView = 0x7f150067
com.fishkaster.app:attr/barrierDirection = 0x7f040073
com.fishkaster.app:attr/windowActionModeOverlay = 0x7f040530
com.fishkaster.app:attr/windowActionBar = 0x7f04052e
com.fishkaster.app:style/ThemeOverlay.Material3.Dialog = 0x7f1402d6
com.fishkaster.app:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f070165
com.fishkaster.app:attr/onHide = 0x7f04038f
com.fishkaster.app:attr/measureWithLargestChild = 0x7f040349
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Inverse = 0x7f140023
com.fishkaster.app:attr/largeFontVerticalOffsetAdjustment = 0x7f0402a5
com.fishkaster.app:color/m3_primary_text_disable_only = 0x7f0600c0
com.fishkaster.app:attr/wavePeriod = 0x7f04052b
com.fishkaster.app:dimen/m3_badge_vertical_offset = 0x7f0700dc
com.fishkaster.app:drawable/dev_menu_fab_icon = 0x7f0800a2
com.fishkaster.app:dimen/compat_button_padding_vertical_material = 0x7f07005c
com.fishkaster.app:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f140092
com.fishkaster.app:mipmap/ic_launcher_round = 0x7f100002
com.fishkaster.app:color/m3_ref_palette_error70 = 0x7f060123
com.fishkaster.app:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1402c9
com.fishkaster.app:attr/boxBackgroundColor = 0x7f040089
com.fishkaster.app:string/summary_description = 0x7f130147
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600eb
com.fishkaster.app:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f14009b
com.fishkaster.app:color/design_default_color_on_primary = 0x7f060055
com.fishkaster.app:attr/viewInflaterClass = 0x7f040525
com.fishkaster.app:color/material_dynamic_neutral_variant40 = 0x7f06025a
com.fishkaster.app:attr/verticalOffset = 0x7f040522
com.fishkaster.app:dimen/mtrl_card_elevation = 0x7f0702c5
com.fishkaster.app:id/material_clock_period_pm_button = 0x7f0a0158
com.fishkaster.app:dimen/abc_text_size_menu_material = 0x7f07004b
com.fishkaster.app:attr/checkedIconEnabled = 0x7f0400c0
com.fishkaster.app:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.fishkaster.app:attr/cropFixAspectRatio = 0x7f040170
com.fishkaster.app:drawable/avd_hide_password = 0x7f08007b
com.fishkaster.app:color/design_fab_stroke_top_inner_color = 0x7f060064
com.fishkaster.app:style/Theme.App.SplashScreen = 0x7f14023e
com.fishkaster.app:attr/uiTiltGestures = 0x7f040516
com.fishkaster.app:attr/uiRotateGestures = 0x7f040513
com.fishkaster.app:attr/contentInsetEnd = 0x7f04013e
com.fishkaster.app:color/m3_dynamic_dark_default_color_primary_text = 0x7f0600a2
com.fishkaster.app:attr/itemHorizontalPadding = 0x7f040282
com.fishkaster.app:attr/spinBars = 0x7f040437
com.fishkaster.app:attr/popExitAnim = 0x7f0403ba
com.fishkaster.app:attr/ratingBarStyle = 0x7f0403d2
com.fishkaster.app:color/abc_search_url_text = 0x7f06000d
com.fishkaster.app:color/dev_launcher_primary = 0x7f06006b
com.fishkaster.app:interpolator/m3_sys_motion_easing_emphasized = 0x7f0c0007
com.fishkaster.app:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f08001f
com.fishkaster.app:attr/keep_content_on_player_reset = 0x7f04029d
com.fishkaster.app:attr/drawableEndCompat = 0x7f0401ba
com.fishkaster.app:id/filter = 0x7f0a0107
com.fishkaster.app:attr/transitionDisable = 0x7f040508
com.fishkaster.app:attr/flow_verticalGap = 0x7f040233
com.fishkaster.app:attr/itemTextAppearanceActiveBoldEnabled = 0x7f040299
com.fishkaster.app:attr/trackTintMode = 0x7f040507
com.fishkaster.app:dimen/material_cursor_inset = 0x7f070256
com.fishkaster.app:id/exo_extra_controls = 0x7f0a00d9
com.fishkaster.app:attr/colorPrimaryDark = 0x7f04011a
com.fishkaster.app:attr/trackTint = 0x7f040506
com.fishkaster.app:attr/windowActionBarOverlay = 0x7f04052f
com.fishkaster.app:id/video_decoder_gl_surface_view = 0x7f0a0259
com.fishkaster.app:attr/trackStopIndicatorSize = 0x7f040504
com.fishkaster.app:id/beginOnFirstDraw = 0x7f0a006c
com.fishkaster.app:attr/font = 0x7f040236
com.fishkaster.app:attr/constraintSet = 0x7f040137
com.fishkaster.app:attr/trackDecoration = 0x7f0404ff
com.fishkaster.app:dimen/m3_searchview_divider_size = 0x7f070208
com.fishkaster.app:style/Widget.Material3.SearchView = 0x7f14040c
com.fishkaster.app:color/material_slider_thumb_color = 0x7f0602d7
com.fishkaster.app:dimen/m3_carousel_small_item_size_max = 0x7f070117
com.fishkaster.app:color/m3_sys_color_dark_surface_container_low = 0x7f06019c
com.fishkaster.app:attr/paddingBottomSystemWindowInsets = 0x7f040398
com.fishkaster.app:style/Theme.ReactNative.AppCompat.Light.NoActionBar.FullScreen = 0x7f1402b1
com.fishkaster.app:attr/topInsetScrimEnabled = 0x7f0404f5
com.fishkaster.app:id/touch_outside = 0x7f0a0245
com.fishkaster.app:attr/buttonIconTint = 0x7f04009f
com.fishkaster.app:attr/deltaPolarAngle = 0x7f0401a5
com.fishkaster.app:dimen/m3_navigation_item_shape_inset_top = 0x7f0701eb
com.fishkaster.app:attr/flow_lastVerticalStyle = 0x7f04022e
com.fishkaster.app:color/m3_slider_active_track_color = 0x7f060177
com.fishkaster.app:id/rectangleVerticalOnly = 0x7f0a01cb
com.fishkaster.app:attr/tooltipStyle = 0x7f0404f3
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f140343
com.fishkaster.app:attr/tooltipForegroundColor = 0x7f0404f1
com.fishkaster.app:attr/show_fastforward_button = 0x7f04041e
com.fishkaster.app:attr/toolbarSurfaceStyle = 0x7f0404f0
com.fishkaster.app:attr/toolbarNavigationButtonStyle = 0x7f0404ee
com.fishkaster.app:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0e000c
com.fishkaster.app:dimen/design_snackbar_action_inline_max_width = 0x7f070081
com.fishkaster.app:styleable/CoordinatorLayout_Layout = 0x7f15002e
com.fishkaster.app:attr/toggleCheckedStateOnClick = 0x7f0404ec
com.fishkaster.app:attr/colorControlActivated = 0x7f0400fc
com.fishkaster.app:attr/titlePositionInterpolator = 0x7f0404e7
com.fishkaster.app:dimen/notification_top_pad_large_text = 0x7f070340
com.fishkaster.app:id/deltaRelative = 0x7f0a00a7
com.fishkaster.app:attr/materialAlertDialogTitlePanelStyle = 0x7f040314
com.fishkaster.app:style/Widget.Material3.Chip.Filter = 0x7f1403ad
com.fishkaster.app:attr/fastScrollEnabled = 0x7f04020e
com.fishkaster.app:attr/useDrawerArrowDrawable = 0x7f04051c
com.fishkaster.app:id/exo_controls_background = 0x7f0a00d6
com.fishkaster.app:attr/titleMarginBottom = 0x7f0404e2
com.fishkaster.app:attr/textInputLayoutFocusedRectEnabled = 0x7f0404b8
com.fishkaster.app:dimen/m3_alert_dialog_icon_size = 0x7f0700c8
com.fishkaster.app:color/m3_appbar_overlay_color = 0x7f060086
com.fishkaster.app:attr/waveVariesBy = 0x7f04052d
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f140294
com.fishkaster.app:dimen/mtrl_switch_thumb_elevation = 0x7f07031d
com.fishkaster.app:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f140492
com.fishkaster.app:attr/shortcutMatchRequired = 0x7f040412
com.fishkaster.app:color/material_dynamic_neutral20 = 0x7f06024b
com.fishkaster.app:attr/tickRadiusActive = 0x7f0404d6
com.fishkaster.app:color/m3_sys_color_dynamic_light_primary = 0x7f0601d7
com.fishkaster.app:attr/colorScheme = 0x7f040120
com.fishkaster.app:attr/tickMarkTintMode = 0x7f0404d5
com.fishkaster.app:attr/chipEndPadding = 0x7f0400ca
com.fishkaster.app:style/Widget.MaterialComponents.Chip.Action = 0x7f140450
com.fishkaster.app:id/accessibility_custom_action_25 = 0x7f0a0028
com.fishkaster.app:attr/thumbTint = 0x7f0404cc
com.fishkaster.app:attr/titleMarginTop = 0x7f0404e5
com.fishkaster.app:drawable/ic_rotate_right_24 = 0x7f080112
com.fishkaster.app:attr/buttonIcon = 0x7f04009d
com.fishkaster.app:attr/prefixTextAppearance = 0x7f0403c3
com.fishkaster.app:anim/rns_ios_from_right_background_close = 0x7f01003e
com.fishkaster.app:attr/thumbStrokeColor = 0x7f0404c9
com.fishkaster.app:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f0701a3
com.fishkaster.app:attr/telltales_tailScale = 0x7f04048a
com.fishkaster.app:attr/thumbIconSize = 0x7f0404c5
com.fishkaster.app:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f14034f
com.fishkaster.app:id/m3_side_sheet = 0x7f0a014e
com.fishkaster.app:attr/colorAccent = 0x7f0400f8
com.fishkaster.app:drawable/extensions_icon = 0x7f0800f5
com.fishkaster.app:color/m3_sys_color_light_on_error = 0x7f0601f9
com.fishkaster.app:attr/textInputOutlinedStyle = 0x7f0404bb
com.fishkaster.app:attr/textInputFilledExposedDropdownMenuStyle = 0x7f0404b6
com.fishkaster.app:id/home = 0x7f0a0127
com.fishkaster.app:attr/itemShapeInsetEnd = 0x7f040291
com.fishkaster.app:color/mtrl_tabs_colored_ripple_color = 0x7f060309
com.fishkaster.app:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0e004f
com.fishkaster.app:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f140420
com.fishkaster.app:id/transition_layout_save = 0x7f0a024d
com.fishkaster.app:color/abc_hint_foreground_material_light = 0x7f060008
com.fishkaster.app:dimen/m3_card_elevation = 0x7f070110
com.fishkaster.app:attr/textColorAlertDialogListItem = 0x7f0404b2
com.fishkaster.app:anim/m3_side_sheet_exit_to_right = 0x7f01002e
com.fishkaster.app:attr/textAppearanceTitleSmall = 0x7f0404b1
com.fishkaster.app:bool/enable_system_foreground_service_default = 0x7f050003
com.fishkaster.app:attr/textAppearanceButton = 0x7f040492
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600d1
com.fishkaster.app:attr/textAppearanceHeadlineMedium = 0x7f04049e
com.fishkaster.app:attr/textAppearanceHeadline5 = 0x7f04049b
com.fishkaster.app:attr/cardElevation = 0x7f0400b0
com.fishkaster.app:color/m3_button_ripple_color = 0x7f06008d
com.fishkaster.app:attr/expandedTitleTextAppearance = 0x7f0401f3
com.fishkaster.app:id/save_non_transition_alpha = 0x7f0a01e1
com.fishkaster.app:string/abc_action_bar_home_description = 0x7f130000
com.fishkaster.app:attr/listDividerAlertDialog = 0x7f0402f9
com.fishkaster.app:attr/textAppearanceHeadline4 = 0x7f04049a
com.fishkaster.app:id/easeInOut = 0x7f0a00bc
com.fishkaster.app:attr/windowMinWidthMinor = 0x7f040536
com.fishkaster.app:attr/textAppearanceHeadline2 = 0x7f040498
com.fishkaster.app:attr/searchHintIcon = 0x7f0403fe
com.fishkaster.app:anim/abc_tooltip_exit = 0x7f01000b
com.fishkaster.app:attr/expoCropBackgroundColor = 0x7f0401f6
com.fishkaster.app:attr/textAppearanceHeadline1 = 0x7f040497
com.fishkaster.app:id/dragLeft = 0x7f0a00b6
com.fishkaster.app:attr/suffixText = 0x7f04045f
com.fishkaster.app:attr/textAllCaps = 0x7f04048c
com.fishkaster.app:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0700d4
com.fishkaster.app:attr/textLocale = 0x7f0404bd
com.fishkaster.app:attr/tabPaddingStart = 0x7f04047d
com.fishkaster.app:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f1404a7
com.fishkaster.app:color/background_floating_material_dark = 0x7f06001d
com.fishkaster.app:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f14043c
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600dc
com.fishkaster.app:integer/m3_sys_motion_duration_short4 = 0x7f0b0021
com.fishkaster.app:id/tag_window_insets_animation_callback = 0x7f0a022b
com.fishkaster.app:dimen/abc_star_medium = 0x7f07003c
com.fishkaster.app:attr/counterTextAppearance = 0x7f040161
com.fishkaster.app:attr/staggered = 0x7f04043d
com.fishkaster.app:attr/textAppearanceBodyLarge = 0x7f04048f
com.fishkaster.app:attr/layout_collapseMode = 0x7f0402b4
com.fishkaster.app:style/Base.V7.Theme.AppCompat.Light = 0x7f1400bf
com.fishkaster.app:layout/ime_base_split_test_activity = 0x7f0d0043
com.fishkaster.app:attr/textAppearanceBody1 = 0x7f04048d
com.fishkaster.app:attr/telltales_velocityMode = 0x7f04048b
com.fishkaster.app:styleable/RangeSlider = 0x7f150081
com.fishkaster.app:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f040200
com.fishkaster.app:dimen/mtrl_navigation_elevation = 0x7f0702eb
com.fishkaster.app:attr/thumbColor = 0x7f0404c1
com.fishkaster.app:string/mtrl_timepicker_confirm = 0x7f130121
com.fishkaster.app:string/common_google_play_services_unsupported_text = 0x7f13005c
com.fishkaster.app:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f070188
com.fishkaster.app:drawable/design_ic_visibility_off = 0x7f08009f
com.fishkaster.app:drawable/abc_list_selector_disabled_holo_dark = 0x7f080054
com.fishkaster.app:attr/targetId = 0x7f040487
com.fishkaster.app:attr/behavior_significantVelocityThreshold = 0x7f04007f
com.fishkaster.app:attr/roundedCornerRadius = 0x7f0403ee
com.fishkaster.app:attr/tabTextColor = 0x7f040485
com.fishkaster.app:color/mtrl_tabs_legacy_text_color_selector = 0x7f06030c
com.fishkaster.app:attr/tabSelectedTextAppearance = 0x7f040481
com.fishkaster.app:drawable/abc_list_selector_holo_dark = 0x7f080056
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1403fc
com.fishkaster.app:attr/addElevationShadow = 0x7f04002c
com.fishkaster.app:style/Platform.MaterialComponents.Light = 0x7f140164
com.fishkaster.app:anim/rns_ios_from_right_foreground_close = 0x7f010040
com.fishkaster.app:style/Widget.Material3.CompoundButton.Switch = 0x7f1403c4
com.fishkaster.app:attr/cropperLabelTextColor = 0x7f040188
com.fishkaster.app:attr/tabSecondaryStyle = 0x7f040480
com.fishkaster.app:attr/transitionPathRotate = 0x7f04050b
com.fishkaster.app:attr/actionBarTheme = 0x7f04000a
com.fishkaster.app:drawable/exo_styled_controls_speed = 0x7f0800f0
com.fishkaster.app:dimen/exo_styled_progress_layout_height = 0x7f0700b3
com.fishkaster.app:color/colorPrimary = 0x7f060035
com.fishkaster.app:attr/paddingBottomNoButtons = 0x7f040397
com.fishkaster.app:style/TextAppearance.Design.Tab = 0x7f140204
com.fishkaster.app:drawable/abc_tab_indicator_material = 0x7f08006b
com.fishkaster.app:anim/rns_slide_out_to_bottom = 0x7f010049
com.fishkaster.app:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f0701a5
com.fishkaster.app:attr/passwordToggleTint = 0x7f0403a6
com.fishkaster.app:style/Widget.Material3.Slider = 0x7f140413
com.fishkaster.app:dimen/m3_comp_outlined_card_outline_width = 0x7f070179
com.fishkaster.app:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1402e0
com.fishkaster.app:attr/tabPaddingEnd = 0x7f04047c
com.fishkaster.app:style/Widget.AppCompat.Light.ActionButton = 0x7f140349
com.fishkaster.app:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0e00ba
com.fishkaster.app:drawable/ic_fullscreen_exit_32dp = 0x7f080106
com.fishkaster.app:id/action_bar_container = 0x7f0a0042
com.fishkaster.app:id/exo_rew_with_amount = 0x7f0a00f0
com.fishkaster.app:attr/itemPaddingTop = 0x7f04028b
com.fishkaster.app:color/abc_search_url_text_normal = 0x7f06000e
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f140079
com.fishkaster.app:attr/tabPadding = 0x7f04047a
com.fishkaster.app:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f140053
com.fishkaster.app:attr/tabMode = 0x7f040479
com.fishkaster.app:dimen/m3_timepicker_window_elevation = 0x7f070245
com.fishkaster.app:attr/tabMaxWidth = 0x7f040477
com.fishkaster.app:attr/tabInlineLabel = 0x7f040476
com.fishkaster.app:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f140426
com.fishkaster.app:attr/tabIndicatorHeight = 0x7f040475
com.fishkaster.app:attr/state_collapsed = 0x7f040447
com.fishkaster.app:attr/tabIndicatorColor = 0x7f040472
com.fishkaster.app:styleable/PropertySet = 0x7f15007f
com.fishkaster.app:attr/cropCenterMoveEnabled = 0x7f04016d
com.fishkaster.app:attr/floatingActionButtonSmallPrimaryStyle = 0x7f04021b
com.fishkaster.app:dimen/m3_small_fab_max_image_size = 0x7f070212
com.fishkaster.app:string/common_google_play_services_install_title = 0x7f130058
com.fishkaster.app:attr/motionDurationExtraLong4 = 0x7f04035d
com.fishkaster.app:style/Theme.Material3.Dark.Dialog = 0x7f140265
com.fishkaster.app:attr/tabIndicatorAnimationMode = 0x7f040471
com.fishkaster.app:string/in_progress = 0x7f1300b7
com.fishkaster.app:color/m3_sys_color_dynamic_dark_error = 0x7f0601a3
com.fishkaster.app:attr/fabAlignmentModeEndMargin = 0x7f040203
com.fishkaster.app:attr/titleEnabled = 0x7f0404e0
com.fishkaster.app:dimen/mtrl_slider_thumb_radius = 0x7f070310
com.fishkaster.app:attr/tabGravity = 0x7f04046c
com.fishkaster.app:color/m3_selection_control_ripple_color_selector = 0x7f060175
com.fishkaster.app:color/dev_launcher_colorPrimaryDark = 0x7f06006a
com.fishkaster.app:dimen/material_time_picker_minimum_screen_width = 0x7f07026b
com.fishkaster.app:anim/design_bottom_sheet_slide_out = 0x7f01001f
com.fishkaster.app:attr/textAppearanceBodyMedium = 0x7f040490
com.fishkaster.app:attr/tabContentStart = 0x7f04046b
com.fishkaster.app:dimen/m3_btn_inset = 0x7f0700fd
com.fishkaster.app:attr/tabBackground = 0x7f04046a
com.fishkaster.app:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f14023b
com.fishkaster.app:attr/startIconScaleType = 0x7f040443
com.fishkaster.app:attr/behavior_peekHeight = 0x7f04007d
com.fishkaster.app:id/startVertical = 0x7f0a0215
com.fishkaster.app:attr/brightness = 0x7f040094
com.fishkaster.app:attr/surface_type = 0x7f040463
com.fishkaster.app:attr/onCross = 0x7f04038e
com.fishkaster.app:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f07018e
com.fishkaster.app:attr/textInputFilledDenseStyle = 0x7f0404b5
com.fishkaster.app:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f070186
com.fishkaster.app:attr/suffixTextColor = 0x7f040461
com.fishkaster.app:anim/rns_slide_out_to_right = 0x7f01004b
com.fishkaster.app:attr/placeholderTextColor = 0x7f0403b4
com.fishkaster.app:id/triangle = 0x7f0a0252
com.fishkaster.app:dimen/mtrl_btn_max_width = 0x7f07028a
com.fishkaster.app:styleable/GenericDraweeHierarchy = 0x7f15003e
com.fishkaster.app:anim/rns_slide_in_from_bottom = 0x7f010046
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1401e1
com.fishkaster.app:anim/rns_slide_in_from_left = 0x7f010047
com.fishkaster.app:attr/subtitleTextStyle = 0x7f04045e
com.fishkaster.app:attr/subtitleCentered = 0x7f04045b
com.fishkaster.app:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.fishkaster.app:dimen/m3_side_sheet_modal_elevation = 0x7f07020c
com.fishkaster.app:styleable/Tooltip = 0x7f1500a2
com.fishkaster.app:attr/touchAnchorId = 0x7f0404f6
com.fishkaster.app:attr/subtitle = 0x7f04045a
com.fishkaster.app:drawable/notification_template_icon_low_bg = 0x7f08015c
com.fishkaster.app:attr/flow_horizontalGap = 0x7f040229
com.fishkaster.app:attr/submitBackground = 0x7f040459
com.fishkaster.app:attr/maxButtonHeight = 0x7f040341
com.fishkaster.app:id/contiguous = 0x7f0a0096
com.fishkaster.app:color/design_fab_stroke_end_inner_color = 0x7f060062
com.fishkaster.app:attr/waveShape = 0x7f04052c
com.fishkaster.app:drawable/$avd_hide_password__1 = 0x7f080001
com.fishkaster.app:attr/subheaderInsetStart = 0x7f040457
com.fishkaster.app:attr/textAppearanceListItemSecondary = 0x7f0404a6
com.fishkaster.app:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f140188
com.fishkaster.app:attr/collapsedTitleGravity = 0x7f0400ef
com.fishkaster.app:color/switch_thumb_disabled_material_light = 0x7f060327
com.fishkaster.app:id/accessibility_custom_action_11 = 0x7f0a0019
com.fishkaster.app:attr/colorSecondaryVariant = 0x7f040125
com.fishkaster.app:drawable/abc_switch_thumb_material = 0x7f080069
com.fishkaster.app:color/m3_sys_color_secondary_fixed_dim = 0x7f06021d
com.fishkaster.app:attr/hide_during_ads = 0x7f040258
com.fishkaster.app:attr/strokeColor = 0x7f040452
com.fishkaster.app:attr/drawerLayoutCornerSize = 0x7f0401c3
com.fishkaster.app:attr/state_indeterminate = 0x7f04044b
com.fishkaster.app:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0e00b8
com.fishkaster.app:color/m3_filled_icon_button_container_color_selector = 0x7f0600b1
com.fishkaster.app:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f140461
com.fishkaster.app:attr/state_error = 0x7f04044a
com.fishkaster.app:anim/design_snackbar_out = 0x7f010021
com.fishkaster.app:attr/itemShapeFillColor = 0x7f04028f
com.fishkaster.app:attr/deltaPolarRadius = 0x7f0401a6
com.fishkaster.app:color/material_dynamic_primary70 = 0x7f06026a
com.fishkaster.app:attr/state_dragged = 0x7f040449
com.fishkaster.app:attr/endIconDrawable = 0x7f0401d5
com.fishkaster.app:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0702b5
com.fishkaster.app:dimen/m3_ripple_pressed_alpha = 0x7f0701fe
com.fishkaster.app:anim/catalyst_slide_down = 0x7f01001c
com.fishkaster.app:drawable/common_google_signin_btn_text_light_normal = 0x7f08009a
com.fishkaster.app:attr/state_above_anchor = 0x7f040446
com.fishkaster.app:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0e00bc
com.fishkaster.app:dimen/abc_floating_window_z = 0x7f07002f
com.fishkaster.app:attr/contentDescription = 0x7f04013d
com.fishkaster.app:attr/startIconCheckable = 0x7f04043f
com.fishkaster.app:id/startHorizontal = 0x7f0a0213
com.fishkaster.app:attr/srcCompat = 0x7f04043b
com.fishkaster.app:attr/controlBackground = 0x7f04014d
com.fishkaster.app:attr/chipStrokeColor = 0x7f0400d8
com.fishkaster.app:attr/spinnerStyle = 0x7f040439
com.fishkaster.app:style/Base.Widget.Material3.TabLayout = 0x7f140111
com.fishkaster.app:attr/spanCount = 0x7f040436
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary40 = 0x7f060106
com.fishkaster.app:attr/singleSelection = 0x7f040430
com.fishkaster.app:attr/motionEasingLinearInterpolator = 0x7f040371
com.fishkaster.app:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f080027
com.fishkaster.app:attr/alertDialogStyle = 0x7f04002f
com.fishkaster.app:color/m3_sys_color_light_tertiary_container = 0x7f060213
com.fishkaster.app:dimen/mtrl_progress_circular_inset_small = 0x7f0702fd
com.fishkaster.app:attr/simpleItemSelectedRippleColor = 0x7f04042c
com.fishkaster.app:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f140282
com.fishkaster.app:color/material_dynamic_primary0 = 0x7f060262
com.fishkaster.app:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f14034e
com.fishkaster.app:color/m3_ref_palette_secondary100 = 0x7f06015c
com.fishkaster.app:attr/backgroundStacked = 0x7f04005b
com.fishkaster.app:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0e0069
com.fishkaster.app:attr/simpleItemLayout = 0x7f04042a
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f070137
com.fishkaster.app:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700e3
com.fishkaster.app:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0601e6
com.fishkaster.app:id/design_menu_item_action_area_stub = 0x7f0a00aa
com.fishkaster.app:attr/shapeAppearanceCornerLarge = 0x7f04040a
com.fishkaster.app:attr/show_vr_button = 0x7f040425
com.fishkaster.app:color/material_dynamic_secondary10 = 0x7f060270
com.fishkaster.app:drawable/abc_vector_test = 0x7f080076
com.fishkaster.app:color/abc_color_highlight_material = 0x7f060004
com.fishkaster.app:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f08014d
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0601df
com.fishkaster.app:attr/show_timeout = 0x7f040424
com.fishkaster.app:string/exo_controls_show = 0x7f130083
com.fishkaster.app:attr/titleMargin = 0x7f0404e1
com.fishkaster.app:dimen/mtrl_tooltip_minHeight = 0x7f07032e
com.fishkaster.app:style/Theme.Material3.Light.BottomSheetDialog = 0x7f14027a
com.fishkaster.app:id/navigation_bar_item_active_indicator_view = 0x7f0a018d
com.fishkaster.app:attr/drawableTint = 0x7f0401bf
com.fishkaster.app:macro/m3_comp_slider_disabled_handle_color = 0x7f0e010d
com.fishkaster.app:attr/show_next_button = 0x7f04041f
com.fishkaster.app:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070233
com.fishkaster.app:id/view_tree_view_model_store_owner = 0x7f0a0262
com.fishkaster.app:color/exo_black_opacity_60 = 0x7f060074
com.fishkaster.app:attr/show_buffering = 0x7f04041d
com.fishkaster.app:attr/actionModeFindDrawable = 0x7f040017
com.fishkaster.app:id/embed = 0x7f0a00c2
com.fishkaster.app:attr/cropBorderCornerOffset = 0x7f040169
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0e0070
com.fishkaster.app:id/right_icon = 0x7f0a01d2
com.fishkaster.app:styleable/TextInputLayout = 0x7f15009f
com.fishkaster.app:drawable/notification_bg_low = 0x7f080154
com.fishkaster.app:attr/showPaths = 0x7f04041a
com.fishkaster.app:attr/showDelay = 0x7f040416
com.fishkaster.app:attr/materialCardViewFilledStyle = 0x7f040329
com.fishkaster.app:style/Base.V14.Theme.Material3.Dark = 0x7f14008c
com.fishkaster.app:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.fishkaster.app:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f14047f
com.fishkaster.app:attr/clockHandColor = 0x7f0400e1
com.fishkaster.app:attr/shapeAppearanceSmallComponent = 0x7f040410
com.fishkaster.app:attr/shapeAppearanceOverlay = 0x7f04040f
com.fishkaster.app:attr/behavior_halfExpandedRatio = 0x7f04007a
com.fishkaster.app:color/m3_ref_palette_neutral_variant95 = 0x7f06014b
com.fishkaster.app:color/background_material_light = 0x7f060020
com.fishkaster.app:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0e00f0
com.fishkaster.app:attr/arrowShaftLength = 0x7f040040
com.fishkaster.app:attr/listPreferredItemPaddingEnd = 0x7f040301
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0e0156
com.fishkaster.app:layout/mtrl_calendar_month = 0x7f0d0063
com.fishkaster.app:attr/actionDropDownStyle = 0x7f04000d
com.fishkaster.app:attr/checkedIconMargin = 0x7f0400c2
com.fishkaster.app:attr/shapeAppearanceCornerMedium = 0x7f04040b
com.fishkaster.app:attr/colorBackgroundFloating = 0x7f0400f9
com.fishkaster.app:attr/alphabeticModifiers = 0x7f040033
com.fishkaster.app:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f040465
com.fishkaster.app:attr/searchViewStyle = 0x7f040401
com.fishkaster.app:styleable/MaterialTextAppearance = 0x7f150060
com.fishkaster.app:attr/searchPrefixText = 0x7f040400
com.fishkaster.app:styleable/FragmentContainerView = 0x7f15003d
com.fishkaster.app:string/abc_searchview_description_voice = 0x7f130017
com.fishkaster.app:color/design_dark_default_color_on_surface = 0x7f06004a
com.fishkaster.app:attr/scrubber_dragged_size = 0x7f0403fb
com.fishkaster.app:attr/scrubber_color = 0x7f0403f9
com.fishkaster.app:dimen/m3_searchbar_padding_start = 0x7f070205
com.fishkaster.app:attr/hintTextColor = 0x7f04025d
com.fishkaster.app:attr/scrimVisibleHeightTrigger = 0x7f0403f8
com.fishkaster.app:dimen/abc_dialog_padding_top_material = 0x7f070025
com.fishkaster.app:attr/spinnerDropDownItemStyle = 0x7f040438
com.fishkaster.app:drawable/ic_call_decline = 0x7f080100
com.fishkaster.app:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.fishkaster.app:style/Widget.Material3.Button.TextButton.Icon = 0x7f1403a2
com.fishkaster.app:id/BOTTOM_START = 0x7f0a0002
com.fishkaster.app:attr/paddingStartSystemWindowInsets = 0x7f04039d
com.fishkaster.app:attr/scrimAnimationDuration = 0x7f0403f6
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0e0071
com.fishkaster.app:attr/allowStacking = 0x7f040031
com.fishkaster.app:attr/fastScrollHorizontalThumbDrawable = 0x7f04020f
com.fishkaster.app:drawable/$m3_avd_show_password__2 = 0x7f08000b
com.fishkaster.app:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.fishkaster.app:style/Widget.Design.NavigationView = 0x7f14037c
com.fishkaster.app:attr/thumbStrokeWidth = 0x7f0404ca
com.fishkaster.app:attr/labelStyle = 0x7f0402a3
com.fishkaster.app:id/progress_horizontal = 0x7f0a01c5
com.fishkaster.app:attr/scopeUris = 0x7f0403f5
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Headline = 0x7f140022
com.fishkaster.app:dimen/abc_control_corner_material = 0x7f070018
com.fishkaster.app:attr/coordinatorLayoutStyle = 0x7f04014f
com.fishkaster.app:attr/fontProviderSystemFontFamily = 0x7f04023f
com.fishkaster.app:string/abc_searchview_description_clear = 0x7f130013
com.fishkaster.app:color/m3_chip_assist_text_color = 0x7f060096
com.fishkaster.app:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.fishkaster.app:attr/motionDurationShort3 = 0x7f040368
com.fishkaster.app:attr/listItemLayout = 0x7f0402fa
com.fishkaster.app:attr/textAppearanceListItem = 0x7f0404a5
com.fishkaster.app:attr/roundingBorderWidth = 0x7f0403f1
com.fishkaster.app:style/TextAppearance.AppCompat.Display1 = 0x7f1401c5
com.fishkaster.app:dimen/mtrl_snackbar_background_corner_radius = 0x7f070317
com.fishkaster.app:attr/iconStartPadding = 0x7f040268
com.fishkaster.app:string/common_signin_button_text_long = 0x7f130064
com.fishkaster.app:attr/materialButtonOutlinedStyle = 0x7f040316
com.fishkaster.app:macro/m3_comp_switch_selected_handle_color = 0x7f0e0124
com.fishkaster.app:attr/contentPaddingRight = 0x7f040148
com.fishkaster.app:attr/roundTopLeft = 0x7f0403ea
com.fishkaster.app:attr/pivotAnchor = 0x7f0403af
com.fishkaster.app:color/expoCropToolbarColor = 0x7f06007f
com.fishkaster.app:style/Widget.AppCompat.AutoCompleteTextView = 0x7f140330
com.fishkaster.app:style/TextAppearance.Material3.SearchView = 0x7f140224
com.fishkaster.app:dimen/mtrl_tooltip_cornerSize = 0x7f07032d
com.fishkaster.app:id/material_timepicker_mode_button = 0x7f0a0162
com.fishkaster.app:id/accessibility_label = 0x7f0a0037
com.fishkaster.app:attr/badgeWidePadding = 0x7f040068
com.fishkaster.app:dimen/m3_navigation_item_horizontal_padding = 0x7f0701e6
com.fishkaster.app:attr/trackDecorationTintMode = 0x7f040501
com.fishkaster.app:attr/roundPercent = 0x7f0403e8
com.fishkaster.app:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1402da
com.fishkaster.app:id/open_search_view_status_bar_spacer = 0x7f0a01aa
com.fishkaster.app:attr/attributeName = 0x7f040041
com.fishkaster.app:attr/textAppearanceLabelSmall = 0x7f0404a2
com.fishkaster.app:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f070276
com.fishkaster.app:attr/roundAsCircle = 0x7f0403e3
com.fishkaster.app:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f140080
com.fishkaster.app:attr/shapeAppearance = 0x7f040407
com.fishkaster.app:attr/retryImage = 0x7f0403de
com.fishkaster.app:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f080011
com.fishkaster.app:color/m3_fab_efab_foreground_color_selector = 0x7f0600af
com.fishkaster.app:attr/popUpToSaveState = 0x7f0403bd
com.fishkaster.app:color/browser_actions_text_color = 0x7f060029
com.fishkaster.app:anim/catalyst_push_up_in = 0x7f01001a
com.fishkaster.app:attr/errorEnabled = 0x7f0401e2
com.fishkaster.app:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700cb
com.fishkaster.app:attr/layout_goneMarginLeft = 0x7f0402e5
com.fishkaster.app:attr/removeEmbeddedFabElevation = 0x7f0403da
com.fishkaster.app:attr/recyclerViewStyle = 0x7f0403d5
com.fishkaster.app:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f140438
com.fishkaster.app:attr/checkedTextViewStyle = 0x7f0400c7
com.fishkaster.app:id/italic = 0x7f0a013e
com.fishkaster.app:attr/floatingActionButtonStyle = 0x7f040220
com.fishkaster.app:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f1402aa
com.fishkaster.app:attr/windowNoTitle = 0x7f040537
com.fishkaster.app:attr/round = 0x7f0403e2
com.fishkaster.app:attr/behavior_autoHide = 0x7f040075
com.fishkaster.app:attr/time_bar_min_update_interval = 0x7f0404d9
com.fishkaster.app:attr/radioButtonStyle = 0x7f0403d0
com.fishkaster.app:attr/queryBackground = 0x7f0403cd
com.fishkaster.app:attr/counterTextColor = 0x7f040162
com.fishkaster.app:layout/design_menu_item_action_area = 0x7f0d002c
com.fishkaster.app:attr/chipIconSize = 0x7f0400ce
com.fishkaster.app:attr/indicatorDirectionCircular = 0x7f040273
com.fishkaster.app:attr/progressBarAutoRotateInterval = 0x7f0403c8
com.fishkaster.app:attr/mock_label = 0x7f040354
com.fishkaster.app:dimen/m3_searchbar_margin_vertical = 0x7f070203
com.fishkaster.app:styleable/KeyTrigger = 0x7f15004a
com.fishkaster.app:attr/popupMenuBackground = 0x7f0403be
com.fishkaster.app:attr/fastScrollVerticalThumbDrawable = 0x7f040211
com.fishkaster.app:attr/itemTextAppearance = 0x7f040297
com.fishkaster.app:color/m3_sys_color_on_primary_fixed = 0x7f060214
com.fishkaster.app:attr/snackbarButtonStyle = 0x7f040433
com.fishkaster.app:attr/player_layout_id = 0x7f0403b8
com.fishkaster.app:attr/flow_lastHorizontalBias = 0x7f04022b
com.fishkaster.app:color/m3_ref_palette_neutral_variant90 = 0x7f06014a
com.fishkaster.app:style/ThemeOverlay.Material3.ActionBar = 0x7f1402be
com.fishkaster.app:color/m3_timepicker_clock_text_color = 0x7f060231
com.fishkaster.app:attr/played_color = 0x7f0403b7
com.fishkaster.app:color/m3_sys_color_dark_inverse_on_surface = 0x7f060183
com.fishkaster.app:style/TextAppearance.MaterialComponents.Headline5 = 0x7f140233
com.fishkaster.app:color/m3_checkbox_button_tint = 0x7f060095
com.fishkaster.app:style/Base.Theme.MaterialComponents.Bridge = 0x7f140069
com.fishkaster.app:color/material_dynamic_color_light_on_error_container = 0x7f060247
com.fishkaster.app:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f140458
com.fishkaster.app:attr/placeholder_emptyVisibility = 0x7f0403b5
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f140479
com.fishkaster.app:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0e00ac
com.fishkaster.app:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0e006e
com.fishkaster.app:layout/dev_loading_view = 0x7f0d0035
com.fishkaster.app:attr/snackbarTextViewStyle = 0x7f040435
com.fishkaster.app:id/useLogo = 0x7f0a0257
com.fishkaster.app:attr/cursorColor = 0x7f04018d
com.fishkaster.app:color/primary_dark_material_dark = 0x7f060317
com.fishkaster.app:attr/unplayed_color = 0x7f040519
com.fishkaster.app:attr/subtitleTextAppearance = 0x7f04045c
com.fishkaster.app:id/scrollIndicatorDown = 0x7f0a01e7
com.fishkaster.app:styleable/RecycleListView = 0x7f150082
com.fishkaster.app:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0e001f
com.fishkaster.app:attr/placeholderText = 0x7f0403b2
com.fishkaster.app:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f08012b
com.fishkaster.app:attr/perpendicularPath_percent = 0x7f0403ae
com.fishkaster.app:attr/percentY = 0x7f0403ad
com.fishkaster.app:color/m3_sys_color_dark_outline = 0x7f060191
com.fishkaster.app:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1403c8
com.fishkaster.app:attr/layout_insetEdge = 0x7f0402e9
com.fishkaster.app:color/m3_sys_color_light_on_surface_variant = 0x7f060200
com.fishkaster.app:style/ThemeOverlay.AppCompat.Light = 0x7f1402bb
com.fishkaster.app:attr/path_percent = 0x7f0403a9
com.fishkaster.app:attr/pathMotionArc = 0x7f0403a8
com.fishkaster.app:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0e006a
com.fishkaster.app:attr/materialIconButtonFilledTonalStyle = 0x7f040332
com.fishkaster.app:string/exo_controls_play_description = 0x7f13007a
com.fishkaster.app:drawable/mtrl_popupmenu_background_overlay = 0x7f080143
com.fishkaster.app:id/mtrl_picker_fullscreen = 0x7f0a0181
com.fishkaster.app:attr/passwordToggleDrawable = 0x7f0403a4
com.fishkaster.app:color/catalyst_redbox_background = 0x7f060034
com.fishkaster.app:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0e00de
com.fishkaster.app:attr/panelMenuListTheme = 0x7f0403a1
com.fishkaster.app:attr/panelBackground = 0x7f0403a0
com.fishkaster.app:attr/colorOnContainer = 0x7f040102
com.fishkaster.app:color/common_google_signin_btn_text_light_focused = 0x7f06003f
com.fishkaster.app:attr/paddingTopNoTitle = 0x7f04039e
com.fishkaster.app:id/role = 0x7f0a01dd
com.fishkaster.app:id/invisible = 0x7f0a013b
com.fishkaster.app:color/m3_ref_palette_error30 = 0x7f06011f
com.fishkaster.app:attr/paddingStart = 0x7f04039c
com.fishkaster.app:layout/abc_list_menu_item_icon = 0x7f0d000f
com.fishkaster.app:attr/reverseLayout = 0x7f0403e0
com.fishkaster.app:dimen/m3_card_hovered_z = 0x7f070111
com.fishkaster.app:attr/badgeShapeAppearance = 0x7f040061
com.fishkaster.app:attr/isAutofillInlineSuggestionTheme = 0x7f04027a
com.fishkaster.app:id/outward = 0x7f0a01af
com.fishkaster.app:attr/paddingEnd = 0x7f040399
com.fishkaster.app:attr/closeItemLayout = 0x7f0400eb
com.fishkaster.app:color/m3_assist_chip_stroke_color = 0x7f060088
com.fishkaster.app:color/abc_tint_switch_track = 0x7f060018
com.fishkaster.app:layout/ime_secondary_split_test_activity = 0x7f0d0044
com.fishkaster.app:attr/actionBarWidgetTheme = 0x7f04000b
com.fishkaster.app:attr/materialCircleRadius = 0x7f04032c
com.fishkaster.app:color/m3_ref_palette_neutral_variant20 = 0x7f060143
com.fishkaster.app:plurals/exo_controls_rewind_by_amount_description = 0x7f110001
com.fishkaster.app:attr/expandActivityOverflowButtonDrawable = 0x7f0401ea
com.fishkaster.app:color/mtrl_textinput_filled_box_default_background_color = 0x7f060311
com.fishkaster.app:attr/uiScrollGesturesDuringRotateOrZoom = 0x7f040515
com.fishkaster.app:attr/overlapAnchor = 0x7f040394
com.fishkaster.app:id/special_effects_controller_view_tag = 0x7f0a0207
com.fishkaster.app:attr/iconTintMode = 0x7f04026a
com.fishkaster.app:drawable/abc_ic_clear_material = 0x7f08003f
com.fishkaster.app:attr/onTouchUp = 0x7f040393
com.fishkaster.app:color/mtrl_on_surface_ripple_color = 0x7f060300
com.fishkaster.app:color/m3_sys_color_dark_inverse_primary = 0x7f060184
com.fishkaster.app:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0e00c5
com.fishkaster.app:attr/collapsingToolbarLayoutLargeSize = 0x7f0400f2
com.fishkaster.app:dimen/exo_styled_bottom_bar_time_padding = 0x7f0700ad
com.fishkaster.app:dimen/mtrl_fab_min_touch_target = 0x7f0702dd
com.fishkaster.app:color/m3_ref_palette_neutral80 = 0x7f060137
com.fishkaster.app:dimen/design_bottom_navigation_item_max_width = 0x7f070068
com.fishkaster.app:string/menuitem_description = 0x7f1300e3
com.fishkaster.app:attr/actionBarItemBackground = 0x7f040002
com.fishkaster.app:color/m3_ref_palette_neutral6 = 0x7f060134
com.fishkaster.app:style/ThemeOverlay.AppCompat.Dark = 0x7f1402b5
com.fishkaster.app:attr/offsetAlignmentMode = 0x7f04038d
com.fishkaster.app:id/skip_next_button = 0x7f0a01ff
com.fishkaster.app:attr/actionModePopupWindowStyle = 0x7f040019
com.fishkaster.app:attr/hideNavigationIcon = 0x7f040255
com.fishkaster.app:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f1300c1
com.fishkaster.app:attr/layout_constraintCircleAngle = 0x7f0402be
com.fishkaster.app:id/exo_duration = 0x7f0a00d7
com.fishkaster.app:dimen/abc_star_small = 0x7f07003d
com.fishkaster.app:dimen/m3_badge_offset = 0x7f0700da
com.fishkaster.app:layout/mtrl_alert_dialog_title = 0x7f0d005a
com.fishkaster.app:color/material_dynamic_neutral99 = 0x7f060254
com.fishkaster.app:id/search_mag_icon = 0x7f0a01f1
com.fishkaster.app:attr/navigationRailStyle = 0x7f040385
com.fishkaster.app:attr/navigationIconTint = 0x7f040383
com.fishkaster.app:attr/navigationIcon = 0x7f040382
com.fishkaster.app:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f08014c
com.fishkaster.app:attr/cardViewStyle = 0x7f0400b5
com.fishkaster.app:attr/navGraph = 0x7f040380
com.fishkaster.app:color/m3_ref_palette_secondary95 = 0x7f060165
com.fishkaster.app:attr/minTouchTargetSize = 0x7f040351
com.fishkaster.app:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f14029d
com.fishkaster.app:dimen/m3_ripple_focused_alpha = 0x7f0701fc
com.fishkaster.app:attr/triggerReceiver = 0x7f04050e
com.fishkaster.app:attr/color = 0x7f0400f7
com.fishkaster.app:attr/autofillInlineSuggestionStartIconStyle = 0x7f04004e
com.fishkaster.app:attr/layout_constraintTag = 0x7f0402d5
com.fishkaster.app:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.fishkaster.app:attr/overlay = 0x7f040395
com.fishkaster.app:color/m3_sys_color_light_error = 0x7f0601f3
com.fishkaster.app:id/ghost_view_holder = 0x7f0a011b
com.fishkaster.app:attr/isMaterialTheme = 0x7f04027e
com.fishkaster.app:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0e0012
com.fishkaster.app:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0e004e
com.fishkaster.app:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f04036e
com.fishkaster.app:dimen/notification_big_circle_margin = 0x7f070334
com.fishkaster.app:attr/motionEasingEmphasized = 0x7f04036c
com.fishkaster.app:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f140113
com.fishkaster.app:attr/dayInvalidStyle = 0x7f04019b
com.fishkaster.app:attr/motionEasingDecelerated = 0x7f04036b
com.fishkaster.app:attr/motionDurationShort4 = 0x7f040369
com.fishkaster.app:string/fallback_menu_item_share_link = 0x7f1300ac
com.fishkaster.app:color/m3_ref_palette_secondary40 = 0x7f06015f
com.fishkaster.app:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0e004d
com.fishkaster.app:color/material_personalized_color_control_highlight = 0x7f0602a2
com.fishkaster.app:drawable/common_google_signin_btn_icon_dark_normal = 0x7f08008c
com.fishkaster.app:dimen/m3_comp_slider_active_handle_leading_space = 0x7f0701a8
com.fishkaster.app:attr/colorOnTertiaryContainer = 0x7f040113
com.fishkaster.app:attr/motionDurationShort2 = 0x7f040367
com.fishkaster.app:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1401d0
com.fishkaster.app:attr/motionDurationMedium1 = 0x7f040362
com.fishkaster.app:drawable/abc_list_divider_material = 0x7f08004c
com.fishkaster.app:drawable/branch_icon = 0x7f08007d
com.fishkaster.app:attr/constraintSetStart = 0x7f040139
com.fishkaster.app:attr/motionDurationLong3 = 0x7f040360
com.fishkaster.app:attr/motionDurationLong2 = 0x7f04035f
com.fishkaster.app:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f070190
com.fishkaster.app:attr/mock_showDiagonals = 0x7f040357
com.fishkaster.app:dimen/m3_appbar_size_compact = 0x7f0700cf
com.fishkaster.app:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f07021f
com.fishkaster.app:color/m3_dark_default_color_primary_text = 0x7f06009b
com.fishkaster.app:attr/transitionFlags = 0x7f04050a
com.fishkaster.app:attr/minSeparation = 0x7f040350
com.fishkaster.app:attr/mimeType = 0x7f04034d
com.fishkaster.app:attr/menuGravity = 0x7f04034c
com.fishkaster.app:attr/lineHeight = 0x7f0402f3
com.fishkaster.app:style/Widget.AppCompat.ListMenuView = 0x7f140356
com.fishkaster.app:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0e00a2
com.fishkaster.app:id/open_search_view_toolbar_container = 0x7f0a01ac
com.fishkaster.app:attr/scrubber_enabled_size = 0x7f0403fd
com.fishkaster.app:color/m3_ref_palette_neutral_variant80 = 0x7f060149
com.fishkaster.app:attr/colorSurfaceContainerHighest = 0x7f04012a
com.fishkaster.app:attr/collapsingToolbarLayoutStyle = 0x7f0400f6
com.fishkaster.app:drawable/inspect = 0x7f080115
com.fishkaster.app:attr/startIconTint = 0x7f040444
com.fishkaster.app:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f14029c
com.fishkaster.app:attr/bottomSheetDialogTheme = 0x7f040086
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f140207
com.fishkaster.app:attr/materialDividerHeavyStyle = 0x7f04032f
com.fishkaster.app:attr/colorPrimary = 0x7f040118
com.fishkaster.app:dimen/abc_panel_menu_list_width = 0x7f070034
com.fishkaster.app:attr/chipStyle = 0x7f0400da
com.fishkaster.app:color/mtrl_card_view_foreground = 0x7f0602e7
com.fishkaster.app:attr/cameraTargetLng = 0x7f0400ab
com.fishkaster.app:color/m3_dynamic_primary_text_disable_only = 0x7f0600ab
com.fishkaster.app:color/m3_button_foreground_color_selector = 0x7f06008b
com.fishkaster.app:style/CalendarDatePickerStyle = 0x7f140123
com.fishkaster.app:layout/material_time_input = 0x7f0d0054
com.fishkaster.app:drawable/abc_list_selector_holo_light = 0x7f080057
com.fishkaster.app:attr/autoSizeTextType = 0x7f040049
com.fishkaster.app:attr/maxVelocity = 0x7f040347
com.fishkaster.app:attr/maxNumber = 0x7f040346
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1401e0
com.fishkaster.app:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f140120
com.fishkaster.app:attr/maxImageSize = 0x7f040344
com.fishkaster.app:id/flip = 0x7f0a0112
com.fishkaster.app:attr/motionTarget = 0x7f04037b
com.fishkaster.app:color/material_harmonized_color_error = 0x7f060290
com.fishkaster.app:dimen/mtrl_btn_pressed_z = 0x7f07028f
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_error = 0x7f0601cb
com.fishkaster.app:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1400d2
com.fishkaster.app:anim/rns_default_exit_out = 0x7f010035
com.fishkaster.app:attr/materialSearchBarStyle = 0x7f040335
com.fishkaster.app:attr/colorOnError = 0x7f040104
com.fishkaster.app:attr/tabIndicatorAnimationDuration = 0x7f040470
com.fishkaster.app:attr/materialIconButtonOutlinedStyle = 0x7f040333
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0e0164
com.fishkaster.app:styleable/AspectRatioFrameLayout = 0x7f150014
com.fishkaster.app:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0702ef
com.fishkaster.app:color/m3_ref_palette_error80 = 0x7f060124
com.fishkaster.app:attr/hoveredFocusedTranslationZ = 0x7f040262
com.fishkaster.app:attr/actionProviderClass = 0x7f040022
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f140346
com.fishkaster.app:layout/exo_track_selection_dialog = 0x7f0d0040
com.fishkaster.app:attr/materialCardViewStyle = 0x7f04032b
com.fishkaster.app:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1402f8
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f1401b5
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f070139
com.fishkaster.app:style/Widget.MaterialComponents.Button.TextButton = 0x7f140446
com.fishkaster.app:attr/extendedFloatingActionButtonStyle = 0x7f0401fe
com.fishkaster.app:attr/selectableItemBackgroundBorderless = 0x7f040404
com.fishkaster.app:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f07013e
com.fishkaster.app:id/use_hardware_layer = 0x7f0a0258
com.fishkaster.app:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f14029a
com.fishkaster.app:color/mtrl_textinput_focused_box_stroke_color = 0x7f060312
com.fishkaster.app:attr/uiZoomControls = 0x7f040517
com.fishkaster.app:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1400a1
com.fishkaster.app:attr/materialCalendarTheme = 0x7f040326
com.fishkaster.app:attr/materialCalendarHeaderTitle = 0x7f040321
com.fishkaster.app:dimen/material_textinput_min_width = 0x7f070269
com.fishkaster.app:attr/endIconContentDescription = 0x7f0401d4
com.fishkaster.app:color/material_timepicker_clockface = 0x7f0602db
com.fishkaster.app:layout/amu_text_bubble = 0x7f0d001e
com.fishkaster.app:color/m3_slider_active_track_color_legacy = 0x7f060178
com.fishkaster.app:attr/materialCalendarHeaderDivider = 0x7f04031e
com.fishkaster.app:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f14010a
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Display4 = 0x7f140021
com.fishkaster.app:dimen/mtrl_calendar_day_height = 0x7f07029d
com.fishkaster.app:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0601e9
com.fishkaster.app:attr/useCompatPadding = 0x7f04051b
com.fishkaster.app:string/mtrl_exceed_max_badge_number_suffix = 0x7f1300f2
com.fishkaster.app:attr/trackDecorationTint = 0x7f040500
com.fishkaster.app:id/exo_overflow_hide = 0x7f0a00e3
com.fishkaster.app:attr/materialCalendarFullscreenTheme = 0x7f04031b
com.fishkaster.app:attr/materialCalendarDayOfWeekLabel = 0x7f04031a
com.fishkaster.app:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0e013d
com.fishkaster.app:attr/tickColorActive = 0x7f0404d1
com.fishkaster.app:style/TextAppearance.Compat.Notification.Media = 0x7f1401f5
com.fishkaster.app:attr/uiScrollGestures = 0x7f040514
com.fishkaster.app:attr/materialAlertDialogTitleTextStyle = 0x7f040315
com.fishkaster.app:color/design_default_color_primary_variant = 0x7f06005a
com.fishkaster.app:attr/materialAlertDialogTitleIconStyle = 0x7f040313
com.fishkaster.app:attr/textAppearanceLargePopupMenu = 0x7f0404a3
com.fishkaster.app:attr/materialAlertDialogTheme = 0x7f040312
com.fishkaster.app:attr/listPreferredItemPaddingLeft = 0x7f040302
com.fishkaster.app:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1400db
com.fishkaster.app:attr/triggerSlack = 0x7f04050f
com.fishkaster.app:color/material_on_primary_emphasis_high_type = 0x7f060298
com.fishkaster.app:attr/materialCalendarDay = 0x7f040319
com.fishkaster.app:attr/materialAlertDialogButtonSpacerVisibility = 0x7f040311
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f140174
com.fishkaster.app:attr/icon = 0x7f040263
com.fishkaster.app:dimen/m3_navigation_item_vertical_padding = 0x7f0701ec
com.fishkaster.app:attr/mapType = 0x7f04030b
com.fishkaster.app:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.fishkaster.app:id/material_clock_period_toggle = 0x7f0a0159
com.fishkaster.app:color/switch_thumb_disabled_material_dark = 0x7f060326
com.fishkaster.app:attr/triggerId = 0x7f04050d
com.fishkaster.app:attr/textAppearancePopupMenuHeader = 0x7f0404a9
com.fishkaster.app:attr/logo = 0x7f040306
com.fishkaster.app:attr/cornerShape = 0x7f040157
com.fishkaster.app:id/expand_activities_button = 0x7f0a00fc
com.fishkaster.app:attr/liteMode = 0x7f040305
com.fishkaster.app:id/end_time_text = 0x7f0a00c6
com.fishkaster.app:color/expoCropToolbarActionTextColor = 0x7f06007e
com.fishkaster.app:attr/buttonBarPositiveButtonStyle = 0x7f040099
com.fishkaster.app:dimen/abc_dialog_padding_material = 0x7f070024
com.fishkaster.app:styleable/ImageFilterView = 0x7f150041
com.fishkaster.app:attr/listPreferredItemPaddingStart = 0x7f040304
com.fishkaster.app:color/m3_ref_palette_neutral96 = 0x7f06013d
com.fishkaster.app:attr/curveFit = 0x7f04018f
com.fishkaster.app:style/TextAppearance.Design.Counter.Overflow = 0x7f1401fc
com.fishkaster.app:attr/listPreferredItemPaddingRight = 0x7f040303
com.fishkaster.app:attr/contentInsetStart = 0x7f040142
com.fishkaster.app:attr/logoScaleType = 0x7f040309
com.fishkaster.app:attr/listLayout = 0x7f0402fb
com.fishkaster.app:attr/collapsedTitleTextAppearance = 0x7f0400f0
com.fishkaster.app:attr/lineSpacing = 0x7f0402f4
com.fishkaster.app:attr/homeAsUpIndicator = 0x7f04025e
com.fishkaster.app:attr/limitBoundsTo = 0x7f0402f2
com.fishkaster.app:attr/switchTextAppearance = 0x7f040469
com.fishkaster.app:attr/nestedScrollViewStyle = 0x7f040388
com.fishkaster.app:attr/layout_scrollFlags = 0x7f0402ed
com.fishkaster.app:macro/m3_comp_checkbox_selected_container_color = 0x7f0e0006
com.fishkaster.app:layout/material_clock_period_toggle = 0x7f0d004d
com.fishkaster.app:attr/buttonBarNegativeButtonStyle = 0x7f040097
com.fishkaster.app:attr/popEnterAnim = 0x7f0403b9
com.fishkaster.app:attr/menu = 0x7f04034a
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f140044
com.fishkaster.app:attr/layout_goneMarginEnd = 0x7f0402e4
com.fishkaster.app:id/collapseActionView = 0x7f0a008c
com.fishkaster.app:attr/rippleColor = 0x7f0403e1
com.fishkaster.app:attr/layout_editor_absoluteY = 0x7f0402e2
com.fishkaster.app:attr/layout_scrollInterpolator = 0x7f0402ee
com.fishkaster.app:attr/motionEasingStandardAccelerateInterpolator = 0x7f040373
com.fishkaster.app:color/mtrl_btn_text_color_selector = 0x7f0602e3
com.fishkaster.app:attr/layout_constraintHorizontal_chainStyle = 0x7f0402cb
com.fishkaster.app:attr/errorAccessibilityLabel = 0x7f0401df
com.fishkaster.app:color/design_default_color_on_background = 0x7f060053
com.fishkaster.app:attr/titleCentered = 0x7f0404de
com.fishkaster.app:attr/layout_constraintWidth_min = 0x7f0402de
com.fishkaster.app:attr/layout_constraintWidth_default = 0x7f0402dc
com.fishkaster.app:id/parent = 0x7f0a01b3
com.fishkaster.app:attr/layout_constraintVertical_chainStyle = 0x7f0402da
com.fishkaster.app:attr/layout_constraintTop_toTopOf = 0x7f0402d8
com.fishkaster.app:integer/mtrl_switch_thumb_motion_duration = 0x7f0b0039
com.fishkaster.app:attr/layout_constraintTop_creator = 0x7f0402d6
com.fishkaster.app:string/exo_track_role_closed_captions = 0x7f130097
com.fishkaster.app:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08003e
com.fishkaster.app:id/sin = 0x7f0a01fd
com.fishkaster.app:id/noScroll = 0x7f0a0195
com.fishkaster.app:dimen/m3_navigation_rail_default_width = 0x7f0701ef
com.fishkaster.app:attr/layout_constraintHorizontal_weight = 0x7f0402cc
com.fishkaster.app:dimen/m3_btn_icon_btn_padding_right = 0x7f0700f8
com.fishkaster.app:dimen/exo_icon_size = 0x7f07009b
com.fishkaster.app:attr/layout_constraintHorizontal_bias = 0x7f0402ca
com.fishkaster.app:attr/fontProviderFallbackQuery = 0x7f04023a
com.fishkaster.app:dimen/material_cursor_width = 0x7f070257
com.fishkaster.app:style/Base.V7.Theme.AppCompat = 0x7f1400bd
com.fishkaster.app:drawable/abc_list_pressed_holo_light = 0x7f080051
com.fishkaster.app:attr/layout_constraintHeight_max = 0x7f0402c7
com.fishkaster.app:layout/material_clockface_view = 0x7f0d0050
com.fishkaster.app:attr/ad_marker_color = 0x7f04002a
com.fishkaster.app:attr/prefixText = 0x7f0403c2
com.fishkaster.app:attr/statusBarForeground = 0x7f040450
com.fishkaster.app:drawable/notify_panel_notification_icon_bg = 0x7f08015e
com.fishkaster.app:attr/layout_constraintDimensionRatio = 0x7f0402c0
com.fishkaster.app:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0701cd
com.fishkaster.app:string/abc_searchview_description_query = 0x7f130014
com.fishkaster.app:attr/dayTodayStyle = 0x7f04019e
com.fishkaster.app:color/expoCropBackButtonIconColor = 0x7f06007c
com.fishkaster.app:attr/auto_show = 0x7f04004b
com.fishkaster.app:dimen/m3_bottomappbar_horizontal_padding = 0x7f0700f0
com.fishkaster.app:attr/statusBarScrim = 0x7f040451
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0600ec
com.fishkaster.app:attr/layout_constraintCircle = 0x7f0402bd
com.fishkaster.app:id/spline = 0x7f0a0209
com.fishkaster.app:color/material_personalized_color_control_normal = 0x7f0602a3
com.fishkaster.app:attr/layout_constraintBottom_toBottomOf = 0x7f0402bb
com.fishkaster.app:dimen/mtrl_toolbar_default_height = 0x7f07032b
com.fishkaster.app:attr/fontProviderPackage = 0x7f04023d
com.fishkaster.app:attr/layout_collapseParallaxMultiplier = 0x7f0402b5
com.fishkaster.app:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0700d8
com.fishkaster.app:id/fillCenter = 0x7f0a0101
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600ed
com.fishkaster.app:style/ShapeAppearance.Material3.Corner.Full = 0x7f14019b
com.fishkaster.app:dimen/m3_fab_translation_z_pressed = 0x7f0701de
com.fishkaster.app:attr/layout_anchorGravity = 0x7f0402b2
com.fishkaster.app:attr/layout_anchor = 0x7f0402b1
com.fishkaster.app:attr/colorOnPrimaryFixed = 0x7f040108
com.fishkaster.app:attr/layoutManager = 0x7f0402b0
com.fishkaster.app:id/autofill_inline_suggestion_start_icon = 0x7f0a0067
com.fishkaster.app:color/accent_material_dark = 0x7f060019
com.fishkaster.app:attr/autofillInlineSuggestionTitle = 0x7f040050
com.fishkaster.app:dimen/mtrl_btn_dialog_btn_min_width = 0x7f070280
com.fishkaster.app:anim/abc_slide_in_bottom = 0x7f010006
com.fishkaster.app:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f080020
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f14003d
com.fishkaster.app:attr/lStar = 0x7f0402a1
com.fishkaster.app:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f14015c
com.fishkaster.app:attr/keyboardIcon = 0x7f04029f
com.fishkaster.app:color/expoCropBackgroundColor = 0x7f06007d
com.fishkaster.app:attr/chipSpacing = 0x7f0400d3
com.fishkaster.app:attr/customDimension = 0x7f040193
com.fishkaster.app:string/common_google_play_services_update_title = 0x7f13005f
com.fishkaster.app:attr/buttonStyleSmall = 0x7f0400a4
com.fishkaster.app:attr/endIconMinSize = 0x7f0401d6
com.fishkaster.app:color/m3_tabs_ripple_color_secondary = 0x7f060223
com.fishkaster.app:color/material_dynamic_neutral0 = 0x7f060248
com.fishkaster.app:attr/itemShapeInsetTop = 0x7f040293
com.fishkaster.app:attr/cornerFamilyBottomLeft = 0x7f040152
com.fishkaster.app:attr/itemShapeInsetStart = 0x7f040292
com.fishkaster.app:style/ThemeOverlay.Design.TextInputEditText = 0x7f1402bc
com.fishkaster.app:attr/alertDialogButtonGroupStyle = 0x7f04002d
com.fishkaster.app:integer/mtrl_view_invisible = 0x7f0b0043
com.fishkaster.app:attr/colorOnSecondaryContainer = 0x7f04010c
com.fishkaster.app:attr/itemPaddingBottom = 0x7f04028a
com.fishkaster.app:attr/itemIconTint = 0x7f040286
com.fishkaster.app:attr/warmth = 0x7f040528
com.fishkaster.app:attr/cropShowLabel = 0x7f040183
com.fishkaster.app:attr/textAppearanceSubtitle1 = 0x7f0404ad
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0e00fa
com.fishkaster.app:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070324
com.fishkaster.app:color/material_slider_active_track_color = 0x7f0602d3
com.fishkaster.app:style/Base.Theme.MaterialComponents = 0x7f140068
com.fishkaster.app:dimen/mtrl_btn_z = 0x7f070296
com.fishkaster.app:attr/itemIconPadding = 0x7f040284
com.fishkaster.app:dimen/notification_action_icon_size = 0x7f070332
com.fishkaster.app:color/m3_ref_palette_neutral4 = 0x7f060131
com.fishkaster.app:macro/m3_comp_fab_secondary_icon_color = 0x7f0e003c
com.fishkaster.app:anim/rns_slide_in_from_right = 0x7f010048
com.fishkaster.app:attr/popUpToInclusive = 0x7f0403bc
com.fishkaster.app:attr/motionDurationExtraLong2 = 0x7f04035b
com.fishkaster.app:attr/itemHorizontalTranslationEnabled = 0x7f040283
com.fishkaster.app:id/rn_redbox_stack = 0x7f0a01dc
com.fishkaster.app:anim/rns_default_enter_out = 0x7f010033
com.fishkaster.app:string/expo_splash_screen_status_bar_translucent = 0x7f1300a5
com.fishkaster.app:attr/fabSize = 0x7f04020a
com.fishkaster.app:integer/abc_config_activityShortDur = 0x7f0b0001
com.fishkaster.app:id/NO_DEBUG = 0x7f0a0009
com.fishkaster.app:attr/itemFillColor = 0x7f040281
com.fishkaster.app:attr/itemActiveIndicatorStyle = 0x7f04027f
com.fishkaster.app:styleable/Autofill.InlineSuggestion = 0x7f150015
com.fishkaster.app:anim/abc_slide_in_top = 0x7f010007
com.fishkaster.app:attr/backgroundInsetTop = 0x7f040058
com.fishkaster.app:color/design_default_color_error = 0x7f060052
com.fishkaster.app:style/Widget.Material3.ActionMode = 0x7f140383
com.fishkaster.app:style/Platform.Widget.AppCompat.Spinner = 0x7f14016d
com.fishkaster.app:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0600bd
com.fishkaster.app:attr/insetForeground = 0x7f040279
com.fishkaster.app:attr/popUpTo = 0x7f0403bb
com.fishkaster.app:animator/mtrl_card_state_list_anim = 0x7f020017
com.fishkaster.app:drawable/abc_list_longpressed_holo = 0x7f08004f
com.fishkaster.app:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700dd
com.fishkaster.app:attr/checkMarkTintMode = 0x7f0400bb
com.fishkaster.app:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f140347
com.fishkaster.app:attr/cropAspectRatioX = 0x7f040163
com.fishkaster.app:id/view_tree_saved_state_registry_owner = 0x7f0a0261
com.fishkaster.app:attr/initialActivityCount = 0x7f040278
com.fishkaster.app:attr/toolbarStyle = 0x7f0404ef
com.fishkaster.app:dimen/design_navigation_item_horizontal_padding = 0x7f07007b
com.fishkaster.app:color/m3_ref_palette_neutral_variant0 = 0x7f060140
com.fishkaster.app:attr/isLightTheme = 0x7f04027b
com.fishkaster.app:attr/thumbTintMode = 0x7f0404cd
com.fishkaster.app:attr/shouldRemoveExpandedCorners = 0x7f040413
com.fishkaster.app:attr/indicatorColor = 0x7f040272
com.fishkaster.app:layout/m3_auto_complete_simple_item = 0x7f0d0048
com.fishkaster.app:drawable/paused_in_debugger_dialog_background = 0x7f080160
com.fishkaster.app:attr/chipGroupStyle = 0x7f0400cb
com.fishkaster.app:attr/controller_layout_id = 0x7f04014e
com.fishkaster.app:attr/startIconTintMode = 0x7f040445
com.fishkaster.app:attr/materialCalendarMonthNavigationButton = 0x7f040324
com.fishkaster.app:attr/logoAdjustViewBounds = 0x7f040307
com.fishkaster.app:dimen/m3_comp_filter_chip_container_height = 0x7f070151
com.fishkaster.app:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f070180
com.fishkaster.app:color/design_dark_default_color_secondary = 0x7f06004e
com.fishkaster.app:attr/cropBorderLineThickness = 0x7f04016c
com.fishkaster.app:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080052
com.fishkaster.app:dimen/design_fab_translation_z_hovered_focused = 0x7f070076
com.fishkaster.app:drawable/exo_notification_stop = 0x7f0800dc
com.fishkaster.app:color/m3_sys_color_dark_error = 0x7f060181
com.fishkaster.app:attr/horizontalOffset = 0x7f040260
com.fishkaster.app:style/Widget.Material3.Chip.Suggestion = 0x7f1403b3
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral12 = 0x7f0600c7
com.fishkaster.app:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0e0108
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_primary = 0x7f0601ab
com.fishkaster.app:color/material_grey_100 = 0x7f060289
com.fishkaster.app:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f070153
com.fishkaster.app:attr/hintTextAppearance = 0x7f04025c
com.fishkaster.app:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f0701bc
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f140029
com.fishkaster.app:string/navigation_menu = 0x7f130122
com.fishkaster.app:id/rn_frame_file = 0x7f0a01d4
com.fishkaster.app:anim/rns_default_exit_in = 0x7f010034
com.fishkaster.app:attr/progressBarPadding = 0x7f0403cb
com.fishkaster.app:attr/chainUseRtl = 0x7f0400b8
com.fishkaster.app:macro/m3_comp_checkbox_selected_icon_color = 0x7f0e000b
com.fishkaster.app:attr/behavior_expandedOffset = 0x7f040078
com.fishkaster.app:attr/helperTextTextColor = 0x7f040252
com.fishkaster.app:dimen/m3_fab_translation_z_hovered_focused = 0x7f0701dd
com.fishkaster.app:drawable/ic_call_answer_video_low = 0x7f0800ff
com.fishkaster.app:attr/badgeShapeAppearanceOverlay = 0x7f040062
com.fishkaster.app:attr/bottomSheetStyle = 0x7f040088
com.fishkaster.app:attr/floatingActionButtonLargeStyle = 0x7f040216
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary80 = 0x7f060117
com.fishkaster.app:attr/actionBarSplitStyle = 0x7f040005
com.fishkaster.app:attr/boxCornerRadiusBottomStart = 0x7f04008d
com.fishkaster.app:attr/tabTextAppearance = 0x7f040484
com.fishkaster.app:attr/flow_firstHorizontalStyle = 0x7f040224
com.fishkaster.app:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.fishkaster.app:attr/colorTertiaryContainer = 0x7f040132
com.fishkaster.app:attr/thumbTrackGapSize = 0x7f0404ce
com.fishkaster.app:drawable/exo_icon_vr = 0x7f0800d4
com.fishkaster.app:dimen/m3_comp_navigation_rail_container_elevation = 0x7f07016d
com.fishkaster.app:color/material_dynamic_secondary70 = 0x7f060277
com.fishkaster.app:attr/windowFixedHeightMinor = 0x7f040532
com.fishkaster.app:attr/actionModeSplitBackground = 0x7f04001c
com.fishkaster.app:style/Base.V26.Theme.AppCompat.Light = 0x7f1400b9
com.fishkaster.app:attr/labelVisibilityMode = 0x7f0402a4
com.fishkaster.app:anim/rns_ios_from_right_background_open = 0x7f01003f
com.fishkaster.app:drawable/paused_in_debugger_background = 0x7f08015f
com.fishkaster.app:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.fishkaster.app:macro/m3_comp_search_view_container_color = 0x7f0e00f1
com.fishkaster.app:attr/textAppearanceTitleMedium = 0x7f0404b0
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0e0160
com.fishkaster.app:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f070181
com.fishkaster.app:attr/horizontalOffsetWithText = 0x7f040261
com.fishkaster.app:drawable/exo_controls_pause = 0x7f0800a9
com.fishkaster.app:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f07016a
com.fishkaster.app:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f0601b8
com.fishkaster.app:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f07016f
com.fishkaster.app:string/exo_controls_custom_playback_speed = 0x7f130071
com.fishkaster.app:attr/forceDefaultNavigationOnClickListener = 0x7f040244
com.fishkaster.app:attr/autoCompleteTextViewStyle = 0x7f040043
com.fishkaster.app:style/Widget.MaterialComponents.ShapeableImageView = 0x7f140487
com.fishkaster.app:attr/forceApplySystemWindowInsetTop = 0x7f040243
com.fishkaster.app:dimen/mtrl_btn_inset = 0x7f070288
com.fishkaster.app:color/error_color_material_dark = 0x7f060072
com.fishkaster.app:attr/clockFaceBackgroundColor = 0x7f0400e0
com.fishkaster.app:styleable/ThemeEnforcement = 0x7f1500a0
com.fishkaster.app:attr/uiMapToolbar = 0x7f040512
com.fishkaster.app:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f08000e
com.fishkaster.app:string/exo_controls_seek_bar_description = 0x7f130081
com.fishkaster.app:attr/maxLines = 0x7f040345
com.fishkaster.app:styleable/ConstraintSet = 0x7f15002c
com.fishkaster.app:dimen/abc_action_bar_default_height_material = 0x7f070002
com.fishkaster.app:drawable/ic_keyboard_black_24dp = 0x7f080107
com.fishkaster.app:string/mtrl_picker_date_header_title = 0x7f1300fb
com.fishkaster.app:color/m3_ref_palette_primary90 = 0x7f060157
com.fishkaster.app:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1403ec
com.fishkaster.app:id/header_title = 0x7f0a0122
com.fishkaster.app:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f140066
com.fishkaster.app:attr/drawerLayoutStyle = 0x7f0401c4
com.fishkaster.app:style/SpinnerDatePickerStyle = 0x7f1401bf
com.fishkaster.app:color/browser_actions_bg_grey = 0x7f060027
com.fishkaster.app:attr/use_controller = 0x7f040520
com.fishkaster.app:dimen/m3_chip_icon_size = 0x7f07011f
com.fishkaster.app:attr/uiZoomGestures = 0x7f040518
com.fishkaster.app:layout/notification_action_tombstone = 0x7f0d0079
com.fishkaster.app:anim/rns_ios_from_left_background_close = 0x7f01003a
com.fishkaster.app:animator/fragment_open_enter = 0x7f020007
com.fishkaster.app:attr/trackColor = 0x7f0404fb
com.fishkaster.app:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080047
com.fishkaster.app:attr/fontProviderCerts = 0x7f040239
com.fishkaster.app:attr/fontFamily = 0x7f040237
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f14046b
com.fishkaster.app:attr/counterOverflowTextColor = 0x7f040160
com.fishkaster.app:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700c3
com.fishkaster.app:attr/flow_maxElementsWrap = 0x7f04022f
com.fishkaster.app:string/exo_controls_stop_description = 0x7f130086
com.fishkaster.app:id/chronometer = 0x7f0a0085
com.fishkaster.app:attr/scaleType = 0x7f0403f4
com.fishkaster.app:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1402d9
com.fishkaster.app:id/exo_position = 0x7f0a00ea
com.fishkaster.app:animator/design_appbar_state_list_animator = 0x7f020000
com.fishkaster.app:attr/flow_lastHorizontalStyle = 0x7f04022c
com.fishkaster.app:id/exo_audio_track = 0x7f0a00cd
com.fishkaster.app:dimen/mtrl_tooltip_arrowSize = 0x7f07032c
com.fishkaster.app:layout/mtrl_layout_snackbar = 0x7f0d0069
com.fishkaster.app:id/staticLayout = 0x7f0a0216
com.fishkaster.app:attr/chipStartPadding = 0x7f0400d7
com.fishkaster.app:styleable/BaseProgressIndicator = 0x7f150017
com.fishkaster.app:color/m3_ref_palette_neutral98 = 0x7f06013e
com.fishkaster.app:color/m3_ref_palette_neutral40 = 0x7f060132
com.fishkaster.app:attr/flow_firstHorizontalBias = 0x7f040223
com.fishkaster.app:attr/route = 0x7f0403f2
com.fishkaster.app:attr/sliderStyle = 0x7f040432
com.fishkaster.app:attr/destination = 0x7f0401a8
com.fishkaster.app:id/accessibility_custom_action_10 = 0x7f0a0018
com.fishkaster.app:attr/floatingActionButtonTertiaryStyle = 0x7f040222
com.fishkaster.app:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401fc
com.fishkaster.app:id/accessibility_custom_action_2 = 0x7f0a0022
com.fishkaster.app:attr/errorIconTintMode = 0x7f0401e5
com.fishkaster.app:dimen/mtrl_textinput_counter_margin_start = 0x7f070327
com.fishkaster.app:anim/linear_indeterminate_line2_head_interpolator = 0x7f010025
com.fishkaster.app:styleable/Chip = 0x7f150020
com.fishkaster.app:attr/trackInsideCornerSize = 0x7f040503
com.fishkaster.app:attr/floatingActionButtonSurfaceStyle = 0x7f040221
com.fishkaster.app:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0e009a
com.fishkaster.app:string/tablist_description = 0x7f13014a
com.fishkaster.app:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f140298
com.fishkaster.app:id/motion_base = 0x7f0a0173
com.fishkaster.app:attr/floatingActionButtonLargeTertiaryStyle = 0x7f040218
com.fishkaster.app:drawable/ic_search_black_24 = 0x7f080113
com.fishkaster.app:attr/layout_constraintHeight_default = 0x7f0402c6
com.fishkaster.app:drawable/material_ic_calendar_black_24dp = 0x7f080123
com.fishkaster.app:attr/shapeAppearanceCornerExtraLarge = 0x7f040408
com.fishkaster.app:attr/layout_constraintHeight_min = 0x7f0402c8
com.fishkaster.app:dimen/m3_comp_divider_thickness = 0x7f07012d
com.fishkaster.app:attr/multiChoiceItemLayout = 0x7f04037f
com.fishkaster.app:color/mtrl_calendar_selected_range = 0x7f0602e6
com.fishkaster.app:anim/rns_ios_from_left_foreground_open = 0x7f01003d
com.fishkaster.app:color/m3_sys_color_dark_outline_variant = 0x7f060192
com.fishkaster.app:id/month_navigation_bar = 0x7f0a016e
com.fishkaster.app:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f070124
com.fishkaster.app:attr/firstBaselineToTopHeight = 0x7f040213
com.fishkaster.app:attr/fastScrollVerticalTrackDrawable = 0x7f040212
com.fishkaster.app:attr/navigationMode = 0x7f040384
com.fishkaster.app:macro/m3_comp_navigation_bar_label_text_type = 0x7f0e0077
com.fishkaster.app:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0e002f
com.fishkaster.app:attr/fadeDuration = 0x7f04020b
com.fishkaster.app:drawable/ic_call_answer = 0x7f0800fc
com.fishkaster.app:attr/behavior_overlapTop = 0x7f04007c
com.fishkaster.app:id/original_focusability = 0x7f0a01ad
com.fishkaster.app:dimen/highlight_alpha_material_colored = 0x7f0700b9
com.fishkaster.app:attr/paddingTopSystemWindowInsets = 0x7f04039f
com.fishkaster.app:color/m3_ref_palette_secondary10 = 0x7f06015b
com.fishkaster.app:attr/snackbarStyle = 0x7f040434
com.fishkaster.app:id/barrier = 0x7f0a006a
com.fishkaster.app:attr/dynamicColorThemeOverlay = 0x7f0401c9
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0e0100
com.fishkaster.app:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401ff
com.fishkaster.app:font/inter_medium = 0x7f090001
com.fishkaster.app:attr/extendMotionSpec = 0x7f0401fa
com.fishkaster.app:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0702b8
com.fishkaster.app:dimen/browser_actions_context_menu_min_padding = 0x7f070054
com.fishkaster.app:color/m3_sys_color_dark_surface = 0x7f060197
com.fishkaster.app:attr/expoCropToolbarIconColor = 0x7f0401f9
com.fishkaster.app:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0b0024
com.fishkaster.app:layout/mtrl_calendar_day = 0x7f0d005f
com.fishkaster.app:attr/expoCropToolbarColor = 0x7f0401f8
com.fishkaster.app:id/scrollable = 0x7f0a01ea
com.fishkaster.app:style/Theme.Material3.Light.Dialog = 0x7f14027b
com.fishkaster.app:attr/expoCropToolbarActionTextColor = 0x7f0401f7
com.fishkaster.app:id/mtrl_card_checked_layer_id = 0x7f0a017d
com.fishkaster.app:id/cradle = 0x7f0a009a
com.fishkaster.app:attr/fabCradleVerticalOffset = 0x7f040208
com.fishkaster.app:dimen/clock_face_margin_start = 0x7f070058
com.fishkaster.app:attr/windowFixedHeightMajor = 0x7f040531
com.fishkaster.app:attr/expandedTitleTextColor = 0x7f0401f4
com.fishkaster.app:id/fit = 0x7f0a0108
com.fishkaster.app:attr/latLngBoundsSouthWestLongitude = 0x7f0402ab
com.fishkaster.app:attr/textAppearanceListItemSmall = 0x7f0404a7
com.fishkaster.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f14017a
com.fishkaster.app:drawable/mtrl_ic_checkbox_checked = 0x7f08013d
com.fishkaster.app:anim/m3_bottom_sheet_slide_in = 0x7f010027
com.fishkaster.app:color/foreground_material_dark = 0x7f060081
com.fishkaster.app:attr/fabCradleMargin = 0x7f040206
com.fishkaster.app:color/design_default_color_on_surface = 0x7f060057
com.fishkaster.app:styleable/RadialViewGroup = 0x7f150080
com.fishkaster.app:attr/homeLayout = 0x7f04025f
com.fishkaster.app:string/exo_controls_overflow_show_description = 0x7f130078
com.fishkaster.app:attr/seekBarStyle = 0x7f040402
com.fishkaster.app:attr/expandedTitleMargin = 0x7f0401ee
com.fishkaster.app:attr/motionDurationLong1 = 0x7f04035e
com.fishkaster.app:attr/expandedHintEnabled = 0x7f0401ec
com.fishkaster.app:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0602e0
com.fishkaster.app:attr/roundTopRight = 0x7f0403eb
com.fishkaster.app:dimen/mtrl_progress_circular_inset_medium = 0x7f0702fc
com.fishkaster.app:id/tag_on_receive_content_listener = 0x7f0a0224
com.fishkaster.app:drawable/settings = 0x7f08016a
com.fishkaster.app:attr/customPixelDimension = 0x7f040197
com.fishkaster.app:attr/badgeGravity = 0x7f04005e
com.fishkaster.app:attr/layout_goneMarginRight = 0x7f0402e6
com.fishkaster.app:attr/enterAnim = 0x7f0401de
com.fishkaster.app:dimen/mtrl_calendar_year_corner = 0x7f0702bc
com.fishkaster.app:attr/ensureMinTouchTargetSize = 0x7f0401dd
com.fishkaster.app:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f140252
com.fishkaster.app:string/exo_download_notification_channel_name = 0x7f13008d
com.fishkaster.app:attr/titleMargins = 0x7f0404e6
com.fishkaster.app:attr/backHandlingEnabled = 0x7f040051
com.fishkaster.app:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f14004b
com.fishkaster.app:color/m3_ref_palette_secondary20 = 0x7f06015d
com.fishkaster.app:dimen/mtrl_extended_fab_translation_z_base = 0x7f0702d9
com.fishkaster.app:attr/roundingBorderPadding = 0x7f0403f0
com.fishkaster.app:attr/expandedTitleMarginBottom = 0x7f0401ef
com.fishkaster.app:attr/alertDialogTheme = 0x7f040030
com.fishkaster.app:attr/enableEdgeToEdge = 0x7f0401d2
com.fishkaster.app:style/Base.Widget.Material3.CollapsingToolbar = 0x7f140105
com.fishkaster.app:attr/elevationOverlayEnabled = 0x7f0401d0
com.fishkaster.app:attr/fabAlignmentMode = 0x7f040202
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f140031
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0e00ff
com.fishkaster.app:attr/editTextColor = 0x7f0401cb
com.fishkaster.app:id/exo_main_text = 0x7f0a00df
com.fishkaster.app:attr/itemStrokeColor = 0x7f040295
com.fishkaster.app:dimen/mtrl_alert_dialog_background_inset_start = 0x7f07026e
com.fishkaster.app:attr/badgeTextColor = 0x7f040066
com.fishkaster.app:attr/dropdownListPreferredItemHeight = 0x7f0401c7
com.fishkaster.app:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f07021e
com.fishkaster.app:attr/colorOnTertiaryFixed = 0x7f040114
com.fishkaster.app:attr/counterOverflowTextAppearance = 0x7f04015f
com.fishkaster.app:attr/borderWidth = 0x7f040081
com.fishkaster.app:attr/region_widthMoreThan = 0x7f0403d9
com.fishkaster.app:attr/buttonGravity = 0x7f04009c
com.fishkaster.app:id/compose_prefetch_scheduler = 0x7f0a008e
com.fishkaster.app:color/m3_sys_color_light_error_container = 0x7f0601f4
com.fishkaster.app:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0c000c
com.fishkaster.app:attr/motionEasingAccelerated = 0x7f04036a
com.fishkaster.app:attr/deriveConstraintsFrom = 0x7f0401a7
com.fishkaster.app:id/month_navigation_previous = 0x7f0a0171
com.fishkaster.app:attr/fontProviderAuthority = 0x7f040238
com.fishkaster.app:attr/alpha = 0x7f040032
com.fishkaster.app:string/mtrl_checkbox_button_path_group_name = 0x7f1300ea
com.fishkaster.app:color/material_dynamic_tertiary10 = 0x7f06027d
com.fishkaster.app:color/abc_primary_text_material_light = 0x7f06000c
com.fishkaster.app:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0702db
com.fishkaster.app:id/uniform = 0x7f0a0254
com.fishkaster.app:integer/design_tab_indicator_anim_duration_ms = 0x7f0b0007
com.fishkaster.app:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f08000c
com.fishkaster.app:styleable/Variant = 0x7f1500a5
com.fishkaster.app:string/mtrl_exceed_max_badge_number_content_description = 0x7f1300f1
com.fishkaster.app:macro/m3_comp_text_button_label_text_type = 0x7f0e0145
com.fishkaster.app:attr/drawPath = 0x7f0401b8
com.fishkaster.app:attr/dividerVertical = 0x7f0401b4
com.fishkaster.app:drawable/redbox_top_border_background = 0x7f080163
com.fishkaster.app:color/cardview_shadow_end_color = 0x7f060031
com.fishkaster.app:attr/autofillInlineSuggestionSubtitle = 0x7f04004f
com.fishkaster.app:attr/colorOnSecondary = 0x7f04010b
com.fishkaster.app:attr/contrast = 0x7f04014c
com.fishkaster.app:color/m3_ref_palette_dynamic_tertiary95 = 0x7f060119
com.fishkaster.app:attr/placeholderImageScaleType = 0x7f0403b1
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f140472
com.fishkaster.app:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0e0101
com.fishkaster.app:color/m3_checkbox_button_icon_tint = 0x7f060094
com.fishkaster.app:anim/abc_popup_enter = 0x7f010003
com.fishkaster.app:anim/m3_motion_fade_enter = 0x7f010029
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f0601be
com.fishkaster.app:attr/dividerInsetEnd = 0x7f0401b0
com.fishkaster.app:style/ExoStyledControls.Button.Center.PlayPause = 0x7f140142
com.fishkaster.app:id/buttonPanel = 0x7f0a0077
com.fishkaster.app:attr/materialDividerStyle = 0x7f040330
com.fishkaster.app:attr/chipCornerRadius = 0x7f0400c9
com.fishkaster.app:dimen/design_bottom_navigation_active_item_max_width = 0x7f070062
com.fishkaster.app:attr/displayOptions = 0x7f0401ac
com.fishkaster.app:attr/dialogPreferredPadding = 0x7f0401aa
com.fishkaster.app:color/design_dark_default_color_surface = 0x7f060050
com.fishkaster.app:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.fishkaster.app:id/item_touch_helper_previous_elevation = 0x7f0a013f
com.fishkaster.app:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.fishkaster.app:attr/divider = 0x7f0401ad
com.fishkaster.app:attr/dialogCornerRadius = 0x7f0401a9
com.fishkaster.app:color/m3_ref_palette_neutral20 = 0x7f06012d
com.fishkaster.app:attr/customColorDrawableValue = 0x7f040191
com.fishkaster.app:attr/actionModeShareDrawable = 0x7f04001b
com.fishkaster.app:dimen/m3_extended_fab_top_padding = 0x7f0701da
com.fishkaster.app:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1400d3
com.fishkaster.app:attr/iconifiedByDefault = 0x7f04026b
com.fishkaster.app:attr/default_artwork = 0x7f0401a4
com.fishkaster.app:dimen/m3_side_sheet_standard_elevation = 0x7f07020d
com.fishkaster.app:drawable/exo_controls_shuffle_off = 0x7f0800b0
com.fishkaster.app:attr/tintNavigationIcon = 0x7f0404dc
com.fishkaster.app:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.fishkaster.app:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f150035
com.fishkaster.app:attr/daySelectedStyle = 0x7f04019c
com.fishkaster.app:attr/contentInsetRight = 0x7f040141
com.fishkaster.app:attr/layout_optimizationLevel = 0x7f0402eb
com.fishkaster.app:attr/dataPattern = 0x7f04019a
com.fishkaster.app:id/action_bar_spinner = 0x7f0a0044
com.fishkaster.app:attr/constraints = 0x7f04013b
com.fishkaster.app:layout/design_navigation_item = 0x7f0d002d
com.fishkaster.app:attr/cropMaxCropResultWidthPX = 0x7f040178
com.fishkaster.app:color/mtrl_btn_transparent_bg_color = 0x7f0602e4
com.fishkaster.app:attr/action = 0x7f040000
com.fishkaster.app:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1400be
com.fishkaster.app:drawable/exo_styled_controls_fullscreen_enter = 0x7f0800e1
com.fishkaster.app:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f0601bc
com.fishkaster.app:attr/layout_constrainedHeight = 0x7f0402b6
com.fishkaster.app:attr/errorTextAppearance = 0x7f0401e7
com.fishkaster.app:layout/design_bottom_sheet_dialog = 0x7f0d0027
com.fishkaster.app:color/m3_radiobutton_button_tint = 0x7f0600c1
com.fishkaster.app:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1403fa
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f1401ac
com.fishkaster.app:string/mtrl_picker_end_date_description = 0x7f1300fe
com.fishkaster.app:attr/queryHint = 0x7f0403ce
com.fishkaster.app:id/middle = 0x7f0a016a
com.fishkaster.app:string/mtrl_picker_announce_current_selection_none = 0x7f1300f7
com.fishkaster.app:attr/region_widthLessThan = 0x7f0403d8
com.fishkaster.app:attr/boxCornerRadiusTopStart = 0x7f04008f
com.fishkaster.app:attr/customIntegerValue = 0x7f040195
com.fishkaster.app:string/not_selected = 0x7f130123
com.fishkaster.app:string/catalyst_heap_capture = 0x7f13003b
com.fishkaster.app:attr/content = 0x7f04013c
com.fishkaster.app:style/Widget.Material3.BottomSheet = 0x7f140391
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1401bc
com.fishkaster.app:attr/yearStyle = 0x7f040539
com.fishkaster.app:style/ExoStyledControls.Button.Bottom = 0x7f140134
com.fishkaster.app:attr/colorOnTertiaryFixedVariant = 0x7f040115
com.fishkaster.app:anim/m3_side_sheet_enter_from_left = 0x7f01002b
com.fishkaster.app:attr/currentState = 0x7f04018c
com.fishkaster.app:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f140493
com.fishkaster.app:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f070135
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f140039
com.fishkaster.app:attr/cameraBearing = 0x7f0400a7
com.fishkaster.app:styleable/Toolbar = 0x7f1500a1
com.fishkaster.app:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f14048b
com.fishkaster.app:attr/textAppearanceLabelMedium = 0x7f0404a1
com.fishkaster.app:id/text_input_error_icon = 0x7f0a0235
com.fishkaster.app:attr/cropShowProgressBar = 0x7f040184
com.fishkaster.app:dimen/m3_btn_disabled_translation_z = 0x7f0700f4
com.fishkaster.app:color/material_personalized_color_on_surface_variant = 0x7f0602af
com.fishkaster.app:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f080060
com.fishkaster.app:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f140168
com.fishkaster.app:attr/restoreState = 0x7f0403dd
com.fishkaster.app:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0702ec
com.fishkaster.app:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0e00cb
com.fishkaster.app:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.fishkaster.app:attr/itemShapeAppearanceOverlay = 0x7f04028e
com.fishkaster.app:attr/editTextBackground = 0x7f0401ca
com.fishkaster.app:animator/fragment_fade_enter = 0x7f020005
com.fishkaster.app:attr/cropMultiTouchEnabled = 0x7f04017e
com.fishkaster.app:attr/cropMinCropWindowWidth = 0x7f04017d
com.fishkaster.app:attr/itemShapeInsetBottom = 0x7f040290
com.fishkaster.app:drawable/exo_icon_stop = 0x7f0800d3
com.fishkaster.app:attr/cropMaxZoom = 0x7f040179
com.fishkaster.app:attr/thumbIcon = 0x7f0404c4
com.fishkaster.app:attr/textAppearanceSearchResultTitle = 0x7f0404ab
com.fishkaster.app:attr/backgroundSplit = 0x7f04005a
com.fishkaster.app:drawable/abc_list_pressed_holo_dark = 0x7f080050
com.fishkaster.app:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f0700c0
com.fishkaster.app:attr/motionDurationMedium3 = 0x7f040364
com.fishkaster.app:color/mtrl_tabs_icon_color_selector = 0x7f06030a
com.fishkaster.app:id/mtrl_calendar_main_pane = 0x7f0a0178
com.fishkaster.app:attr/placeholderImage = 0x7f0403b0
com.fishkaster.app:macro/m3_comp_dialog_headline_type = 0x7f0e0025
com.fishkaster.app:attr/drawableBottomCompat = 0x7f0401b9
com.fishkaster.app:attr/cropGuidelines = 0x7f040173
com.fishkaster.app:attr/scrubber_drawable = 0x7f0403fc
com.fishkaster.app:attr/cropFlipVertically = 0x7f040172
com.fishkaster.app:attr/expoCropBackButtonIconColor = 0x7f0401f5
com.fishkaster.app:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0601e3
com.fishkaster.app:attr/cropFlipHorizontally = 0x7f040171
com.fishkaster.app:style/TextAppearance.MaterialComponents.Body2 = 0x7f14022b
com.fishkaster.app:drawable/common_google_signin_btn_icon_disabled = 0x7f08008e
com.fishkaster.app:attr/listPreferredItemHeightLarge = 0x7f0402ff
com.fishkaster.app:dimen/design_bottom_navigation_height = 0x7f070066
com.fishkaster.app:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f140436
com.fishkaster.app:dimen/m3_card_dragged_z = 0x7f07010b
com.fishkaster.app:style/Base.Animation.AppCompat.DropDownUp = 0x7f140011
com.fishkaster.app:color/mtrl_textinput_default_box_stroke_color = 0x7f06030f
com.fishkaster.app:string/bottomsheet_action_collapse = 0x7f130021
com.fishkaster.app:attr/colorPrimaryFixed = 0x7f04011b
com.fishkaster.app:color/primary_text_default_material_light = 0x7f06031c
com.fishkaster.app:attr/show_rewind_button = 0x7f040421
com.fishkaster.app:attr/css = 0x7f04018b
com.fishkaster.app:attr/cropAspectRatioY = 0x7f040164
com.fishkaster.app:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f07014d
com.fishkaster.app:attr/dividerHorizontal = 0x7f0401af
com.fishkaster.app:attr/materialDisplayDividerStyle = 0x7f04032e
com.fishkaster.app:attr/cameraTargetLat = 0x7f0400aa
com.fishkaster.app:attr/counterEnabled = 0x7f04015d
com.fishkaster.app:attr/sizePercent = 0x7f040431
com.fishkaster.app:color/m3_sys_color_secondary_fixed = 0x7f06021c
com.fishkaster.app:attr/layout_constraintStart_toEndOf = 0x7f0402d3
com.fishkaster.app:dimen/abc_text_size_small_material = 0x7f07004c
com.fishkaster.app:attr/actionBarStyle = 0x7f040006
com.fishkaster.app:attr/cornerSizeTopRight = 0x7f04015c
com.fishkaster.app:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1402d3
com.fishkaster.app:styleable/NavInclude = 0x7f150072
com.fishkaster.app:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f070168
com.fishkaster.app:attr/titleTextAppearance = 0x7f0404e8
com.fishkaster.app:styleable/ActionMenuItemView = 0x7f150002
com.fishkaster.app:attr/cornerSizeTopLeft = 0x7f04015b
com.fishkaster.app:attr/tabSelectedTextColor = 0x7f040482
com.fishkaster.app:macro/m3_comp_time_picker_container_shape = 0x7f0e014f
com.fishkaster.app:attr/indicatorTrackGapSize = 0x7f040277
com.fishkaster.app:style/TextAppearance.Design.Suffix = 0x7f140203
com.fishkaster.app:attr/actionBarTabBarStyle = 0x7f040007
com.fishkaster.app:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0e011f
com.fishkaster.app:color/bright_foreground_inverse_material_light = 0x7f060024
com.fishkaster.app:attr/cornerFamily = 0x7f040151
com.fishkaster.app:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f140059
com.fishkaster.app:drawable/abc_cab_background_top_mtrl_alpha = 0x7f080039
com.fishkaster.app:dimen/mtrl_calendar_action_height = 0x7f070298
com.fishkaster.app:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f07027d
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f0601b2
com.fishkaster.app:attr/centerIfNoTextEnabled = 0x7f0400b7
com.fishkaster.app:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0702ae
com.fishkaster.app:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1401d2
com.fishkaster.app:dimen/design_navigation_icon_padding = 0x7f070079
com.fishkaster.app:attr/duration = 0x7f0401c8
com.fishkaster.app:id/unchecked = 0x7f0a0253
com.fishkaster.app:color/m3_ref_palette_dynamic_primary0 = 0x7f0600f4
com.fishkaster.app:attr/contentPaddingTop = 0x7f04014a
com.fishkaster.app:attr/contentPaddingLeft = 0x7f040147
com.fishkaster.app:attr/actionLayout = 0x7f04000e
com.fishkaster.app:attr/contentInsetLeft = 0x7f040140
com.fishkaster.app:layout/abc_action_menu_layout = 0x7f0d0003
com.fishkaster.app:attr/contentPaddingEnd = 0x7f040146
com.fishkaster.app:attr/contentInsetStartWithNavigation = 0x7f040143
com.fishkaster.app:anim/design_bottom_sheet_slide_in = 0x7f01001e
com.fishkaster.app:drawable/mtrl_switch_track_decoration = 0x7f08014f
com.fishkaster.app:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f14045a
com.fishkaster.app:dimen/mtrl_progress_circular_size_small = 0x7f070302
com.fishkaster.app:drawable/btn_radio_off_mtrl = 0x7f080082
com.fishkaster.app:attr/state_liftable = 0x7f04044c
com.fishkaster.app:attr/maxHeight = 0x7f040343
com.fishkaster.app:id/shortcut = 0x7f0a01f9
com.fishkaster.app:color/material_blue_grey_900 = 0x7f06023a
com.fishkaster.app:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f1401a9
com.fishkaster.app:id/ALT = 0x7f0a0000
com.fishkaster.app:attr/layout_constraintEnd_toEndOf = 0x7f0402c1
com.fishkaster.app:attr/motionDurationShort1 = 0x7f040366
com.fishkaster.app:attr/colorSurfaceVariant = 0x7f04012f
com.fishkaster.app:attr/marginLeftSystemWindowInsets = 0x7f04030d
com.fishkaster.app:id/fillEnd = 0x7f0a0102
com.fishkaster.app:attr/dragScale = 0x7f0401b6
com.fishkaster.app:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f140457
com.fishkaster.app:attr/constraintSetEnd = 0x7f040138
com.fishkaster.app:attr/fabCradleRoundedCornerRadius = 0x7f040207
com.fishkaster.app:attr/colorTertiaryFixedDim = 0x7f040134
com.fishkaster.app:string/path_password_eye = 0x7f130125
com.fishkaster.app:attr/colorTertiary = 0x7f040131
com.fishkaster.app:id/search_voice_btn = 0x7f0a01f4
com.fishkaster.app:attr/state_with_icon = 0x7f04044e
com.fishkaster.app:attr/materialTimePickerTheme = 0x7f04033d
com.fishkaster.app:attr/colorSwitchThumbNormal = 0x7f040130
com.fishkaster.app:id/right = 0x7f0a01d0
com.fishkaster.app:color/bright_foreground_disabled_material_dark = 0x7f060021
com.fishkaster.app:attr/colorSurfaceInverse = 0x7f04012e
com.fishkaster.app:attr/shapeCornerFamily = 0x7f040411
com.fishkaster.app:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1402fc
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f140304
com.fishkaster.app:attr/badgeWithTextWidth = 0x7f04006e
com.fishkaster.app:attr/colorSurfaceContainerLow = 0x7f04012b
com.fishkaster.app:color/design_default_color_secondary = 0x7f06005b
com.fishkaster.app:color/abc_tint_default = 0x7f060014
com.fishkaster.app:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f1403bc
com.fishkaster.app:id/open_search_view_content_container = 0x7f0a01a2
com.fishkaster.app:dimen/m3_btn_icon_only_default_padding = 0x7f0700f9
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f140290
com.fishkaster.app:drawable/ic_call_answer_low = 0x7f0800fd
com.fishkaster.app:integer/m3_sys_motion_duration_medium2 = 0x7f0b001b
com.fishkaster.app:string/state_on = 0x7f130143
com.fishkaster.app:dimen/exo_icon_text_size = 0x7f07009c
com.fishkaster.app:id/transform = 0x7f0a0246
com.fishkaster.app:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f140187
com.fishkaster.app:attr/listPopupWindowStyle = 0x7f0402fd
com.fishkaster.app:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1402dc
com.fishkaster.app:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f14010f
com.fishkaster.app:style/Widget.Autofill.InlineSuggestionStartIconStyle = 0x7f140372
com.fishkaster.app:color/common_google_signin_btn_text_dark_pressed = 0x7f06003b
com.fishkaster.app:dimen/m3_comp_switch_disabled_track_opacity = 0x7f0701b7
com.fishkaster.app:anim/m3_side_sheet_exit_to_left = 0x7f01002d
com.fishkaster.app:style/Theme.Material3.DayNight = 0x7f14026b
com.fishkaster.app:id/accessibility_custom_action_12 = 0x7f0a001a
com.fishkaster.app:attr/colorSurfaceBright = 0x7f040127
com.fishkaster.app:attr/show_subtitle_button = 0x7f040423
com.fishkaster.app:attr/endIconTint = 0x7f0401d9
com.fishkaster.app:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0e0170
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1401ee
com.fishkaster.app:macro/m3_comp_bottom_app_bar_container_color = 0x7f0e0005
com.fishkaster.app:attr/cropCornerRadius = 0x7f04016f
com.fishkaster.app:id/hybrid = 0x7f0a012a
com.fishkaster.app:id/gone = 0x7f0a011d
com.fishkaster.app:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f070329
com.fishkaster.app:attr/cardBackgroundColor = 0x7f0400ae
com.fishkaster.app:attr/colorPrimaryFixedDim = 0x7f04011c
com.fishkaster.app:attr/barrierMargin = 0x7f040074
com.fishkaster.app:color/m3_ref_palette_primary40 = 0x7f060152
com.fishkaster.app:style/Widget.Material3.Chip.Input = 0x7f1403af
com.fishkaster.app:attr/panelMenuListWidth = 0x7f0403a2
com.fishkaster.app:attr/colorOutlineVariant = 0x7f040117
com.fishkaster.app:dimen/m3_comp_snackbar_container_elevation = 0x7f0701af
com.fishkaster.app:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f140194
com.fishkaster.app:color/common_google_signin_btn_text_light_pressed = 0x7f060040
com.fishkaster.app:attr/data = 0x7f040199
com.fishkaster.app:attr/activeIndicatorLabelPadding = 0x7f040025
com.fishkaster.app:style/Theme.MaterialComponents = 0x7f140281
com.fishkaster.app:dimen/abc_button_padding_vertical_material = 0x7f070015
com.fishkaster.app:color/m3_textfield_stroke_color = 0x7f06022d
com.fishkaster.app:id/browser_actions_menu_view = 0x7f0a0075
com.fishkaster.app:color/m3_sys_color_dynamic_light_secondary = 0x7f0601d9
com.fishkaster.app:string/mtrl_picker_range_header_title = 0x7f130109
com.fishkaster.app:attr/colorOnTertiary = 0x7f040112
com.fishkaster.app:attr/colorOnSurfaceVariant = 0x7f040111
com.fishkaster.app:attr/chipIcon = 0x7f0400cc
com.fishkaster.app:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.fishkaster.app:attr/alertDialogCenterButtons = 0x7f04002e
com.fishkaster.app:attr/implementationMode = 0x7f04026f
com.fishkaster.app:id/text_input_start_icon = 0x7f0a0236
com.fishkaster.app:anim/rns_ios_from_left_foreground_close = 0x7f01003c
com.fishkaster.app:attr/colorOnSurfaceInverse = 0x7f040110
com.fishkaster.app:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f08012e
com.fishkaster.app:attr/windowFixedWidthMinor = 0x7f040534
com.fishkaster.app:attr/textAppearanceHeadlineLarge = 0x7f04049d
com.fishkaster.app:string/common_signin_button_text = 0x7f130063
com.fishkaster.app:attr/verticalOffsetWithText = 0x7f040523
com.fishkaster.app:attr/bottomInsetScrimEnabled = 0x7f040084
com.fishkaster.app:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f14011f
com.fishkaster.app:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400f5
com.fishkaster.app:color/material_dynamic_secondary30 = 0x7f060273
com.fishkaster.app:id/transition_position = 0x7f0a024f
com.fishkaster.app:attr/collapsingToolbarLayoutMediumSize = 0x7f0400f4
com.fishkaster.app:id/accessibility_custom_action_8 = 0x7f0a0034
com.fishkaster.app:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400f3
com.fishkaster.app:attr/tabIconTint = 0x7f04046d
com.fishkaster.app:attr/dayStyle = 0x7f04019d
com.fishkaster.app:attr/subtitleTextColor = 0x7f04045d
com.fishkaster.app:id/exo_controller_placeholder = 0x7f0a00d5
com.fishkaster.app:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0e0034
com.fishkaster.app:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0701f7
com.fishkaster.app:id/textinput_suffix_text = 0x7f0a023c
com.fishkaster.app:string/mtrl_switch_thumb_group_name = 0x7f130118
com.fishkaster.app:attr/boxCollapsedPaddingTop = 0x7f04008b
com.fishkaster.app:attr/layout_constraintBottom_toTopOf = 0x7f0402bc
com.fishkaster.app:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f070231
com.fishkaster.app:attr/cropperLabelText = 0x7f040187
com.fishkaster.app:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0e00be
com.fishkaster.app:attr/collapsedSize = 0x7f0400ee
com.fishkaster.app:attr/boxCornerRadiusTopEnd = 0x7f04008e
com.fishkaster.app:string/abc_toolbar_collapse_description = 0x7f13001a
com.fishkaster.app:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080028
com.fishkaster.app:color/m3_ref_palette_black = 0x7f0600c3
com.fishkaster.app:id/material_minute_tv = 0x7f0a015e
com.fishkaster.app:drawable/exo_ic_default_album_image = 0x7f0800b8
com.fishkaster.app:attr/closeIconTint = 0x7f0400e9
com.fishkaster.app:attr/endIconScaleType = 0x7f0401d8
com.fishkaster.app:id/tag_on_apply_window_listener = 0x7f0a0223
com.fishkaster.app:attr/failureImage = 0x7f04020c
com.fishkaster.app:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f080147
com.fishkaster.app:attr/closeIconEnabled = 0x7f0400e5
com.fishkaster.app:drawable/common_google_signin_btn_icon_dark = 0x7f08008a
com.fishkaster.app:color/m3_dynamic_default_color_secondary_text = 0x7f0600a8
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f140293
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Caption = 0x7f14001d
com.fishkaster.app:string/material_hour_24h_suffix = 0x7f1300cd
com.fishkaster.app:id/FUNCTION = 0x7f0a0006
com.fishkaster.app:attr/floatingActionButtonSmallSurfaceStyle = 0x7f04021e
com.fishkaster.app:color/mtrl_scrim_color = 0x7f060304
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f140287
com.fishkaster.app:anim/rns_ios_from_left_background_open = 0x7f01003b
com.fishkaster.app:id/fill_horizontal = 0x7f0a0104
com.fishkaster.app:attr/layout_constrainedWidth = 0x7f0402b7
com.fishkaster.app:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.fishkaster.app:attr/chipSurfaceColor = 0x7f0400db
com.fishkaster.app:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0e0062
com.fishkaster.app:id/fixed_height = 0x7f0a0110
com.fishkaster.app:color/material_dynamic_neutral100 = 0x7f06024a
com.fishkaster.app:attr/behavior_fitToContents = 0x7f040079
com.fishkaster.app:attr/layout_constraintLeft_toLeftOf = 0x7f0402ce
com.fishkaster.app:integer/m3_sys_motion_duration_long2 = 0x7f0b0017
com.fishkaster.app:attr/chipStrokeWidth = 0x7f0400d9
com.fishkaster.app:attr/animate_relativeTo = 0x7f040038
com.fishkaster.app:string/expo_system_ui_user_interface_style = 0x7f1300a6
com.fishkaster.app:attr/hintEnabled = 0x7f04025b
com.fishkaster.app:attr/colorContainer = 0x7f0400fb
com.fishkaster.app:style/Widget.Material3.SearchBar.Outlined = 0x7f14040b
com.fishkaster.app:attr/helperText = 0x7f04024f
com.fishkaster.app:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.fishkaster.app:style/Theme.Material3.Light.NoActionBar = 0x7f14027f
com.fishkaster.app:attr/lastBaselineToBottomHeight = 0x7f0402a6
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f0601ae
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0e0078
com.fishkaster.app:attr/flow_verticalStyle = 0x7f040234
com.fishkaster.app:attr/cornerFamilyBottomRight = 0x7f040153
com.fishkaster.app:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0702c9
com.fishkaster.app:color/m3_switch_track_tint = 0x7f06017f
com.fishkaster.app:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0e00b9
com.fishkaster.app:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0e000a
com.fishkaster.app:dimen/abc_text_size_display_1_material = 0x7f070043
com.fishkaster.app:attr/badgeText = 0x7f040064
com.fishkaster.app:attr/nestedScrollFlags = 0x7f040387
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f140073
com.fishkaster.app:attr/customFloatValue = 0x7f040194
com.fishkaster.app:drawable/$avd_hide_password__2 = 0x7f080002
com.fishkaster.app:id/accessibility_custom_action_24 = 0x7f0a0027
com.fishkaster.app:attr/actionOverflowMenuStyle = 0x7f040021
com.fishkaster.app:attr/checkedButton = 0x7f0400bd
com.fishkaster.app:macro/m3_comp_slider_inactive_track_color = 0x7f0e0110
com.fishkaster.app:color/exo_styled_error_message_background = 0x7f060079
com.fishkaster.app:attr/actionOverflowButtonStyle = 0x7f040020
com.fishkaster.app:string/scrollbar_description = 0x7f130132
com.fishkaster.app:integer/mtrl_card_anim_delay_ms = 0x7f0b0036
com.fishkaster.app:macro/m3_comp_search_bar_input_text_type = 0x7f0e00ea
com.fishkaster.app:attr/checkMarkTint = 0x7f0400ba
com.fishkaster.app:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.fishkaster.app:styleable/StyledPlayerControlView = 0x7f150096
com.fishkaster.app:attr/buttonSize = 0x7f0400a2
com.fishkaster.app:drawable/abc_ic_voice_search_api_material = 0x7f080049
com.fishkaster.app:id/container = 0x7f0a0093
com.fishkaster.app:color/m3_sys_color_dark_on_primary = 0x7f060189
com.fishkaster.app:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0e00bd
com.fishkaster.app:anim/rns_no_animation_350 = 0x7f010044
com.fishkaster.app:attr/yearSelectedStyle = 0x7f040538
com.fishkaster.app:id/text2 = 0x7f0a022e
com.fishkaster.app:attr/cardPreventCornerOverlap = 0x7f0400b3
com.fishkaster.app:dimen/material_clock_period_toggle_vertical_gap = 0x7f070253
com.fishkaster.app:layout/mtrl_alert_dialog_actions = 0x7f0d0059
com.fishkaster.app:attr/cardCornerRadius = 0x7f0400af
com.fishkaster.app:attr/itemPadding = 0x7f040289
com.fishkaster.app:attr/cropShowCropOverlay = 0x7f040182
com.fishkaster.app:anim/linear_indeterminate_line1_tail_interpolator = 0x7f010024
com.fishkaster.app:dimen/notification_subtext_size = 0x7f07033e
com.fishkaster.app:styleable/SideSheetBehavior_Layout = 0x7f15008b
com.fishkaster.app:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1400c1
com.fishkaster.app:attr/actionButtonStyle = 0x7f04000c
com.fishkaster.app:attr/mock_labelColor = 0x7f040356
com.fishkaster.app:dimen/m3_card_elevated_elevation = 0x7f07010e
com.fishkaster.app:color/m3_timepicker_button_background_color = 0x7f06022e
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f14020d
com.fishkaster.app:dimen/mtrl_extended_fab_end_padding = 0x7f0702d0
com.fishkaster.app:attr/subheaderTextAppearance = 0x7f040458
com.fishkaster.app:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0701e1
com.fishkaster.app:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1402bf
com.fishkaster.app:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700cd
com.fishkaster.app:drawable/design_ic_visibility = 0x7f08009e
com.fishkaster.app:style/Widget.AppCompat.RatingBar = 0x7f140360
com.fishkaster.app:attr/behavior_skipCollapsed = 0x7f040080
com.fishkaster.app:style/Base.TextAppearance.AppCompat.Title = 0x7f140032
com.fishkaster.app:attr/colorControlNormal = 0x7f0400fe
com.fishkaster.app:attr/buttonTintMode = 0x7f0400a6
com.fishkaster.app:style/Widget.MaterialComponents.CheckedTextView = 0x7f14044f
com.fishkaster.app:attr/elevationOverlayColor = 0x7f0401cf
com.fishkaster.app:id/exo_settings = 0x7f0a00f1
com.fishkaster.app:attr/bottomSheetDragHandleStyle = 0x7f040087
com.fishkaster.app:style/Theme.AppCompat.Dialog.Alert = 0x7f140249
com.fishkaster.app:color/m3_ref_palette_primary10 = 0x7f06014e
com.fishkaster.app:dimen/design_tab_text_size_2line = 0x7f07008f
com.fishkaster.app:dimen/material_emphasis_high_type = 0x7f07025b
com.fishkaster.app:attr/colorOnSecondaryFixed = 0x7f04010d
com.fishkaster.app:color/material_dynamic_primary60 = 0x7f060269
com.fishkaster.app:color/material_dynamic_color_dark_on_error = 0x7f060242
com.fishkaster.app:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0e011d
com.fishkaster.app:attr/compatShadowEnabled = 0x7f040136
com.fishkaster.app:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1402ca
com.fishkaster.app:anim/catalyst_fade_out = 0x7f010019
com.fishkaster.app:attr/textAppearanceLabelLarge = 0x7f0404a0
com.fishkaster.app:attr/materialCardViewOutlinedStyle = 0x7f04032a
com.fishkaster.app:attr/clockIcon = 0x7f0400e2
com.fishkaster.app:integer/mtrl_calendar_header_orientation = 0x7f0b0033
com.fishkaster.app:id/exo_bottom_bar = 0x7f0a00cf
com.fishkaster.app:attr/buttonIconDimen = 0x7f04009e
com.fishkaster.app:anim/mtrl_bottom_sheet_slide_in = 0x7f01002f
com.fishkaster.app:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070264
com.fishkaster.app:attr/boxCornerRadiusBottomEnd = 0x7f04008c
com.fishkaster.app:style/Theme.MaterialComponents.Light.Bridge = 0x7f1402a0
com.fishkaster.app:attr/chipSpacingHorizontal = 0x7f0400d4
com.fishkaster.app:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f14034a
com.fishkaster.app:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.fishkaster.app:color/mtrl_outlined_stroke_color = 0x7f060302
com.fishkaster.app:style/Platform.V21.AppCompat.Light = 0x7f14016a
com.fishkaster.app:attr/indeterminateProgressStyle = 0x7f040271
com.fishkaster.app:color/m3_dynamic_highlighted_text = 0x7f0600a9
com.fishkaster.app:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1400ae
com.fishkaster.app:color/primary_text_default_material_dark = 0x7f06031b
com.fishkaster.app:attr/customColorValue = 0x7f040192
com.fishkaster.app:attr/actionModeTheme = 0x7f04001e
com.fishkaster.app:styleable/SimpleDraweeView = 0x7f15008d
com.fishkaster.app:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.fishkaster.app:color/design_dark_default_color_on_background = 0x7f060046
com.fishkaster.app:attr/colorSecondaryFixed = 0x7f040123
com.fishkaster.app:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1401fa
com.fishkaster.app:attr/drawableTintMode = 0x7f0401c0
com.fishkaster.app:dimen/m3_ripple_hovered_alpha = 0x7f0701fd
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_error = 0x7f0601a9
com.fishkaster.app:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f140052
com.fishkaster.app:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0e0035
com.fishkaster.app:style/Theme.AppCompat = 0x7f14023f
com.fishkaster.app:attr/materialSearchViewPrefixStyle = 0x7f040336
com.fishkaster.app:color/material_personalized_color_surface_container = 0x7f0602bf
com.fishkaster.app:drawable/abc_scrubber_track_mtrl_alpha = 0x7f080061
com.fishkaster.app:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010026
com.fishkaster.app:attr/boxStrokeErrorColor = 0x7f040091
com.fishkaster.app:color/design_box_stroke_color = 0x7f060043
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f0601ad
com.fishkaster.app:attr/animateNavigationIcon = 0x7f040037
com.fishkaster.app:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f140478
com.fishkaster.app:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f14002b
com.fishkaster.app:string/exo_controls_shuffle_on_description = 0x7f130085
com.fishkaster.app:drawable/m3_tabs_line_indicator = 0x7f08011f
com.fishkaster.app:attr/materialCalendarStyle = 0x7f040325
com.fishkaster.app:style/Base.V23.Theme.AppCompat.Light = 0x7f1400b3
com.fishkaster.app:dimen/m3_comp_search_view_docked_header_container_height = 0x7f07019a
com.fishkaster.app:attr/actionBarPopupTheme = 0x7f040003
com.fishkaster.app:id/square = 0x7f0a020d
com.fishkaster.app:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0e011b
com.fishkaster.app:dimen/m3_carousel_small_item_default_corner_size = 0x7f070116
com.fishkaster.app:layout/material_clock_display = 0x7f0d004b
com.fishkaster.app:string/catalyst_reload_button = 0x7f130046
com.fishkaster.app:attr/marginHorizontal = 0x7f04030c
com.fishkaster.app:string/state_off_description = 0x7f130142
com.fishkaster.app:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1402b4
com.fishkaster.app:id/rn_redbox_reload_button = 0x7f0a01d9
com.fishkaster.app:attr/popupMenuStyle = 0x7f0403bf
com.fishkaster.app:id/mtrl_picker_title_text = 0x7f0a0189
com.fishkaster.app:attr/toolbarId = 0x7f0404ed
com.fishkaster.app:attr/actionModeCloseContentDescription = 0x7f040013
com.fishkaster.app:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f140264
com.fishkaster.app:layout/redbox_item_frame = 0x7f0d0088
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral87 = 0x7f0600d4
com.fishkaster.app:attr/cropGuidelinesColor = 0x7f040174
com.fishkaster.app:id/clip_vertical = 0x7f0a0089
com.fishkaster.app:attr/background = 0x7f040052
com.fishkaster.app:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f07017d
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600ea
com.fishkaster.app:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f140310
com.fishkaster.app:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1402f6
com.fishkaster.app:style/TextAppearance.Compat.Notification.Line2.Media = 0x7f1401f4
com.fishkaster.app:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f14007a
com.fishkaster.app:attr/cropperLabelTextSize = 0x7f040189
com.fishkaster.app:attr/fontVariationSettings = 0x7f040241
com.fishkaster.app:style/Widget.Material3.ActionBar.Solid = 0x7f140382
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0e007c
com.fishkaster.app:color/notification_action_color_filter = 0x7f060314
com.fishkaster.app:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700eb
com.fishkaster.app:attr/actionBarDivider = 0x7f040001
com.fishkaster.app:style/Base.Theme.AppCompat.CompactMenu = 0x7f14004f
com.fishkaster.app:attr/indicatorInset = 0x7f040275
com.fishkaster.app:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.fishkaster.app:attr/targetPackage = 0x7f040488
com.fishkaster.app:attr/materialTimePickerStyle = 0x7f04033c
com.fishkaster.app:attr/cardUseCompatPadding = 0x7f0400b4
com.fishkaster.app:attr/backgroundTint = 0x7f04005c
com.fishkaster.app:id/textSpacerNoTitle = 0x7f0a0231
com.fishkaster.app:color/design_fab_stroke_top_outer_color = 0x7f060065
com.fishkaster.app:style/ExoStyledControls.TimeText.Duration = 0x7f140147
com.fishkaster.app:attr/behavior_autoShrink = 0x7f040076
com.fishkaster.app:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.fishkaster.app:attr/fastScrollHorizontalTrackDrawable = 0x7f040210
com.fishkaster.app:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.fishkaster.app:attr/bar_gravity = 0x7f040070
com.fishkaster.app:dimen/exo_styled_progress_enabled_thumb_size = 0x7f0700b2
com.fishkaster.app:attr/badgeWithTextShapeAppearance = 0x7f04006c
com.fishkaster.app:attr/shapeAppearanceLargeComponent = 0x7f04040d
com.fishkaster.app:attr/tabMinWidth = 0x7f040478
com.fishkaster.app:drawable/abc_btn_colored_material = 0x7f08002f
com.fishkaster.app:style/Widget.AppCompat.SeekBar.Discrete = 0x7f140366
com.fishkaster.app:id/exo_extra_controls_scroll_view = 0x7f0a00da
com.fishkaster.app:attr/materialCalendarHeaderLayout = 0x7f04031f
com.fishkaster.app:attr/badgeHeight = 0x7f04005f
com.fishkaster.app:string/common_google_play_services_notification_channel_name = 0x7f130059
com.fishkaster.app:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f14007f
com.fishkaster.app:id/search_plate = 0x7f0a01f2
com.fishkaster.app:attr/backgroundTintMode = 0x7f04005d
com.fishkaster.app:attr/motionDurationMedium4 = 0x7f040365
com.fishkaster.app:dimen/mtrl_navigation_rail_default_width = 0x7f0702f3
com.fishkaster.app:id/view_tree_disjoint_parent = 0x7f0a025e
com.fishkaster.app:attr/closeIconStartPadding = 0x7f0400e8
com.fishkaster.app:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1400d8
com.fishkaster.app:attr/actionBarTabStyle = 0x7f040008
com.fishkaster.app:attr/marginTopSystemWindowInsets = 0x7f04030f
com.fishkaster.app:attr/goIcon = 0x7f040249
com.fishkaster.app:attr/listChoiceBackgroundIndicator = 0x7f0402f6
com.fishkaster.app:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0e0162
com.fishkaster.app:integer/mtrl_calendar_selection_text_lines = 0x7f0b0034
com.fishkaster.app:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401fd
com.fishkaster.app:style/NoAnimationDialog = 0x7f14015f
com.fishkaster.app:macro/m3_comp_elevated_card_container_color = 0x7f0e002a
com.fishkaster.app:drawable/abc_star_black_48dp = 0x7f080067
com.fishkaster.app:attr/backgroundImage = 0x7f040054
com.fishkaster.app:attr/fabAnchorMode = 0x7f040204
com.fishkaster.app:attr/cameraTilt = 0x7f0400ac
com.fishkaster.app:attr/animation_enabled = 0x7f04003a
com.fishkaster.app:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1402f9
com.fishkaster.app:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0e016b
com.fishkaster.app:attr/applyMotionScene = 0x7f04003c
com.fishkaster.app:color/material_personalized_color_on_tertiary_container = 0x7f0602b1
com.fishkaster.app:attr/checkedIconSize = 0x7f0400c3
com.fishkaster.app:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0e0105
com.fishkaster.app:attr/extendStrategy = 0x7f0401fb
com.fishkaster.app:styleable/AppCompatEmojiHelper = 0x7f15000e
com.fishkaster.app:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.fishkaster.app:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f07016c
com.fishkaster.app:attr/scrubber_disabled_size = 0x7f0403fa
com.fishkaster.app:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f14020a
com.fishkaster.app:attr/colorSurfaceContainer = 0x7f040128
com.fishkaster.app:attr/startIconDrawable = 0x7f040441
com.fishkaster.app:attr/appBarLayoutStyle = 0x7f04003b
com.fishkaster.app:attr/defaultDuration = 0x7f04019f
com.fishkaster.app:color/m3_sys_color_light_surface_container = 0x7f06020b
com.fishkaster.app:string/fallback_menu_item_copy_link = 0x7f1300aa
com.fishkaster.app:drawable/abc_control_background_material = 0x7f08003a
com.fishkaster.app:attr/motionEasingStandardInterpolator = 0x7f040375
com.fishkaster.app:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.fishkaster.app:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f140289
com.fishkaster.app:attr/colorTertiaryFixed = 0x7f040133
com.fishkaster.app:styleable/ActionMode = 0x7f150004
com.fishkaster.app:drawable/scan = 0x7f080169
com.fishkaster.app:color/design_dark_default_color_primary = 0x7f06004b
com.fishkaster.app:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0e00c2
com.fishkaster.app:dimen/mtrl_calendar_day_horizontal_padding = 0x7f07029e
com.fishkaster.app:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.fishkaster.app:id/animateToEnd = 0x7f0a005d
com.fishkaster.app:color/abc_btn_colored_text_material = 0x7f060003
com.fishkaster.app:attr/nullable = 0x7f04038a
com.fishkaster.app:anim/m3_motion_fade_exit = 0x7f01002a
com.fishkaster.app:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0701cb
com.fishkaster.app:attr/hideMotionSpec = 0x7f040254
com.fishkaster.app:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f14044c
com.fishkaster.app:animator/mtrl_btn_state_list_anim = 0x7f020015
com.fishkaster.app:attr/errorShown = 0x7f0401e6
com.fishkaster.app:style/Widget.AppCompat.ButtonBar = 0x7f140337
com.fishkaster.app:style/TextAppearance.MaterialComponents.Headline2 = 0x7f140230
com.fishkaster.app:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0702d3
com.fishkaster.app:drawable/m3_tabs_rounded_line_indicator = 0x7f080120
com.fishkaster.app:dimen/abc_text_size_headline_material = 0x7f070047
com.fishkaster.app:attr/itemTextAppearanceInactive = 0x7f04029a
com.fishkaster.app:color/m3_ref_palette_neutral24 = 0x7f06012f
com.fishkaster.app:dimen/highlight_alpha_material_light = 0x7f0700bb
com.fishkaster.app:dimen/material_textinput_max_width = 0x7f070268
com.fishkaster.app:dimen/m3_comp_search_bar_container_elevation = 0x7f070195
com.fishkaster.app:string/exo_controls_fullscreen_enter_description = 0x7f130073
com.fishkaster.app:id/default_activity_button = 0x7f0a00a6
com.fishkaster.app:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0c000d
com.fishkaster.app:drawable/abc_item_background_holo_light = 0x7f08004b
com.fishkaster.app:attr/itemTextColor = 0x7f04029b
com.fishkaster.app:dimen/m3_btn_elevation = 0x7f0700f6
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.Button = 0x7f1401e6
com.fishkaster.app:id/stretch = 0x7f0a021a
com.fishkaster.app:attr/textAppearanceTitleLarge = 0x7f0404af
com.fishkaster.app:attr/actualImageUri = 0x7f040029
com.fishkaster.app:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f070228
com.fishkaster.app:attr/fontProviderFetchStrategy = 0x7f04023b
com.fishkaster.app:styleable/Spinner = 0x7f150091
com.fishkaster.app:color/material_dynamic_color_light_on_error = 0x7f060246
com.fishkaster.app:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f140307
com.fishkaster.app:color/material_dynamic_color_dark_error_container = 0x7f060241
com.fishkaster.app:layout/mtrl_calendar_day_of_week = 0x7f0d0060
com.fishkaster.app:attr/actionModeWebSearchDrawable = 0x7f04001f
com.fishkaster.app:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0e0154
com.fishkaster.app:color/m3_ref_palette_neutral99 = 0x7f06013f
com.fishkaster.app:attr/actionModeCloseDrawable = 0x7f040014
com.fishkaster.app:attr/flow_horizontalBias = 0x7f040228
com.fishkaster.app:attr/layoutDuringTransition = 0x7f0402af
com.fishkaster.app:attr/actionMenuTextAppearance = 0x7f04000f
com.fishkaster.app:color/design_default_color_primary_dark = 0x7f060059
com.fishkaster.app:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600de
com.fishkaster.app:color/m3_ref_palette_error10 = 0x7f06011c
com.fishkaster.app:dimen/abc_text_size_medium_material = 0x7f070049
com.fishkaster.app:dimen/mtrl_btn_letter_spacing = 0x7f070289
com.fishkaster.app:string/mtrl_checkbox_button_path_checked = 0x7f1300e9
com.fishkaster.app:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08005d
com.fishkaster.app:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f0601b1
com.fishkaster.app:drawable/common_google_signin_btn_text_dark_normal = 0x7f080095
com.fishkaster.app:attr/thumbWidth = 0x7f0404cf
com.fishkaster.app:style/Widget.Material3.Chip.Filter.Elevated = 0x7f1403ae
com.fishkaster.app:anim/rns_fade_from_bottom = 0x7f010036
com.fishkaster.app:string/catalyst_dev_menu_header = 0x7f130038
com.fishkaster.app:attr/contentPaddingBottom = 0x7f040145
com.fishkaster.app:styleable/ClockHandView = 0x7f150024
com.fishkaster.app:string/abc_menu_meta_shortcut_label = 0x7f13000d
com.fishkaster.app:attr/mock_showLabel = 0x7f040358
com.fishkaster.app:styleable/ScrollingViewBehavior_Layout = 0x7f150086
com.fishkaster.app:dimen/mtrl_slider_label_radius = 0x7f07030d
com.fishkaster.app:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1400c2
com.fishkaster.app:attr/titleCollapseMode = 0x7f0404df
com.fishkaster.app:id/clip_horizontal = 0x7f0a0088
com.fishkaster.app:dimen/abc_text_size_subhead_material = 0x7f07004d
com.fishkaster.app:attr/contentPadding = 0x7f040144
com.fishkaster.app:color/bright_foreground_material_light = 0x7f060026
com.fishkaster.app:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0602cc
com.fishkaster.app:color/m3_button_outline_color_selector = 0x7f06008c
com.fishkaster.app:attr/layout_constraintGuide_end = 0x7f0402c4
com.fishkaster.app:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f070316
com.fishkaster.app:color/m3_ref_palette_error0 = 0x7f06011b
com.fishkaster.app:attr/maxActionInlineWidth = 0x7f040340
com.fishkaster.app:string/exo_download_failed = 0x7f13008c
com.fishkaster.app:dimen/mtrl_card_checked_icon_margin = 0x7f0702c1
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f070236
com.fishkaster.app:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f07012b
com.fishkaster.app:attr/prefixTextColor = 0x7f0403c4
com.fishkaster.app:attr/iconGravity = 0x7f040265
com.fishkaster.app:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0e00c0
com.fishkaster.app:attr/haloRadius = 0x7f04024c
com.fishkaster.app:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f070274
com.fishkaster.app:color/material_slider_halo_color = 0x7f0602d4
com.fishkaster.app:color/mtrl_switch_track_tint = 0x7f060308
com.fishkaster.app:attr/values = 0x7f040521
com.fishkaster.app:attr/hideOnScroll = 0x7f040257
com.fishkaster.app:attr/colorOnContainerUnchecked = 0x7f040103
com.fishkaster.app:dimen/design_tab_max_width = 0x7f07008c
com.fishkaster.app:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0601cc
com.fishkaster.app:attr/drawableStartCompat = 0x7f0401be
com.fishkaster.app:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f0701a6
com.fishkaster.app:styleable/CompoundButton = 0x7f150028
com.fishkaster.app:dimen/design_fab_size_mini = 0x7f070074
com.fishkaster.app:attr/graph = 0x7f04024a
com.fishkaster.app:style/TextAppearance.AppCompat.Medium = 0x7f1401d1
com.fishkaster.app:style/Widget.AppCompat.Light.PopupMenu = 0x7f140352
com.fishkaster.app:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0e008d
com.fishkaster.app:attr/passwordToggleContentDescription = 0x7f0403a3
com.fishkaster.app:color/m3_sys_color_light_surface_variant = 0x7f060211
com.fishkaster.app:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f140447
com.fishkaster.app:style/Widget.Material3.TabLayout = 0x7f14041a
com.fishkaster.app:string/material_hour_suffix = 0x7f1300cf
com.fishkaster.app:drawable/abc_ic_search_api_material = 0x7f080048
com.fishkaster.app:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f080136
com.fishkaster.app:color/material_personalized_hint_foreground = 0x7f0602ce
com.fishkaster.app:attr/errorIconTint = 0x7f0401e4
com.fishkaster.app:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.fishkaster.app:attr/colorSecondaryFixedDim = 0x7f040124
com.fishkaster.app:layout/mtrl_calendar_month_navigation = 0x7f0d0065
com.fishkaster.app:attr/switchMinWidth = 0x7f040466
com.fishkaster.app:id/scale = 0x7f0a01e4
com.fishkaster.app:color/m3_ref_palette_dynamic_secondary60 = 0x7f060108
com.fishkaster.app:anim/abc_slide_out_top = 0x7f010009
com.fishkaster.app:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1400cf
com.fishkaster.app:color/common_google_signin_btn_text_light_default = 0x7f06003d
com.fishkaster.app:style/Base.V28.Theme.AppCompat = 0x7f1400bb
com.fishkaster.app:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1400a3
com.fishkaster.app:color/m3_timepicker_display_background_color = 0x7f060232
com.fishkaster.app:id/mtrl_calendar_days_of_week = 0x7f0a0176
com.fishkaster.app:drawable/amu_bubble_mask = 0x7f080078
com.fishkaster.app:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f140086
com.fishkaster.app:drawable/mtrl_switch_thumb_pressed_checked = 0x7f080149
com.fishkaster.app:attr/elevationOverlayAccentColor = 0x7f0401ce
com.fishkaster.app:anim/rns_ios_from_right_foreground_open = 0x7f010041
com.fishkaster.app:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0e0091
com.fishkaster.app:anim/abc_fade_out = 0x7f010001
com.fishkaster.app:dimen/m3_comp_input_chip_container_height = 0x7f070157
com.fishkaster.app:integer/material_motion_duration_medium_1 = 0x7f0b002b
com.fishkaster.app:anim/rns_fade_to_bottom = 0x7f010039
com.fishkaster.app:style/TextAppearance.AppCompat.Subhead = 0x7f1401d8
com.fishkaster.app:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.fishkaster.app:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070272
com.fishkaster.app:attr/expanded = 0x7f0401eb
com.fishkaster.app:anim/m3_side_sheet_enter_from_right = 0x7f01002c
com.fishkaster.app:animator/fragment_close_enter = 0x7f020003
com.fishkaster.app:macro/m3_comp_slider_label_label_text_color = 0x7f0e0112
com.fishkaster.app:attr/autoSizeStepGranularity = 0x7f040048
com.fishkaster.app:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1403c1
com.fishkaster.app:attr/launchSingleTop = 0x7f0402ac
com.fishkaster.app:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f140083
com.fishkaster.app:interpolator/m3_sys_motion_easing_linear = 0x7f0c000a
com.fishkaster.app:attr/autoSizeMinTextSize = 0x7f040046
com.fishkaster.app:attr/selectableItemBackground = 0x7f040403
com.fishkaster.app:array/exo_controls_playback_speeds = 0x7f030000
com.fishkaster.app:attr/dividerPadding = 0x7f0401b2
com.fishkaster.app:dimen/material_clock_display_width = 0x7f07024a
com.fishkaster.app:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1401b7
com.fishkaster.app:macro/m3_comp_date_picker_modal_container_shape = 0x7f0e000e
com.fishkaster.app:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1401e4
com.fishkaster.app:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0e007a
com.fishkaster.app:attr/marginRightSystemWindowInsets = 0x7f04030e
com.fishkaster.app:animator/m3_card_state_list_anim = 0x7f02000d
com.fishkaster.app:attr/mock_diagonalsColor = 0x7f040353
com.fishkaster.app:attr/autoSizeMaxTextSize = 0x7f040045
com.fishkaster.app:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f07014b
com.fishkaster.app:id/wrap = 0x7f0a026c
com.fishkaster.app:id/accessibility_custom_action_20 = 0x7f0a0023
com.fishkaster.app:styleable/View = 0x7f1500a6
com.fishkaster.app:id/open_search_view_search_prefix = 0x7f0a01a9
com.fishkaster.app:dimen/mtrl_progress_circular_size_extra_small = 0x7f070300
com.fishkaster.app:layout/exo_styled_sub_settings_list_item = 0x7f0d003f
com.fishkaster.app:attr/textAppearanceHeadline6 = 0x7f04049c
com.fishkaster.app:id/filled = 0x7f0a0106
com.fishkaster.app:style/Base.Widget.AppCompat.SearchView = 0x7f1400f5
com.fishkaster.app:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0e014b
com.fishkaster.app:drawable/ellipsis_horizontal = 0x7f0800a4
com.fishkaster.app:anim/abc_fade_in = 0x7f010000
com.fishkaster.app:attr/maxAcceleration = 0x7f04033f
com.fishkaster.app:style/TextAppearance.Compat.Notification.Time.Media = 0x7f1401f7
com.fishkaster.app:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f080096
com.fishkaster.app:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f070237
com.fishkaster.app:attr/layout_constraintStart_toStartOf = 0x7f0402d4
com.fishkaster.app:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0e0136
com.fishkaster.app:attr/showDividers = 0x7f040417
com.fishkaster.app:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f1401b0
com.fishkaster.app:id/fill = 0x7f0a0100
com.fishkaster.app:dimen/material_clock_period_toggle_horizontal_gap = 0x7f070252
com.fishkaster.app:styleable/AlertDialog = 0x7f150007
com.fishkaster.app:color/m3_sys_color_light_surface_container_high = 0x7f06020c
com.fishkaster.app:animator/fragment_fade_exit = 0x7f020006
com.fishkaster.app:attr/dropDownBackgroundTint = 0x7f0401c5
com.fishkaster.app:id/select_dialog_listview = 0x7f0a01f6
com.fishkaster.app:attr/checkboxStyle = 0x7f0400bc
com.fishkaster.app:color/m3_ref_palette_error60 = 0x7f060122
com.fishkaster.app:anim/abc_popup_exit = 0x7f010004
com.fishkaster.app:dimen/m3_extended_fab_bottom_padding = 0x7f0701d5
com.fishkaster.app:color/dev_launcher_white = 0x7f06006d
