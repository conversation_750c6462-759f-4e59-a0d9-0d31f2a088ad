# ninja log v5
2	21	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/x86_64/CMakeFiles/cmake.verify_globs	26736392c374d884
23	2576	7802456634919825	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	dfd14aa2d56a5dce
43	2650	7802456635630470	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	2fe72e07cb46ca9
68	2804	7802456637206928	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd470dadb21de9e1a8a9b540771142e1/components/safeareacontext/States.cpp.o	2dac61d4b4060f78
14	2927	7802456638097933	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	4bc7f34b6db6135
47	3043	7802456639399145	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	9fd3788ba7f3483b
77	3300	7802456642111921	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51215515fe21556a3837eeba2b49d411/safeareacontext/EventEmitters.cpp.o	e1f81c0d933f47c
26	3314	7802456642221929	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	c324d5ab1f31bd19
55	3351	7802456642597399	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	5b8592c95d884db9
20	3403	7802456643037781	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	d1ef0001c769203
51	3414	7802456643238042	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	66de8fdc46162b16
11	3504	7802456644008812	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	814c64197bd972f3
40	3515	7802456643978808	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	1897e6c01135c89a
105	3576	7802456644864618	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/34e83742130ce8fa5cb0bd5b71d73d82/jni/safeareacontext-generated.cpp.o	1000e78643e37708
94	3584	7802456644909708	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2e4509cb0d0a1d5cc7411f483425a060/safeareacontextJSI-generated.cpp.o	185e9e7287c4cc59
110	3593	7802456644959762	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSBottomTabsState.cpp.o	182e92f99c6499e8
116	3890	7802456648128470	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	33d04efdbf0f654d
150	3914	7802456648293652	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	81ad281937797ffc
17	4002	7802456648856720	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	2174c6610feccfd2
34	4080	7802456649845496	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	d761a837e151303c
59	4129	7802456650251016	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/002469d5b84800fdd549c5e175ca642e/RNCSafeAreaViewState.cpp.o	9dd30e394305d0b3
29	4150	7802456650533807	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	9759cabb9d31bce9
8	4229	7802456651329757	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	3b22cbd6590834c3
6	4394	7802456653165840	CMakeFiles/appmodules.dir/OnLoad.cpp.o	60693f106b840e71
133	4491	7802456654231965	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	fe04992b94f7b6c3
73	4566	7802456654767648	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd470dadb21de9e1a8a9b540771142e1/components/safeareacontext/Props.cpp.o	c8e61cfc112c09b
82	4582	7802456655087980	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51215515fe21556a3837eeba2b49d411/safeareacontext/ShadowNodes.cpp.o	ab26a8f6a3ff82bd
121	4622	7802456655448341	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	fe86164f07754027
100	4651	7802456655758699	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	e03e7c142603f0b8
127	4802	7802456657265225	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSModalScreenShadowNode.cpp.o	127e21fa638c4909
139	4983	7802456659067252	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e7ccc897f1f91c69642c8739e8d03702/components/rnscreens/RNSScreenShadowNode.cpp.o	dd480fcbec4533aa
145	5008	7802456659357363	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	7f1d068260bb297a
88	5149	7802456660658752	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2e4509cb0d0a1d5cc7411f483425a060/ComponentDescriptors.cpp.o	c9a9adcb06f4b7d1
63	5347	7802456662751164	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/002469d5b84800fdd549c5e175ca642e/RNCSafeAreaViewShadowNode.cpp.o	764cdd057802df2b
5347	5480	7802456664016108	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/x86_64/libreact_codegen_safeareacontext.so	aca48b27c34fdf55
3301	5854	7802456667874931	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o	d22800b3935df802
3585	6094	7802456670258798	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/96f712542fa1af3a4416d1d2a5f6896e/jni/react/renderer/components/rnscreens/States.cpp.o	f76b119b7a8e1e97
2805	6188	7802456671184681	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/19c14822b9fd6da13ca8af6ae442fcdf/renderer/components/rnscreens/RNSScreenState.cpp.o	931b18a33b2a42fa
3891	6195	7802456671309784	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/States.cpp.o	75ec473ef6a36214
3505	6231	7802456671630098	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o	1010d73270918821
3414	6437	7802456673717582	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o	a66c47c3bb5d4513
3576	6570	7802456674998512	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/79a671fe4e2ad3a12207216f48eb05ff/components/rnscreens/rnscreensJSI-generated.cpp.o	c7c1ea0988652b95
3314	6575	7802456675073615	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o	1e893fce1d00b188
4230	6606	7802456675413937	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/EventEmitters.cpp.o	c61b7851f39d3571
3352	6797	7802456677330734	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o	4a79fd5620902264
4003	6820	7802456677545926	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/RNWorkletsSpecJSI-generated.cpp.o	9edf9b62d6647ef0
4151	6860	7802456677938277	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/RNWorkletsSpec-generated.cpp.o	3458c314712cc2f8
4129	6862	7802456677978277	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o	176a4fee137d8a29
3403	6933	7802456678688959	rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o	4288a4e7a152756a
2928	6985	7802456679204351	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5695f77ec92f70f29e128dd3dad296b3/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	a67acfa65ed1f233
2583	6987	7802456679229383	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	dd694a09970644c9
3914	7045	7802456679804920	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/ShadowNodes.cpp.o	b598100d94578134
4395	7057	7802456679950000	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/Props.cpp.o	248d260ad9ef0556
4080	7199	7802456681321155	RNWorkletsSpec_autolinked_build/CMakeFiles/react_codegen_RNWorkletsSpec.dir/react/renderer/components/RNWorkletsSpec/ComponentDescriptors.cpp.o	673a29f6b17c5360
3593	7212	7802456681496311	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a328c61c957b5da5800f94032115c8ec/react/renderer/components/rnscreens/ShadowNodes.cpp.o	c4dec83148e09163
2650	7842	7802456687626998	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/79a671fe4e2ad3a12207216f48eb05ff/components/rnscreens/ComponentDescriptors.cpp.o	47643fb2ed9ae85f
3515	7850	7802456687707001	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/96f712542fa1af3a4416d1d2a5f6896e/jni/react/renderer/components/rnscreens/Props.cpp.o	1f14421e7ea6fba8
2	7911	7802456688182551	CMakeFiles/appmodules.dir/27cb79e6d61ee1f4d1cb0b5ed9bcaa7b/raskel/raskel-live-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	f19c2cc4a154289f
3044	8114	7802456690285246	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f60e6a02b5031c13be66380d8fa3a5a7/renderer/components/rnscreens/EventEmitters.cpp.o	2f743ebf94df2c08
8114	8202	7802456691262960	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/x86_64/libreact_codegen_rnscreens.so	41c278c6c1489f6
8202	8311	7802456692379094	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/intermediates/cxx/Debug/b1j5m1r3/obj/x86_64/libappmodules.so	cb6931a9f4ed0b47
