/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.ViewManagerWithGeneratedInterface;

public interface RNSSplitViewHostManagerInterface<T extends View> extends ViewManagerWithGeneratedInterface {
  void setPreferredDisplayMode(T view, @Nullable String value);
  void setPreferredSplitBehavior(T view, @Nullable String value);
  void setPrimaryEdge(T view, @Nullable String value);
  void setShowSecondaryToggleButton(T view, boolean value);
  void setDisplayModeButtonVisibility(T view, @Nullable String value);
  void setColumnMetrics(T view, @Nullable ReadableMap value);
  void setOrientation(T view, @Nullable String value);
  void setPresentsWithGesture(T view, boolean value);
  void setShowInspector(T view, boolean value);
}
