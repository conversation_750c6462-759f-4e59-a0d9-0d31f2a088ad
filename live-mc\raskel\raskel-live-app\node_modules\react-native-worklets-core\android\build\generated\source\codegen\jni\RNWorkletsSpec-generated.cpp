
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "RNWorkletsSpec.h"

namespace facebook::react {

static facebook::jsi::Value __hostFunction_NativeWorkletsSpecJSI_install(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, BooleanKind, "install", "()Z", args, count, cachedMethodId);
}

NativeWorkletsSpecJSI::NativeWorkletsSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["install"] = MethodMetadata {0, __hostFunction_NativeWorkletsSpecJSI_install};
}

std::shared_ptr<TurboModule> RNWorkletsSpec_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "Worklets") {
    return std::make_shared<NativeWorkletsSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
