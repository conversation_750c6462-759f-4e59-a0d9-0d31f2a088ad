{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-be478b1c672e868ca0be.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "AgoraRtcNgSpec_autolinked_build", "jsonFile": "directory-AgoraRtcNgSpec_autolinked_build-Debug-36228ed01d8f7b16e730.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-7edbff53295884eb8316.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-78f77f64e9840826a508.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-a4c03abf740cbc6a99c0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnworklets_autolinked_build", "jsonFile": "directory-rnworklets_autolinked_build-Debug-4fed6edc360780dd45ac.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "RNWorkletsSpec_autolinked_build", "jsonFile": "directory-RNWorkletsSpec_autolinked_build-Debug-135c21e0b21b1f4dd285.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-a3250202f8c1ae9b0551.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_AgoraRtcNgSpec::@b3e2fa00649b55f06a11", "jsonFile": "target-react_codegen_AgoraRtcNgSpec-Debug-e5c039a3b67fb7754b07.json", "name": "react_codegen_AgoraRtcNgSpec", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_RNWorkletsSpec::@c787b30dd2b0f948dced", "jsonFile": "target-react_codegen_RNWorkletsSpec-Debug-35701a29e175e17fbe74.json", "name": "react_codegen_RNWorkletsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-9c418492bed9381a20af.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-41fdc295fbd3488ac655.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-5d8db866ea888e76a934.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnworklets::@68f58d84d4754f193387", "jsonFile": "target-react_codegen_rnworklets-Debug-4f0a88c4a81790e78f9d.json", "name": "react_codegen_rnworklets", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-bbefedd21e426143adf9.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}