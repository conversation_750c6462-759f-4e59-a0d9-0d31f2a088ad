# ninja log v5
12	7755	7802455610316660	src/fabric/CMakeFiles/fabric.dir/cmake_pch.hxx.pch	4a3dc5867705984b
3	7865	7802455611773481	CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch	9b54c9271c816c9c
8101	9722	7802455628920768	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	8b080530b22afad0
7874	9784	7802455629161274	CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o	ea5d5bcfd23a47cc
8027	9801	7802455629637038	CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/SharedRef.cpp.o	8bb8224e4543cdcc
7866	10428	7802455632000595	CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/NativeModule.cpp.o	94c98025544a7758
8151	10453	7802455632115772	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o	75e835d8e3bddcb9
8084	10468	7802455629181264	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	77cd930f7d768cdc
8132	10788	7802455635615408	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	e330722135fbed86
8034	10886	7802455638399464	CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/TypedArray.cpp.o	c4ba6b4b9344f3a7
8457	11255	7802455640908226	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	f35f236c8c099bd2
8422	11265	7802455645059418	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	b13296806f9a96a9
8441	11349	7802455646215961	CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o	8d148647af1ee8cd
8049	11721	7802455646681602	CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/SharedObject.cpp.o	17f4b80be7dd28fb
9723	11745	7802455647257215	CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/LazyObject.cpp.o	f754a98f691d572a
8449	11750	7802455646621543	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	2849598419fa30f1
7786	11869	7802455651760249	src/fabric/CMakeFiles/fabric.dir/8cdb0914a492ff61de3267e4cb77f99d/common/cpp/fabric/ExpoViewEventEmitter.cpp.o	e2aa666ef19a7aa9
8118	12027	7802455652216105	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	c2a3cf1a8742f8ab
8302	12095	7802455652661877	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	21cee6480e23dec0
8091	12099	7802455653568224	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	a85ff9327f22e2cc
8171	12640	7802455659414202	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	10a768f6873c7017
8076	12902	7802455660967809	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	48a7d830d13785dc
8395	12904	7802455661556678	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o	72781f7a5559a5c6
8351	12907	7802455661202965	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	1c2937bbfa5b9577
8187	12972	7802455662628532	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	da3537419a42e7f1
8059	13795	7802455668511060	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	60ecb53fc6774dc
8378	13797	7802455669841349	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	7c2163a85d05eedf
9803	13799	7802455669946524	CMakeFiles/expo-modules-core.dir/e6b18d4f51e54e10ca0ead7d868a78dd/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	84199ce66c72b1d6
7763	14373	7802455675404687	src/fabric/CMakeFiles/fabric.dir/8cdb0914a492ff61de3267e4cb77f99d/common/cpp/fabric/ExpoViewShadowNode.cpp.o	8742ed9901aa2463
8387	14736	7802455679311049	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	318724dabefca0c4
11255	14748	7802455680081740	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o	a7721e16f17e51b4
8412	14816	7802455681023066	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	f59691d4eac22d68
11349	15072	7802455679225734	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o	852712aafc337980
8434	15088	7802455679941573	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	373ab37cfcc7cc3c
11265	15090	7802455681839353	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o	88d7e6b7caaa11a5
10455	15425	7802455687663888	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	1d026c15f1a7cefd
9786	15626	7802455687849186	CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/EventEmitter.cpp.o	31556f761b4b11c1
8110	15652	7802455688344830	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o	b6248666bf88a0d5
7808	15680	7802455689967843	src/fabric/CMakeFiles/fabric.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/fabric/ExpoViewProps.cpp.o	bf9d4224b23caced
10574	15794	7802455690338300	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o	42e87d31d5698ea5
7822	15811	7802455690769721	src/fabric/CMakeFiles/fabric.dir/FabricComponentsRegistry.cpp.o	b9f279d19ae809c1
10888	15818	7802455691546183	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	7cb402f31485db8d
10789	16123	7802455694471119	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o	4cd8c5c4b86bdbd1
8125	16128	7802455694471119	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o	fd5620a62d19c3ae
10429	16498	7802455698039114	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o	c87b044e27a5c631
7798	16618	7802455699475116	src/fabric/CMakeFiles/fabric.dir/8cdb0914a492ff61de3267e4cb77f99d/common/cpp/fabric/ExpoViewComponentDescriptor.cpp.o	ef59490b225651b9
16618	16957	7802455702701880	src/fabric/libfabric.a	2f565bfeac4d1ee9
8162	20991	7802455743348926	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	3e4bcfbeae872df0
20992	21154	7802455744947183	../../../../build/intermediates/cxx/Debug/335l6r1b/obj/arm64-v8a/libexpo-modules-core.so	cf722a7f5d418ce6
