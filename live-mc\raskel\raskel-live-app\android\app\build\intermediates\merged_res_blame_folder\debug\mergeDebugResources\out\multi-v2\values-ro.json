{"logs": [{"outputFile": "com.fishkaster.app-mergeDebugResources-106:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8aa23bce709bfe96db10568360097556\\transformed\\core-1.15.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "60,61,62,63,64,65,66,246", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4512,4610,4712,4812,4911,5013,5122,20412", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "4605,4707,4807,4906,5008,5117,5234,20508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f198488ab5a5112dc7ed7a95c5218eda\\transformed\\browser-1.6.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "91,149,150,151", "startColumns": "4,4,4,4", "startOffsets": "8067,12542,12644,12757", "endColumns": "106,101,112,102", "endOffsets": "8169,12639,12752,12855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\29b62c1e8951214205c1999e10893e42\\transformed\\foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,236", "endColumns": "85,94,99", "endOffsets": "136,231,331"}, "to": {"startLines": "54,253,254", "startColumns": "4,4,4", "startOffsets": "3973,21006,21101", "endColumns": "85,94,99", "endOffsets": "4054,21096,21196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\02bec446bb5b07b8548f83ec26b6ae83\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1168,1291,1397,1510,1594,1699,1818,1903,1983,2074,2167,2262,2356,2456,2549,2644,2738,2829,2921,3002,3112,3220,3318,3430,3536,3640,3802,19061", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "1286,1392,1505,1589,1694,1813,1898,1978,2069,2162,2257,2351,2451,2544,2639,2733,2824,2916,2997,3107,3215,3313,3425,3531,3635,3797,3898,19138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,274,345,429,497,573,657,742,832,901,985,1075,1150,1232,1311,1389,1467,1541,1626,1699,1775", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "120,199,269,340,424,492,568,652,737,827,896,980,1070,1145,1227,1306,1384,1462,1536,1621,1694,1770,1855"}, "to": {"startLines": "53,72,152,154,155,172,173,174,223,224,227,228,235,236,237,239,240,242,244,245,247,250,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3903,5726,12860,13004,13075,14358,14426,14502,18555,18640,18908,18977,19543,19633,19708,19864,19943,20100,20253,20327,20513,20730,20921", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "3968,5800,12925,13070,13154,14421,14497,14581,18635,18725,18972,19056,19628,19703,19785,19938,20016,20173,20322,20407,20581,20801,21001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\1ff7e39d1dce962b72e0e33d3ff513a9\\transformed\\play-services-base-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,579,685,832,958,1077,1182,1340,1447,1602,1731,1873,2035,2100,2164", "endColumns": "103,155,125,105,146,125,118,104,157,106,154,128,141,161,64,63,78", "endOffsets": "296,452,578,684,831,957,1076,1181,1339,1446,1601,1730,1872,2034,2099,2163,2242"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5805,5913,6073,6203,6313,6464,6594,6717,6970,7132,7243,7402,7535,7681,7847,7916,7984", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "5908,6068,6198,6308,6459,6589,6712,6821,7127,7238,7397,7530,7676,7842,7911,7979,8062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3ef9533b8d5a0884be92f5c6e6c0dbd2\\transformed\\exoplayer-ui-2.18.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,939,1028,1116,1212,1308,1383,1449,1548,1645,1716,1781,1844,1913,2029,2144,2260,2336,2416,2485,2561,2659,2759,2824,2887,2940,2998,3046,3107,3171,3241,3306,3375,3436,3494,3560,3624,3690,3742,3804,3880,3956", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,115,114,115,75,79,68,75,97,99,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,934,1023,1111,1207,1303,1378,1444,1543,1640,1711,1776,1839,1908,2024,2139,2255,2331,2411,2480,2556,2654,2754,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3619,3685,3737,3799,3875,3951,4008"}, "to": {"startLines": "2,11,16,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,677,8589,8676,8765,8853,8949,9045,9120,9186,9285,9382,9453,9518,9581,9650,9766,9881,9997,10073,10153,10222,10298,10396,10496,10561,11321,11374,11432,11480,11541,11605,11675,11740,11809,11870,11928,11994,12058,12124,12176,12238,12314,12390", "endLines": "10,15,20,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,115,114,115,75,79,68,75,97,99,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "376,672,942,8671,8760,8848,8944,9040,9115,9181,9280,9377,9448,9513,9576,9645,9761,9876,9992,10068,10148,10217,10293,10391,10491,10556,10619,11369,11427,11475,11536,11600,11670,11735,11804,11865,11923,11989,12053,12119,12171,12233,12309,12385,12442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\5deb70712b1e9df4e3892e963cadfc60\\transformed\\material-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1142,1208,1303,1377,1437,1521,1583,1649,1707,1780,1843,1899,2018,2075,2136,2192,2266,2411,2497,2572,2661,2740,2824,2957,3039,3122,3268,3358,3438,3493,3544,3610,3683,3761,3832,3917,3988,4065,4139,4211,4317,4408,4482,4577,4675,4749,4829,4930,4983,5069,5135,5224,5314,5376,5440,5503,5577,5689,5799,5909,6014,6073,6128,6207,6293,6370", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1137,1203,1298,1372,1432,1516,1578,1644,1702,1775,1838,1894,2013,2070,2131,2187,2261,2406,2492,2567,2656,2735,2819,2952,3034,3117,3263,3353,3433,3488,3539,3605,3678,3756,3827,3912,3983,4060,4134,4206,4312,4403,4477,4572,4670,4744,4824,4925,4978,5064,5130,5219,5309,5371,5435,5498,5572,5684,5794,5904,6009,6068,6123,6202,6288,6365,6444"}, "to": {"startLines": "21,55,56,57,58,59,67,68,69,95,96,148,153,158,159,160,161,162,163,164,165,166,167,168,169,170,171,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,222,230,231,233", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,4059,4151,4239,4326,4422,5239,5340,5461,8461,8523,12447,12930,13324,13384,13468,13530,13596,13654,13727,13790,13846,13965,14022,14083,14139,14213,14586,14672,14747,14836,14915,14999,15132,15214,15297,15443,15533,15613,15668,15719,15785,15858,15936,16007,16092,16163,16240,16314,16386,16492,16583,16657,16752,16850,16924,17004,17105,17158,17244,17310,17399,17489,17551,17615,17678,17752,17864,17974,18084,18189,18248,18476,19143,19229,19379", "endLines": "25,55,56,57,58,59,67,68,69,95,96,148,153,158,159,160,161,162,163,164,165,166,167,168,169,170,171,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,222,230,231,233", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "1163,4146,4234,4321,4417,4507,5335,5456,5540,8518,8584,12537,12999,13379,13463,13525,13591,13649,13722,13785,13841,13960,14017,14078,14134,14208,14353,14667,14742,14831,14910,14994,15127,15209,15292,15438,15528,15608,15663,15714,15780,15853,15931,16002,16087,16158,16235,16309,16381,16487,16578,16652,16747,16845,16919,16999,17100,17153,17239,17305,17394,17484,17546,17610,17673,17747,17859,17969,18079,18184,18243,18298,18550,19224,19301,19453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\30f51c8a36c85c07b7e3abc1db11ccb6\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,289,373,470,572,660,738,825,916,998,1086,1176,1249,1334,1408,1487,1562,1639,1706", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,84,73,78,74,76,66,114", "endOffsets": "284,368,465,567,655,733,820,911,993,1081,1171,1244,1329,1403,1482,1557,1634,1701,1816"}, "to": {"startLines": "70,71,92,93,94,156,157,220,221,225,226,232,234,238,241,243,248,249,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5545,5642,8174,8271,8373,13159,13237,18303,18394,18730,18818,19306,19458,19790,20021,20178,20586,20663,20806", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,84,73,78,74,76,66,114", "endOffsets": "5637,5721,8266,8368,8456,13232,13319,18389,18471,18813,18903,19374,19538,19859,20095,20248,20658,20725,20916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cf4e6ff07dbeac5bcd8d8e9bb606754a\\transformed\\exoplayer-core-2.18.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,265,337,415,495,585,678", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "131,195,260,332,410,490,580,673,747"}, "to": {"startLines": "121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10624,10705,10769,10834,10906,10984,11064,11154,11247", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "10700,10764,10829,10901,10979,11059,11149,11242,11316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\72fd1eaed4249ec9561f48df05200d45\\transformed\\play-services-basement-18.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "6826", "endColumns": "143", "endOffsets": "6965"}}]}]}