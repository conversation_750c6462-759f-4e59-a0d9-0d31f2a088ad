{"name": "msrfi-live-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:dev-client": "expo start --dev-client", "android": "expo run:android", "android:dev": "expo run:android --device", "ios": "expo run:ios", "ios:dev": "expo run:ios --device", "web": "expo start --web", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "safe-build": "node scripts/safe-build.js", "safe-android": "node scripts/safe-build.js && expo run:android", "clean-build": "node scripts/safe-build.js --clean && expo run:android"}, "dependencies": {"@react-native-async-storage/async-storage": "2.2.0", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@supabase/supabase-js": "^2.50.4", "@tensorflow-models/mobilenet": "^2.1.1", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-react-native": "^1.0.0", "cloudinary-react-native": "^1.2.1", "dotenv": "^17.2.2", "expo": "^54.0.0", "expo-av": "~16.0.7", "expo-build-properties": "~1.0.9", "expo-constants": "~18.0.9", "expo-dev-client": "~6.0.12", "expo-document-picker": "~14.0.7", "expo-image-picker": "~17.0.8", "expo-location": "~19.0.7", "expo-status-bar": "~3.0.8", "expo-system-ui": "~6.0.7", "react": "19.1.0", "react-native": "0.79.5", "react-native-maps": "1.20.1", "react-native-purchases": "^9.5.0", "react-native-reanimated": "^4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-vision-camera": "^4.7.2", "react-native-worklets": "^0.5.1", "react-native-worklets-core": "^1.6.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-flow-strip-types": "^7.25.9", "@babel/preset-typescript": "^7.27.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.1.10", "babel-preset-expo": "~54.0.0", "jest": "^29.7.0", "jest-expo": "~54.0.12", "react-native": "0.81.4", "react-test-renderer": "19.0.0", "typescript": "~5.9.2"}, "private": true}