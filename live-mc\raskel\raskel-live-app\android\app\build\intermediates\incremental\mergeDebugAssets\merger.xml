<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="io.agora.infra:aosl:1.2.13.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47a8c24638f7806a7d7949740d88a970\transformed\aosl-1.2.13.1\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47a8c24638f7806a7d7949740d88a970\transformed\aosl-1.2.13.1\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-av1-codec-dec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4d9641c210f9be34b7515035d8b90c78\transformed\full-video-av1-codec-dec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4d9641c210f9be34b7515035d8b90c78\transformed\full-video-av1-codec-dec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-av1-codec-enc:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3dae6a4cabb556ae0808dcaab442c95\transformed\full-video-av1-codec-enc-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3dae6a4cabb556ae0808dcaab442c95\transformed\full-video-av1-codec-enc-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-codec-dec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4fd0bc4d1a348ef958e55abc3160a9a5\transformed\full-video-codec-dec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4fd0bc4d1a348ef958e55abc3160a9a5\transformed\full-video-codec-dec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-codec-enc:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67013ada8d40c23f135a4cae1b3ec60c\transformed\full-video-codec-enc-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67013ada8d40c23f135a4cae1b3ec60c\transformed\full-video-codec-enc-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-voice-drive:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\871cf47407cf8dc1249fb74b735ac3ab\transformed\full-voice-drive-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\871cf47407cf8dc1249fb74b735ac3ab\transformed\full-voice-drive-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-face-capture:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a93c16f569432f4b7d0fb0bc65691921\transformed\full-face-capture-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a93c16f569432f4b7d0fb0bc65691921\transformed\full-face-capture-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-face-detect:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8af0240122297b32cfeda1bd179812f3\transformed\full-face-detect-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8af0240122297b32cfeda1bd179812f3\transformed\full-face-detect-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-vqa:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799ee7dcb1efc559d5d56638065b9294\transformed\full-vqa-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799ee7dcb1efc559d5d56638065b9294\transformed\full-vqa-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:aiaec-ll:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c5136930f9417c837c1099137c692f8\transformed\aiaec-ll-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c5136930f9417c837c1099137c692f8\transformed\aiaec-ll-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:aiaec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30deb1b687fbddf5338393a4bce767aa\transformed\aiaec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30deb1b687fbddf5338393a4bce767aa\transformed\aiaec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:spatial-audio:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4489de5ddeaa36a54534c73b55773983\transformed\spatial-audio-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4489de5ddeaa36a54534c73b55773983\transformed\spatial-audio-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-virtual-background:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5af46a4618534e09fdfe491394fc563e\transformed\full-virtual-background-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5af46a4618534e09fdfe491394fc563e\transformed\full-virtual-background-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:screen-capture:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b1b08ff95de0e1cd1dcbbe1cae19c3e3\transformed\screen-capture-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b1b08ff95de0e1cd1dcbbe1cae19c3e3\transformed\screen-capture-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-content-inspect:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3865c2c16ec5a7e07df6a8d3069e7058\transformed\full-content-inspect-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3865c2c16ec5a7e07df6a8d3069e7058\transformed\full-content-inspect-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:clear-vision:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd626e99e262b25795c754ac8b8c2825\transformed\clear-vision-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd626e99e262b25795c754ac8b8c2825\transformed\clear-vision-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:audio-beauty:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10d5afe10da867477c209167f6155891\transformed\audio-beauty-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10d5afe10da867477c209167f6155891\transformed\audio-beauty-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:ains-ll:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34e1e07fe2d26e3bb1cd9a723f962d79\transformed\ains-ll-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34e1e07fe2d26e3bb1cd9a723f962d79\transformed\ains-ll-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:ains:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0deec9455bf20b0529adda60023b5e34\transformed\ains-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0deec9455bf20b0529adda60023b5e34\transformed\ains-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-rtc-basic:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2edb3e0560fcd9ed0b85d3876a990ca8\transformed\full-rtc-basic-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2edb3e0560fcd9ed0b85d3876a990ca8\transformed\full-rtc-basic-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:iris-rtc:4.5.2-build.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfa89d7c2b589e03630064b12eb207dc\transformed\iris-rtc-4.5.2-build.1\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfa89d7c2b589e03630064b12eb207dc\transformed\iris-rtc-4.5.2-build.1\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-sdk:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fc304888e2ec161dee6b1e644b213bfa\transformed\full-sdk-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fc304888e2ec161dee6b1e644b213bfa\transformed\full-sdk-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="com.google.mlkit:barcode-scanning:17.3.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\836dcec7963b6cec7ad750d2129187af\transformed\barcode-scanning-17.3.0\assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\836dcec7963b6cec7ad750d2129187af\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\836dcec7963b6cec7ad750d2129187af\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\836dcec7963b6cec7ad750d2129187af\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-updates-interface\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-json-utils\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-manifests\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-client\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-constants\android\build\intermediates\assets\debug\mergeDebugAssets"><file name="app.config" path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-constants\android\build\intermediates\assets\debug\mergeDebugAssets\app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu-interface\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-worklets-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-worklets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-purchases" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-purchases\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-maps" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-maps\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-agora" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-vision-camera" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-safe-area-context\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>