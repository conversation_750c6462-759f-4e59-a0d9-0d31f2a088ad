C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\.cxx\Debug\3h292v2g\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\fbjni\fbjniConfig.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\.cxx\Debug\3h292v2g\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\fbjni\fbjniConfigVersion.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\.cxx\Debug\3h292v2g\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\react-native-worklets-core\react-native-worklets-coreConfig.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\.cxx\Debug\3h292v2g\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\ReactAndroid\ReactAndroidConfig.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\.cxx\Debug\3h292v2g\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\ReactAndroid\ReactAndroidConfigVersion.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\CMakeLists.txt