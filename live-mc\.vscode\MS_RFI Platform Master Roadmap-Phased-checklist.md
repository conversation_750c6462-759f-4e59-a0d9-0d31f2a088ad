MS.RFI Platform Master Roadmap & Phase Checklist
🌟 Project Overview
A next-gen construction streaming/social/mobile SaaS with AR, drone, AI, marketplace, and secure enterprise ad/commerce backend.
Stack: React Native (Expo/EAS), Node (API), Supabase (Postgres), modular Docker, and AI service integration.

Master Build/Feature Phases
[Phase 1] Rebrand & Domain Finalization

[Phase 2] Main Feed Core Features, Modularization, & Analytics

[Phase 3] Stripe (Payments), Pricing Logic, and Account Beta Gating

[Phase 4] Enterprise Ad System & Admin Partner Backend

[Phase 5] Live Streaming Page Enhancements (filters, audio, UI, music)

[Phase 6] ElevenLabs Voice/AI Integration

[Phase 7] DJI Drone/AR Scan SDK Integration

[Phase 8] Marketplace (Shop, Product Guidelines, E-commerce)

[Phase 9] Cloud Migration & Microservice Scaling

[Ongoing] Regression QA, Infra Docs, Rollback/Recovery

Progress Table
Phase	Status	Done?	Key Files/Links
Phase 1	Planned	☐	Spec
Phase 2	Planned	☐	
...			
[Phase 1 Spec] Rebrand & Domain Finalization
Goals
Migrate all app, backend, and integration code from “raskel”/placeholder to "Ms.RFI" and msrfi.com branding.

Prepare for secure production deployment and app store requirements.

Checklist
1. Repo/Config Code Changes
 Change project display name, icons, and splash screens to “Ms.RFI”.

 Update all endpoint references, config vars (API_URL, frontend, backend, Cloudinary, etc) to msrfi.com.

 Remove all references to "raskel" and old domains in code, docs, and CI/CD scripts.

 Update app.json/app.config.js (expo) for new name, slug, bundle IDs, schemes.

 Review and update privacy policy, TOS, legal links (match final brand/domain).

2. Auth & 3rd Party Services
 Register msrfi.com with Google and Apple OAuth developers panels.

 Update Google Auth (and any SSO/OAuth/SSO endpoints) to accept new domain.

 Update API keys/callbacks for Stripe, Cloudinary, analytics, push notifications, etc.

3. Backend/DB
 Point DB/API endpoints to the new base domain.

 Update Supabase settings (site URL, redirect URIs).

 Validate any webhook/notifications (e.g., Stripe, admin tools) still work with new URLs.

4. Staging/Testing
 Test all flows (login, registration, content upload, payment, ad click, etc.) on staging with new domain.

 Update .env, secrets, and deployment docs with new hostnames.

 Confirm all e-mail and notification links use msrfi.com.

5. Infra & DNS
 Point msrfi.com DNS to staging/production servers.

 Install/renew SSL on all new endpoints.

 Redirect any legacy/old URLs to new ones for backwards compatibility.

6. QA & Go-Live
 Full regression test: logins, live stream joins, ad loads, payments, etc.

 Run ExpoDev/EAS Build with all updated branding/config for testflight/internal QA.

 Archive old builds/branding for compliance record (optional, not required).

 Lock and cut a release branch for next phase.

Recovery
 Back up all code, DB, and EAS build artifacts before migration.

 (Have rollback plan: how to revert to prior domain/config in <1hr if needed.)