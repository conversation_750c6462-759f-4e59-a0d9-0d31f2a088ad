ninja: Entering directory `C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\.cxx\Debug\6c01v5s1\x86_64'
[0/2] Re-checking globbed directories...
[1/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformOp.cpp.o
[2/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/transforms/Quaternion.cpp.o
[3/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/transforms/vectors.cpp.o
[4/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/common.cpp.o
[5/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/steps.cpp.o
[6/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/cubicBezier.cpp.o
[7/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/linear.cpp.o
[8/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSTransitionConfig.cpp.o
[9/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp.o
[10/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/progress/RawProgressProvider.cpp.o
[11/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o
[12/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSNumber.cpp.o
[13/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSKeyword.cpp.o
[14/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSBoolean.cpp.o
[15/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/values/CSSDiscreteArray.cpp.o
[16/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSColor.cpp.o
[17/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/interpolation/PropertyInterpolator.cpp.o
[18/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSLength.cpp.o
[19/69] Building CXX object CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/GroupPropertiesInterpolator.cpp.o
[20/69] Building CXX object CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/ArrayPropertiesInterpolator.cpp.o
[21/69] Building CXX object CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/RecordPropertiesInterpolator.cpp.o
[22/69] Building CXX object CMakeFiles/reanimated.dir/927cb6a155b88f1ef245c13aab4c73ae/transforms/TransformOperationInterpolator.cpp.o
[23/69] Building CXX object CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/transforms/operations/matrix.cpp.o
[24/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp.o
[25/69] Building CXX object CMakeFiles/reanimated.dir/5e10170989c27bf941bfae90f134ce6a/interpolation/transforms/TransformsStyleInterpolator.cpp.o
[26/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/StaticPropsRegistry.cpp.o
[27/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/interpolation/InterpolatorFactory.cpp.o
[28/69] Building CXX object CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/transforms/TransformOperation.cpp.o
[29/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/algorithms.cpp.o
[30/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp.o
[31/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/keyframes.cpp.o
[32/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/svg/values/SVGStrokeDashArray.cpp.o
[33/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/svg/values/SVGLength.cpp.o
[34/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Tools/FeatureFlags.cpp.o
[35/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Tools/ReanimatedVersion.cpp.o
[36/69] Building CXX object CMakeFiles/reanimated.dir/280c933f74635b381103377f9c8a194d/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o
[37/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o
[38/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/interpolators.cpp.o
[39/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o
[40/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp.o
[41/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformMatrix2D.cpp.o
[42/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/NativeModules/PropValueProcessor.cpp.o
[43/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o
[44/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o
[45/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp.o
[46/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformMatrix3D.cpp.o
[47/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/configs/interpolators/registry.cpp.o
[48/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/core/CSSAnimation.cpp.o
[49/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/core/CSSTransition.cpp.o
[50/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSKeyframesConfig.cpp.o
[51/69] Building CXX object CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/styles/TransitionStyleInterpolator.cpp.o
[52/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSAnimationConfig.cpp.o
[53/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSKeyframesRegistry.cpp.o
[54/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSAnimationsRegistry.cpp.o
[55/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/progress/TransitionProgressProvider.cpp.o
[56/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/progress/AnimationProgressProvider.cpp.o
[57/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSTransitionsRegistry.cpp.o
[58/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/DelayedItemsManager.cpp.o
[59/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/svg/configs/init.cpp.o
[60/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o
[61/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/props.cpp.o
[62/69] Building CXX object CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o
[63/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/Fabric/updates/UpdatesRegistryManager.cpp.o
[64/69] Building CXX object CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/OnLoad.cpp.o
[65/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o
[66/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o
[67/69] Building CXX object CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/NativeProxy.cpp.o
[68/69] Building CXX object CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o
[69/69] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.so
