{"installationFolder": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-worklets-core", "packageInfo": {"packageName": "react-native-worklets-core", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "rnworklets", "moduleHeaders": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\headers\\rnworklets", "moduleExportLibraries": [], "abis": []}]}}