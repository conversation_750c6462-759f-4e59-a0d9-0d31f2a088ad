{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Reanimated", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "reanimated::@6890427a1f51a3e7e1df", "jsonFile": "target-reanimated-Debug-f6fcdafc6b061d8e0ea0.json", "name": "reanimated", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android"}, "version": {"major": 2, "minor": 3}}