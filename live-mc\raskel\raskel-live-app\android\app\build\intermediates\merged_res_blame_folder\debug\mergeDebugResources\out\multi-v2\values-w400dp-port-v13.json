{"logs": [{"outputFile": "com.fishkaster.app-mergeDebugResources-84:/values-w400dp-port-v13/values-w400dp-port-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\5deb70712b1e9df4e3892e963cadfc60\\transformed\\material-1.12.0\\res\\values-w400dp-port-v13\\values-w400dp-port-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,114,170,226,293,358,413,478", "endColumns": "58,55,55,66,64,54,64,68", "endOffsets": "109,165,221,288,353,408,473,542"}}]}]}