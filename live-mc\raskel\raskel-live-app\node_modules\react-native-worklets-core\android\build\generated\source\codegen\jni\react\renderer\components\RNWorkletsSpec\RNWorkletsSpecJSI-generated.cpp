/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNWorkletsSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeWorkletsCxxSpecJSI_install(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeWorkletsCxxSpecJSI *>(&turboModule)->install(
    rt
  );
}

NativeWorkletsCxxSpecJSI::NativeWorkletsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Worklets", jsInvoker) {
  methodMap_["install"] = MethodMetadata {0, __hostFunction_NativeWorkletsCxxSpecJSI_install};
}


} // namespace facebook::react
