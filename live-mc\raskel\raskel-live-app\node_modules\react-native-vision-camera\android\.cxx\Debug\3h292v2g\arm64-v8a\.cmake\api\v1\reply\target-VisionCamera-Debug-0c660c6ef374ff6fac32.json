{"artifacts": [{"path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-vision-camera/android/build/intermediates/cxx/Debug/3h292v2g/obj/arm64-v8a/libVisionCamera.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_definitions", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 26, "parent": 0}, {"command": 1, "file": 0, "line": 58, "parent": 0}, {"command": 1, "file": 0, "line": 68, "parent": 0}, {"command": 1, "file": 0, "line": 84, "parent": 0}, {"command": 2, "file": 0, "line": 15, "parent": 0}, {"command": 2, "file": 0, "line": 16, "parent": 0}, {"command": 2, "file": 0, "line": 19, "parent": 0}, {"command": 3, "file": 0, "line": 46, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -Wno-unused-variable -fstack-protector-all -fno-limit-debug-info  -fPIC"}], "defines": [{"backtrace": 5, "define": "EGL_EGLEXT_PROTOTYPES"}, {"backtrace": 6, "define": "GL_GLEXT_PROTOTYPES"}, {"backtrace": 7, "define": "VISION_CAMERA_ENABLE_FRAME_PROCESSORS=true"}, {"define": "VisionCamera_EXPORTS"}], "includes": [{"backtrace": 8, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-vision-camera/android/src/main/cpp"}, {"backtrace": 8, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-vision-camera/android/src/main/cpp/frameprocessors"}, {"backtrace": 8, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-vision-camera/android/src/main/cpp/frameprocessors/java-bindings"}, {"backtrace": 8, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon"}, {"backtrace": 8, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker"}, {"backtrace": 8, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/headers/rnworklets"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "VisionCamera::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\24\\liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\intermediates\\cxx\\Debug\\5t5f5h6s\\obj\\arm64-v8a\\librnworklets.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "VisionCamera", "nameOnDisk": "libVisionCamera.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/VisionCamera.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/MutableJByteBuffer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/FrameHostObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/FrameProcessorPluginHostObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/JSIJNIConversion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/VisionCameraProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/java-bindings/JSharedArray.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/java-bindings/JFrame.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/java-bindings/JFrameProcessor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/java-bindings/JFrameProcessorPlugin.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/java-bindings/JVisionCameraProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/frameprocessors/java-bindings/JVisionCameraScheduler.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}