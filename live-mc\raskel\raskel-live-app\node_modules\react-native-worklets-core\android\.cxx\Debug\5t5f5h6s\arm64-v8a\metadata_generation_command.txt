                        -HC:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\build\intermediates\cxx\Debug\5t5f5h6s\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\build\intermediates\cxx\Debug\5t5f5h6s\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\.cxx\Debug\5t5f5h6s\prefab\arm64-v8a\prefab
-BC:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\.cxx\Debug\5t5f5h6s\arm64-v8a
-GNinja
-DANDROID_STL=c++_shared
-DANDROID_TOOLCHAIN=clang
-DREACT_NATIVE_DIR=C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules/react-native
-DJS_RUNTIME=hermes
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
-DHERMES_ENABLE_DEBUGGER=1
                        Build command args: []
                        Version: 2