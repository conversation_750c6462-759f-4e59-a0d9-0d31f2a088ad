# ninja log v5
57	3272	7802456035377076	CMakeFiles/VisionCamera.dir/src/main/cpp/MutableJByteBuffer.cpp.o	7d42599bd0b6077e
88	3280	7802456035582318	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrame.cpp.o	d661b5282c00d5fa
115	3369	7802456036243167	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrameProcessorPlugin.cpp.o	d2b097bcc047419c
13	3731	7802456038916364	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/FrameProcessorPluginHostObject.cpp.o	9b384f749ece9dc4
17	1835	7802461728695666	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/VisionCameraProxy.cpp.o	134d0e4d6e0e1266
136	4002	7802456041955538	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JVisionCameraScheduler.cpp.o	f357f7633c22ecd7
8	1707	7802461727433770	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JSharedArray.cpp.o	35724a0f3f0b0f4e
12	1358	7802461723933948	CMakeFiles/VisionCamera.dir/src/main/cpp/VisionCamera.cpp.o	7bd01c112a3d4e13
41	4699	7802456049902439	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/FrameHostObject.cpp.o	fdbd89f7b2a1809f
23	1758	7802461727919470	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrameProcessor.cpp.o	277608688e6465d7
4	1888	7802461729231266	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/JSIJNIConversion.cpp.o	db154761ac1fef44
28	2032	7802461730637789	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JVisionCameraProxy.cpp.o	63960e5a4c8314b3
2033	2108	7802461731388499	../../../../build/intermediates/cxx/Debug/3h292v2g/obj/x86_64/libVisionCamera.so	3c47a85bbb0464b9
