# ninja log v5
88	3280	7802456035582318	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrame.cpp.o	d661b5282c00d5fa
57	3272	7802456035377076	CMakeFiles/VisionCamera.dir/src/main/cpp/MutableJByteBuffer.cpp.o	7d42599bd0b6077e
115	3369	7802456036243167	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrameProcessorPlugin.cpp.o	d2b097bcc047419c
76	5051	7802456053277444	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/VisionCameraProxy.cpp.o	134d0e4d6e0e1266
13	3731	7802456038916364	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/FrameProcessorPluginHostObject.cpp.o	9b384f749ece9dc4
107	4909	7802456051755326	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JSharedArray.cpp.o	35724a0f3f0b0f4e
136	4002	7802456041955538	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JVisionCameraScheduler.cpp.o	f357f7633c22ecd7
49	4629	7802456049006073	CMakeFiles/VisionCamera.dir/src/main/cpp/VisionCamera.cpp.o	7bd01c112a3d4e13
41	4699	7802456049902439	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/FrameHostObject.cpp.o	fdbd89f7b2a1809f
98	5094	7802456053768241	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrameProcessor.cpp.o	277608688e6465d7
66	5126	7802456054168112	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/JSIJNIConversion.cpp.o	db154761ac1fef44
125	6004	7802456062821759	CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JVisionCameraProxy.cpp.o	63960e5a4c8314b3
6004	6403	7802456065279965	../../../../build/intermediates/cxx/Debug/3h292v2g/obj/x86_64/libVisionCamera.so	3c47a85bbb0464b9
