ninja: Entering directory `C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\.cxx\Debug\335l6r1b\x86_64'
[1/49] Building CXX object src/fabric/CMakeFiles/fabric.dir/cmake_pch.hxx.pch
[2/49] Building CXX object CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch
[3/49] Building CXX object CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/NativeModule.cpp.o
[4/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o
[5/49] Building CXX object CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o
[6/49] Building CXX object CMakeFiles/expo-modules-core.dir/e6b18d4f51e54e10ca0ead7d868a78dd/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o
[7/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o
[8/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o
[9/49] Building CXX object src/fabric/CMakeFiles/fabric.dir/8cdb0914a492ff61de3267e4cb77f99d/common/cpp/fabric/ExpoViewEventEmitter.cpp.o
[10/49] Building CXX object CMakeFiles/expo-modules-core.dir/e6b18d4f51e54e10ca0ead7d868a78dd/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o
[11/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o
[12/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o
[13/49] Building CXX object CMakeFiles/expo-modules-core.dir/e6b18d4f51e54e10ca0ead7d868a78dd/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o
[14/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o
[15/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o
[16/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o
[17/49] Building CXX object CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/SharedObject.cpp.o
[18/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o
[19/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o
[20/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o
[21/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o
[22/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o
[23/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o
[24/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o
[25/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o
[26/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o
[27/49] Building CXX object CMakeFiles/expo-modules-core.dir/e6b18d4f51e54e10ca0ead7d868a78dd/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o
[28/49] Building CXX object src/fabric/CMakeFiles/fabric.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/fabric/ExpoViewProps.cpp.o
[29/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o
[30/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o
[31/49] Building CXX object src/fabric/CMakeFiles/fabric.dir/8cdb0914a492ff61de3267e4cb77f99d/common/cpp/fabric/ExpoViewShadowNode.cpp.o
[32/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o
[33/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o
[34/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o
[35/49] Building CXX object src/fabric/CMakeFiles/fabric.dir/FabricComponentsRegistry.cpp.o
[36/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o
[37/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o
[38/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o
[39/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o
[40/49] Building CXX object CMakeFiles/expo-modules-core.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/expo-modules-core/common/cpp/EventEmitter.cpp.o
[41/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o
[42/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o
[43/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o
[44/49] Building CXX object src/fabric/CMakeFiles/fabric.dir/8cdb0914a492ff61de3267e4cb77f99d/common/cpp/fabric/ExpoViewComponentDescriptor.cpp.o
[45/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o
[46/49] Linking CXX static library src\fabric\libfabric.a
[47/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o
[48/49] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o
[49/49] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\335l6r1b\obj\x86_64\libexpo-modules-core.so
