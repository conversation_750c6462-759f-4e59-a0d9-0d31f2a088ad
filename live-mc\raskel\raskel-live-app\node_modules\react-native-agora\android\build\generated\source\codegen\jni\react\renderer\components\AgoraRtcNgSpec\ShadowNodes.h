
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/AgoraRtcNgSpec/EventEmitters.h>
#include <react/renderer/components/AgoraRtcNgSpec/Props.h>
#include <react/renderer/components/AgoraRtcNgSpec/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char AgoraRtcSurfaceViewComponentName[];

/*
 * `ShadowNode` for <AgoraRtcSurfaceView> component.
 */
using AgoraRtcSurfaceViewShadowNode = ConcreteViewShadowNode<
    AgoraRtcSurfaceViewComponentName,
    AgoraRtcSurfaceViewProps,
    AgoraRtcSurfaceViewEventEmitter,
    AgoraRtcSurfaceViewState>;

JSI_EXPORT extern const char AgoraRtcTextureViewComponentName[];

/*
 * `ShadowNode` for <AgoraRtcTextureView> component.
 */
using AgoraRtcTextureViewShadowNode = ConcreteViewShadowNode<
    AgoraRtcTextureViewComponentName,
    AgoraRtcTextureViewProps,
    AgoraRtcTextureViewEventEmitter,
    AgoraRtcTextureViewState>;

} // namespace facebook::react
