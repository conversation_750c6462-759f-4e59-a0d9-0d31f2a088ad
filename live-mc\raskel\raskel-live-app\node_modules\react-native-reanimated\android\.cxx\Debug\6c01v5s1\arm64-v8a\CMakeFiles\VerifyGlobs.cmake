# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# REANIMATED_COMMON_CPP_SOURCES at CMakeLists.txt:38 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/transforms/Quaternion.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/transforms/TransformMatrix2D.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/transforms/TransformMatrix3D.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/transforms/TransformOp.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/transforms/vectors.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/values/CSSBoolean.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/values/CSSColor.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/values/CSSDiscreteArray.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/values/CSSKeyword.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/values/CSSLength.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/common/values/CSSNumber.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/configs/CSSAnimationConfig.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/configs/CSSKeyframesConfig.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/configs/CSSTransitionConfig.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/configs/common.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/configs/interpolators/registry.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/core/CSSAnimation.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/core/CSSTransition.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/easing/cubicBezier.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/easing/linear.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/easing/steps.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/InterpolatorFactory.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/PropertyInterpolator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/groups/ArrayPropertiesInterpolator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/groups/GroupPropertiesInterpolator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/groups/RecordPropertiesInterpolator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/styles/TransitionStyleInterpolator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/transforms/TransformOperation.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/transforms/TransformOperationInterpolator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/transforms/TransformsStyleInterpolator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/interpolation/transforms/operations/matrix.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/progress/AnimationProgressProvider.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/progress/RawProgressProvider.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/progress/TransitionProgressProvider.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/registries/CSSAnimationsRegistry.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/registries/CSSKeyframesRegistry.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/registries/CSSTransitionsRegistry.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/registries/StaticPropsRegistry.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/svg/configs/init.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/svg/values/SVGLength.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/svg/values/SVGStrokeDashArray.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/utils/DelayedItemsManager.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/utils/algorithms.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/utils/interpolators.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/utils/keyframes.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/CSS/utils/props.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/updates/UpdatesRegistryManager.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/PropValueProcessor.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Tools/FeatureFlags.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Tools/ReanimatedVersion.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# REANIMATED_ANDROID_CPP_SOURCES at CMakeLists.txt:40 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp"
  "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()
