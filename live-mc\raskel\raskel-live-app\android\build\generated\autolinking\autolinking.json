{"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app", "reactNativePath": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "expo": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-maps": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-maps", "name": "react-native-maps", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-maps\\android", "packageImportPath": "import com.rnmaps.maps.MapsPackage;", "packageInstance": "new MapsPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-maps/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-purchases": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-purchases", "name": "react-native-purchases", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-purchases\\android", "packageImportPath": "import com.revenuecat.purchases.react.RNPurchasesPackage;", "packageInstance": "new RNPurchasesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-purchases/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-reanimated": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor", "RNSBottomTabsComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-vision-camera": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-vision-camera", "name": "react-native-vision-camera", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-vision-camera\\android", "packageImportPath": "import com.mrousavy.camera.react.CameraPackage;", "packageInstance": "new CameraPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-vision-camera/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-worklets": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets", "name": "react-native-worklets", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android", "packageImportPath": "import com.swmansion.worklets.WorkletsPackage;", "packageInstance": "new WorkletsPackage()", "buildTypes": [], "libraryName": "rnworklets", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-worklets-core": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core", "name": "react-native-worklets-core", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android", "packageImportPath": "import com.worklets.WorkletsCorePackage;", "packageInstance": "new WorkletsCorePackage()", "buildTypes": [], "libraryName": "RNWorkletsSpec", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-fs": {"root": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-fs", "name": "react-native-fs", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-fs\\android", "packageImportPath": "import com.rnfs.RNFSPackage;", "packageInstance": "new RNFSPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-fs/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}}, "project": {"android": {"packageName": "com.fishkaster.app", "sourceDir": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android"}}}