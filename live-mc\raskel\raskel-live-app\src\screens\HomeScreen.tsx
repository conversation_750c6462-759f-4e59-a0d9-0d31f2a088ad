import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, ActivityIndicator, RefreshControl, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { useAgoraSimple } from '../hooks/useAgoraSimple';
import { streamService } from '../services/api';

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

type Stream = {
  id: string;
  title: string;
  description: string;
  channel_name: string;
  host_id: string;
  host_name: string;
  is_live: boolean;
  viewer_count: number;
  created_at: string;
};

const HomeScreen = () => {
  const [streams, setStreams] = useState<Stream[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user, signOut } = useAuth();
  
  // Add Agora hook for debugging
  const { initializeEngine, error: agoraError, engineRef } = useAgoraSimple();

  const fetchStreams = async () => {
    try {
      setLoading(true);
      console.log('🔍 HomeScreen: Starting to fetch streams...');
      
      // Fetch real streams from Supabase
      const response = await streamService.getLiveStreams();
      
      if (response.error) {
        console.error('❌ HomeScreen: Error fetching streams:', response.error.message);
        
        // Check if it's a database setup issue
        if (response.error.message.includes('relation') && response.error.message.includes('does not exist')) {
          console.log('🔧 HomeScreen: Database tables not found - Supabase setup required');
          console.log('📋 HomeScreen: Please run setup-supabase.sql in your Supabase dashboard');
        } else if (response.error.message.includes('refresh token') || response.error.message.includes('JWT')) {
          console.log('🔐 HomeScreen: Authentication issue detected');
        } else {
          console.log('🌐 HomeScreen: Network or API error');
        }
        
        console.log('🔄 HomeScreen: Falling back to mock data due to API error');
        
        // Enhanced mock data for better testing experience
        const mockStreams: Stream[] = [
          {
            id: 'mock-1',
            title: '🎣 Bass Fishing Tournament Live',
            description: 'Live from Lake Michigan - Bass fishing competition',
            channel_name: 'bass_tournament_live',
            host_id: '00000000-0000-4000-8000-123456789abc',
            host_name: 'Captain Mike',
            is_live: true,
            viewer_count: 156,
            created_at: new Date().toISOString(),
          },
          {
            id: 'mock-2',
            title: '🐟 Fish Species Identification',
            description: 'Learn to identify different fish species with AI',
            channel_name: 'fish_id_channel',
            host_id: '00000000-0000-4000-8000-123456789abc',
            host_name: 'Dr. Sarah Fisher',
            is_live: true,
            viewer_count: 89,
            created_at: new Date().toISOString(),
          },
          {
            id: 'mock-3',
            title: '🌊 Deep Sea Fishing Adventure',
            description: 'Join us for an epic deep sea fishing expedition',
            channel_name: 'deep_sea_live',
            host_id: '00000000-0000-4000-8000-123456789abc',
            host_name: 'Captain Rodriguez',
            is_live: true,
            viewer_count: 234,
            created_at: new Date().toISOString(),
          },
        ];
        console.log('✅ HomeScreen: Set', mockStreams.length, 'mock streams (Supabase connection failed)');
        setStreams(mockStreams);
      } else if (response.data && response.data.length > 0) {
        console.log('✅ HomeScreen: Fetched', response.data.length, 'real live streams from database');
        setStreams(response.data);
      } else {
        console.log('⚠️ HomeScreen: No live streams in database, showing mock data instead');
        
        // Show mock data when database is empty (better UX for development/testing)
        const mockStreams: Stream[] = [
          {
            id: 'dev-1',
            title: '🎣 Morning Fishing Session',
            description: 'Early morning fishing at the local lake',
            channel_name: 'morning_fishing_live',
            host_id: '00000000-0000-4000-8000-123456789abc',
            host_name: 'Jake Angler',
            is_live: true,
            viewer_count: 156,
            created_at: new Date().toISOString(),
          },
          {
            id: 'dev-2',
            title: '🎯 Fishing Gear Reviews',
            description: 'Testing the latest fishing rods and tackle',
            channel_name: 'gear_review_channel',
            host_id: '00000000-0000-4000-8000-123456789abc',
            host_name: 'Emma TackleMaster',
            is_live: true,
            viewer_count: 89,
            created_at: new Date().toISOString(),
          },
          {
            id: 'dev-3',
            title: '🌅 Sunrise Fly Fishing',
            description: 'Peaceful fly fishing in mountain streams',
            channel_name: 'fly_fishing_live',
            host_id: '00000000-0000-4000-8000-123456789abc',
            host_name: 'River Guide Tom',
            is_live: true,
            viewer_count: 234,
            created_at: new Date().toISOString(),
          },
        ];
        console.log('✅ HomeScreen: Set', mockStreams.length, 'mock streams for development');
        setStreams(mockStreams);
      }
    } catch (error) {
      console.error('❌ HomeScreen: Exception while fetching streams:', error);
      
      // More specific error handling
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('Network request failed')) {
        console.log('🌐 HomeScreen: Network connectivity issue - check internet connection');
      } else if (errorMessage.includes('fetch')) {
        console.log('🔌 HomeScreen: API endpoint unreachable - check Supabase configuration');
      } else {
        console.log('🐛 HomeScreen: Unexpected error:', errorMessage);
      }
      
      console.log('🔄 HomeScreen: Falling back to mock data due to exception');
      
      // Use mock data as fallback for any exception
      const mockStreams: Stream[] = [
        {
          id: 'fallback-1',
          title: '🎣 Demo Fishing Stream (Offline Mode)',
          description: 'This is a fallback fishing stream - check your connection',
          channel_name: 'emergency_fishing_demo',
          host_id: '00000000-0000-4000-8000-123456789abc',
          host_name: 'Demo Angler',
          is_live: true,
          viewer_count: 1,
          created_at: new Date().toISOString(),
        },
      ];
      setStreams(mockStreams);
    } finally {
      setLoading(false);
      setRefreshing(false);
      console.log('🏁 HomeScreen: Fetch streams completed');
    }
  };

  useEffect(() => {
    fetchStreams();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    fetchStreams();
  };

  // Debug function to test Agora initialization
  const testAgoraInit = async () => {
    try {
      console.log('🧪 Manual Agora test started...');
      await initializeEngine();
      Alert.alert('Success!', `Agora engine initialized successfully!\nEngine: ${engineRef.current ? 'Available' : 'Not Available'}`);
    } catch (error) {
      console.error('❌ Manual Agora test failed:', error);
      Alert.alert('Error', `Agora initialization failed:\n${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleCreateStream = () => {
    navigation.navigate('CreateStream');
  };

  const handleStartStream = () => {
    navigation.navigate('StartStream');
  };

  const handleAIStreamCreator = () => {
    navigation.navigate('AIStreamCreator');
  };

  const handleViewStream = (stream: Stream) => {
    navigation.navigate('ViewStream', {
      streamId: stream.id,
      channelName: stream.channel_name,
      hostUid: stream.host_id,
    });
  };

  const handleProfile = () => {
    navigation.navigate('Profile');
  };

  const handleDashboard = () => {
    navigation.navigate('Dashboard');
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Fish Kaster</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity style={styles.dashboardButton} onPress={handleDashboard}>
            <Text style={styles.dashboardButtonText}>🎯 Dashboard</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.profileButton} onPress={handleProfile}>
            <Text style={styles.profileButtonText}>Profile</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.streamHeader}>
          <Text style={styles.streamHeaderTitle}>Live Streams</Text>
          <View style={styles.createButtons}>
            <TouchableOpacity style={styles.aiButton} onPress={handleAIStreamCreator}>
              <Text style={styles.aiButtonText}>🤖 AI Creator</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.createButton} onPress={handleStartStream}>
              <Text style={styles.createButtonText}>🔴 Go Live</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.debugButton} onPress={testAgoraInit}>
              <Text style={styles.debugButtonText}>🧪 Test</Text>
            </TouchableOpacity>
          </View>
        </View>

        {loading ? (
          <ActivityIndicator size="large" color="#4a90e2" style={styles.loader} />
        ) : streams.length > 0 ? (
          <FlatList
            data={streams}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.streamCard}
                onPress={() => handleViewStream(item)}
              >
                <View style={styles.streamInfo}>
                  <Text style={styles.streamTitle}>{item.title}</Text>
                  <Text style={styles.streamDescription}>{item.description}</Text>
                  <View style={styles.streamMeta}>
                    <Text style={styles.hostName}>{item.host_name}</Text>
                    <View style={styles.viewerCount}>
                      <Text style={styles.viewerCountText}>{item.viewer_count} viewers</Text>
                    </View>
                  </View>
                </View>
                {item.is_live && <View style={styles.liveIndicator}><Text style={styles.liveText}>LIVE</Text></View>}
              </TouchableOpacity>
            )}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No live streams available</Text>
            <Text style={styles.emptySubText}>Be the first to go live!</Text>
            <TouchableOpacity style={styles.emptyButton} onPress={handleCreateStream}>
              <Text style={styles.emptyButtonText}>Start Streaming</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingTop: 50,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  dashboardButton: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#2E7DFF',
  },
  dashboardButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  profileButton: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  profileButtonText: {
    color: '#333',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  streamHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  streamHeaderTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  createButton: {
    backgroundColor: '#4a90e2',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  streamCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  streamInfo: {
    flex: 1,
  },
  streamTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  streamDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  streamMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  hostName: {
    fontSize: 12,
    color: '#888',
  },
  viewerCount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewerCountText: {
    fontSize: 12,
    color: '#888',
  },
  liveIndicator: {
    backgroundColor: '#ff4d4f',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  liveText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  emptyButton: {
    backgroundColor: '#4a90e2',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  createButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  aiButton: {
    backgroundColor: '#9c27b0',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  aiButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  debugButton: {
    backgroundColor: '#ff9800',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  debugButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
});

export default HomeScreen;