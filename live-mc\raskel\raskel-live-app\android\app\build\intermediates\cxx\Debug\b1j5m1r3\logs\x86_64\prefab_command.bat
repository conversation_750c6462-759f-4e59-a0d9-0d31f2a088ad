@echo off
"C:\\Users\\<USER>\\AppData\\Local\\jdk-17\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86_64 ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging6609213979034906831\\staged-cli-output" ^
  "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-vision-camera\\6l1e102j" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab" ^
  "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\4j154d3w" ^
  "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-worklets\\95l2j6o6" ^
  "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-worklets-core\\3n3s634p" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3cefa1ac864c6b2d83e4f85211b38ee6\\transformed\\hermes-android-0.81.4-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab"
