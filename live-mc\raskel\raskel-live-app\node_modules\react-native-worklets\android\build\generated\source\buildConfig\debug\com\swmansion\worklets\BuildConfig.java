/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.swmansion.worklets;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String LIBRARY_PACKAGE_NAME = "com.swmansion.worklets";
  public static final String BUILD_TYPE = "debug";
  // Field from default config.
  public static final boolean BUNDLE_MODE = false;
  // Field from default config.
  public static final int EXOPACKAGE_FLAGS = 0;
  // Field from default config.
  public static final boolean IS_INTERNAL_BUILD = false;
  // Field from default config.
  public static final int REACT_NATIVE_MINOR_VERSION = 81;
}
