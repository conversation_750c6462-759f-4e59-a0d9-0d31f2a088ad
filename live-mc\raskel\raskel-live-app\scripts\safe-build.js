#!/usr/bin/env node

/**
 * Safe Build Script for Fish Kaster App
 * Performs pre-build checks to prevent common build failures
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROJECT_ROOT = path.join(__dirname, '..');
const ANDROID_DIR = path.join(PROJECT_ROOT, 'android');

console.log('🔍 Running pre-build safety checks...\n');

/**
 * Check if file exists
 */
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

/**
 * Read file content safely
 */
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    return null;
  }
}

/**
 * Check Android configuration consistency
 */
function checkAndroidConfig() {
  console.log('📱 Checking Android configuration...');
  
  const appJson = JSON.parse(readFile(path.join(PROJECT_ROOT, 'app.json')));
  const buildGradle = readFile(path.join(ANDROID_DIR, 'app', 'build.gradle'));
  const settingsGradle = readFile(path.join(ANDROID_DIR, 'settings.gradle'));
  
  const expectedPackage = appJson.expo.android.package;
  const expectedName = appJson.expo.name;
  
  // Check package name consistency
  if (!buildGradle.includes(`applicationId '${expectedPackage}'`)) {
    console.error(`❌ Package name mismatch in build.gradle. Expected: ${expectedPackage}`);
    return false;
  }
  
  // Check project name consistency
  if (!settingsGradle.includes(`rootProject.name = '${expectedName}'`)) {
    console.error(`❌ Project name mismatch in settings.gradle. Expected: ${expectedName}`);
    return false;
  }
  
  console.log('✅ Android configuration is consistent');
  return true;
}

/**
 * Check NDK configuration
 */
function checkNDKConfig() {
  console.log('🔧 Checking NDK configuration...');
  
  const gradleProperties = readFile(path.join(ANDROID_DIR, 'gradle.properties'));
  
  if (!gradleProperties.includes('android.ndkVersion=')) {
    console.error('❌ NDK version not specified in gradle.properties');
    return false;
  }
  
  console.log('✅ NDK configuration looks good');
  return true;
}

/**
 * Check Vision Camera configuration
 */
function checkVisionCameraConfig() {
  console.log('📷 Checking Vision Camera configuration...');
  
  const appJson = JSON.parse(readFile(path.join(PROJECT_ROOT, 'app.json')));
  const packageJson = JSON.parse(readFile(path.join(PROJECT_ROOT, 'package.json')));
  
  // Check if Vision Camera is in dependencies
  if (!packageJson.dependencies['react-native-vision-camera']) {
    console.error('❌ react-native-vision-camera not found in dependencies');
    return false;
  }
  
  // Check if Vision Camera plugin is configured
  const hasVisionCameraPlugin = appJson.expo.plugins.some(plugin => 
    Array.isArray(plugin) ? plugin[0] === 'react-native-vision-camera' : plugin === 'react-native-vision-camera'
  );
  
  if (!hasVisionCameraPlugin) {
    console.error('❌ react-native-vision-camera plugin not configured in app.json');
    return false;
  }
  
  console.log('✅ Vision Camera configuration looks good');
  return true;
}

/**
 * Check environment configuration
 */
function checkEnvironmentConfig() {
  console.log('🌍 Checking environment configuration...');
  
  const envPath = path.join(PROJECT_ROOT, 'server', '.env');
  if (!fileExists(envPath)) {
    console.error('❌ .env file not found in server directory');
    return false;
  }
  
  const envContent = readFile(envPath);
  const requiredVars = [
    'AGORA_APP_ID',
    'AGORA_APP_CERTIFICATE',
    'SUPABASE_URL',
    'SUPABASE_KEY'
  ];
  
  for (const varName of requiredVars) {
    if (!envContent.includes(`${varName}=`)) {
      console.error(`❌ Required environment variable ${varName} not found`);
      return false;
    }
  }
  
  console.log('✅ Environment configuration looks good');
  return true;
}

/**
 * Check node modules and dependencies
 */
function checkDependencies() {
  console.log('📦 Checking dependencies...');
  
  if (!fileExists(path.join(PROJECT_ROOT, 'node_modules'))) {
    console.error('❌ node_modules not found. Run: npm install');
    return false;
  }
  
  // Check for common problematic dependencies
  const packageJson = JSON.parse(readFile(path.join(PROJECT_ROOT, 'package.json')));
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  // Check for worklets-core compatibility
  if (deps['react-native-worklets-core'] && deps['react-native-vision-camera']) {
    console.log('⚠️  Both worklets-core and vision-camera detected. Ensure compatibility.');
  }
  
  console.log('✅ Dependencies look good');
  return true;
}

/**
 * Clean build artifacts
 */
function cleanBuildArtifacts() {
  console.log('🧹 Cleaning build artifacts...');
  
  const cleanPaths = [
    path.join(ANDROID_DIR, 'build'),
    path.join(ANDROID_DIR, 'app', 'build'),
    path.join(PROJECT_ROOT, '.expo'),
  ];
  
  cleanPaths.forEach(cleanPath => {
    if (fileExists(cleanPath)) {
      try {
        execSync(`rm -rf "${cleanPath}"`, { stdio: 'inherit' });
        console.log(`✅ Cleaned: ${cleanPath}`);
      } catch (error) {
        console.log(`⚠️  Could not clean: ${cleanPath}`);
      }
    }
  });
}

/**
 * Main execution
 */
async function main() {
  const checks = [
    checkAndroidConfig,
    checkNDKConfig,
    checkVisionCameraConfig,
    checkEnvironmentConfig,
    checkDependencies,
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    if (!check()) {
      allPassed = false;
    }
    console.log('');
  }
  
  if (!allPassed) {
    console.error('❌ Some checks failed. Please fix the issues above before building.');
    process.exit(1);
  }
  
  console.log('🎉 All pre-build checks passed!');
  
  // Ask if user wants to clean build artifacts
  const shouldClean = process.argv.includes('--clean');
  if (shouldClean) {
    cleanBuildArtifacts();
  }
  
  console.log('\n🚀 Ready to build! Run one of these commands:');
  console.log('  npm run android        # Build and run on Android');
  console.log('  npm run ios            # Build and run on iOS');
  console.log('  npx expo run:android   # Direct Expo build');
  console.log('\n💡 Tip: Use --clean flag to clean build artifacts first');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
