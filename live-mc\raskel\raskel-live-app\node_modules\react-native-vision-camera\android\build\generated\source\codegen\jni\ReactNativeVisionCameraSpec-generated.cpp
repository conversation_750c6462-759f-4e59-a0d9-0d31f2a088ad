
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "ReactNativeVisionCameraSpec.h"

namespace facebook::react {



std::shared_ptr<TurboModule> ReactNativeVisionCameraSpec_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {

  return nullptr;
}

} // namespace facebook::react
