ninja: Entering directory `C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets\android\.cxx\Debug\2q11c611\x86_64'
[0/2] Re-checking globbed directories...
[1/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Resources/ValueUnpacker.cpp.o
[2/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Resources/SynchronizableUnpacker.cpp.o
[3/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/RuntimeData.cpp.o
[4/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/SharedItems/SynchronizableAccess.cpp.o
[5/33] Building CXX object CMakeFiles/worklets.dir/src/main/cpp/worklets/android/PlatformLogger.cpp.o
[6/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/FeatureFlags.cpp.o
[7/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o
[8/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o
[9/33] Building CXX object CMakeFiles/worklets.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/react-native-worklets/Common/cpp/worklets/Tools/JSLogger.cpp.o
[10/33] Building CXX object CMakeFiles/worklets.dir/e751c91732a5ab9c8eacba856b5cb7bb/cpp/worklets/AnimationFrameQueue/AnimationFrameBatchinator.cpp.o
[11/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/UIScheduler.cpp.o
[12/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/UIRuntimeDecorator.cpp.o
[13/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/SharedItems/Synchronizable.cpp.o
[14/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o
[15/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/JSScheduler.cpp.o
[16/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/JSISerializer.cpp.o
[17/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/RunLoop/AsyncQueueImpl.cpp.o
[18/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/RunLoop/EventLoop.cpp.o
[19/33] Building CXX object CMakeFiles/worklets.dir/src/main/cpp/worklets/android/AndroidUIScheduler.cpp.o
[20/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/WorkletHermesRuntime.cpp.o
[21/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/SharedItems/Serializable.cpp.o
[22/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/VersionUtils.cpp.o
[23/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o
[24/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o
[25/33] Building CXX object CMakeFiles/worklets.dir/e751c91732a5ab9c8eacba856b5cb7bb/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o
[26/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/RuntimeManager.cpp.o
[27/33] Building CXX object CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsOnLoad.cpp.o
[28/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o
[29/33] Building CXX object CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsModule.cpp.o
[30/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o
[31/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o
[32/33] Building CXX object CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp.o
[33/33] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\2q11c611\obj\x86_64\libworklets.so
