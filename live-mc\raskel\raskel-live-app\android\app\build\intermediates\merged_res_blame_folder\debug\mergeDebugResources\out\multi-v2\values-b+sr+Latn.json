{"logs": [{"outputFile": "com.fishkaster.app-mergeDebugResources-84:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\02bec446bb5b07b8548f83ec26b6ae83\\transformed\\appcompat-1.7.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1136,1243,1344,1450,1536,1640,1762,1847,1929,2020,2113,2208,2302,2402,2495,2590,2695,2786,2877,2963,3068,3174,3277,3384,3493,3600,3770,17841", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "1238,1339,1445,1531,1635,1757,1842,1924,2015,2108,2203,2297,2397,2490,2585,2690,2781,2872,2958,3063,3169,3272,3379,3488,3595,3765,3862,17923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\72fd1eaed4249ec9561f48df05200d45\\transformed\\play-services-basement-18.4.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "79", "startColumns": "4", "startOffsets": "6607", "endColumns": "129", "endOffsets": "6732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\29b62c1e8951214205c1999e10893e42\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,91", "endOffsets": "140,230,322"}, "to": {"startLines": "53,230,231", "startColumns": "4,4,4", "startOffsets": "3867,18928,19018", "endColumns": "89,89,91", "endOffsets": "3952,19013,19105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\30f51c8a36c85c07b7e3abc1db11ccb6\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,293,380,477,578,664,741,832,924,1009,1089,1174,1247,1337,1414,1493,1570,1649,1719", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "288,375,472,573,659,736,827,919,1004,1084,1169,1242,1332,1409,1488,1565,1644,1714,1832"}, "to": {"startLines": "69,70,90,91,92,151,152,212,213,215,216,220,222,223,224,225,227,228,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5426,5523,7896,7993,8094,12555,12632,17419,17511,17676,17756,18087,18237,18327,18404,18483,18661,18740,18810", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "5518,5605,7988,8089,8175,12627,12718,17506,17591,17751,17836,18155,18322,18399,18478,18555,18735,18805,18923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cf4e6ff07dbeac5bcd8d8e9bb606754a\\transformed\\exoplayer-core-2.18.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10289,10364,10425,10490,10562,10641,10714,10802,10886", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "10359,10420,10485,10557,10636,10709,10797,10881,10956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\1ff7e39d1dce962b72e0e33d3ff513a9\\transformed\\play-services-base-18.5.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "71,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5610,5717,5875,6002,6112,6266,6393,6505,6737,6886,6993,7153,7280,7429,7572,7640,7705", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "5712,5870,5997,6107,6261,6388,6500,6602,6881,6988,7148,7275,7424,7567,7635,7700,7780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3ef9533b8d5a0884be92f5c6e6c0dbd2\\transformed\\exoplayer-ui-2.18.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1946,2060,2171,2247,2335,2409,2480,2572,2665,2732,2797,2850,2908,2956,3017,3083,3147,3210,3275,3339,3400,3466,3531,3597,3649,3711,3787,3863", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1941,2055,2166,2242,2330,2404,2475,2567,2660,2727,2792,2845,2903,2951,3012,3078,3142,3205,3270,3334,3395,3461,3526,3592,3644,3706,3782,3858,3914"}, "to": {"startLines": "2,11,16,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,651,8309,8391,8474,8556,8645,8736,8806,8873,8967,9062,9130,9194,9257,9329,9438,9552,9663,9739,9827,9901,9972,10064,10157,10224,10961,11014,11072,11120,11181,11247,11311,11374,11439,11503,11564,11630,11695,11761,11813,11875,11951,12027", "endLines": "10,15,20,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "377,646,907,8386,8469,8551,8640,8731,8801,8868,8962,9057,9125,9189,9252,9324,9433,9547,9658,9734,9822,9896,9967,10059,10152,10219,10284,11009,11067,11115,11176,11242,11306,11369,11434,11498,11559,11625,11690,11756,11808,11870,11946,12022,12078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8aa23bce709bfe96db10568360097556\\transformed\\core-1.15.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "59,60,61,62,63,64,65,226", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4393,4491,4593,4690,4794,4898,5003,18560", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4486,4588,4685,4789,4893,4998,5114,18656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\5deb70712b1e9df4e3892e963cadfc60\\transformed\\material-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1130,1196,1289,1357,1420,1523,1583,1649,1705,1776,1836,1890,2002,2059,2120,2174,2250,2375,2462,2539,2632,2716,2799,2938,3020,3103,3234,3322,3400,3454,3510,3576,3650,3728,3799,3881,3957,4033,4108,4180,4287,4377,4450,4542,4638,4710,4786,4882,4935,5017,5084,5171,5258,5320,5384,5447,5516,5621,5731,5827,5935,5993,6053,6133,6216,6292", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "319,396,473,553,661,755,849,981,1062,1125,1191,1284,1352,1415,1518,1578,1644,1700,1771,1831,1885,1997,2054,2115,2169,2245,2370,2457,2534,2627,2711,2794,2933,3015,3098,3229,3317,3395,3449,3505,3571,3645,3723,3794,3876,3952,4028,4103,4175,4282,4372,4445,4537,4633,4705,4781,4877,4930,5012,5079,5166,5253,5315,5379,5442,5511,5616,5726,5822,5930,5988,6048,6128,6211,6287,6364"}, "to": {"startLines": "21,54,55,56,57,58,66,67,68,93,94,146,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,214,218,219,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,3957,4034,4111,4191,4299,5119,5213,5345,8180,8243,12083,12487,12723,12786,12889,12949,13015,13071,13142,13202,13256,13368,13425,13486,13540,13616,13741,13828,13905,13998,14082,14165,14304,14386,14469,14600,14688,14766,14820,14876,14942,15016,15094,15165,15247,15323,15399,15474,15546,15653,15743,15816,15908,16004,16076,16152,16248,16301,16383,16450,16537,16624,16686,16750,16813,16882,16987,17097,17193,17301,17359,17596,17928,18011,18160", "endLines": "25,54,55,56,57,58,66,67,68,93,94,146,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,214,218,219,221", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "1131,4029,4106,4186,4294,4388,5208,5340,5421,8238,8304,12171,12550,12781,12884,12944,13010,13066,13137,13197,13251,13363,13420,13481,13535,13611,13736,13823,13900,13993,14077,14160,14299,14381,14464,14595,14683,14761,14815,14871,14937,15011,15089,15160,15242,15318,15394,15469,15541,15648,15738,15811,15903,15999,16071,16147,16243,16296,16378,16445,16532,16619,16681,16745,16808,16877,16982,17092,17188,17296,17354,17414,17671,18006,18082,18232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f198488ab5a5112dc7ed7a95c5218eda\\transformed\\browser-1.6.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "89,147,148,149", "startColumns": "4,4,4,4", "startOffsets": "7785,12176,12276,12389", "endColumns": "110,99,112,97", "endOffsets": "7891,12271,12384,12482"}}]}]}