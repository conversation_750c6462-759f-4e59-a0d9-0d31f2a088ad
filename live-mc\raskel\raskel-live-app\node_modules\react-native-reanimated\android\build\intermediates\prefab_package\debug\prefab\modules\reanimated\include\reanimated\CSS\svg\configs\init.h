#pragma once

#include <reanimated/CSS/configs/interpolators/registry.h>

#include <reanimated/CSS/svg/configs/interpolators/circle.h>
#include <reanimated/CSS/svg/configs/interpolators/ellipse.h>
#include <reanimated/CSS/svg/configs/interpolators/line.h>
#include <reanimated/CSS/svg/configs/interpolators/path.h>
#include <reanimated/CSS/svg/configs/interpolators/rect.h>

namespace reanimated::css {

void initSvgCssSupport();

} // namespace reanimated::css
