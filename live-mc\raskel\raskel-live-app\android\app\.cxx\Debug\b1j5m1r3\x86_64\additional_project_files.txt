C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\ComponentDescriptors.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\EventEmitters.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\Props.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\ShadowNodes.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\States.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\rnworkletsJSI-generated.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\rnworklets-generated.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\RNWorkletsSpec_autolinked_build\CMakeFiles\react_codegen_RNWorkletsSpec.dir\RNWorkletsSpec-generated.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\RNWorkletsSpec_autolinked_build\CMakeFiles\react_codegen_RNWorkletsSpec.dir\react\renderer\components\RNWorkletsSpec\ComponentDescriptors.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\RNWorkletsSpec_autolinked_build\CMakeFiles\react_codegen_RNWorkletsSpec.dir\react\renderer\components\RNWorkletsSpec\EventEmitters.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\RNWorkletsSpec_autolinked_build\CMakeFiles\react_codegen_RNWorkletsSpec.dir\react\renderer\components\RNWorkletsSpec\Props.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\RNWorkletsSpec_autolinked_build\CMakeFiles\react_codegen_RNWorkletsSpec.dir\react\renderer\components\RNWorkletsSpec\RNWorkletsSpecJSI-generated.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\RNWorkletsSpec_autolinked_build\CMakeFiles\react_codegen_RNWorkletsSpec.dir\react\renderer\components\RNWorkletsSpec\ShadowNodes.cpp.o
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\.cxx\Debug\b1j5m1r3\x86_64\RNWorkletsSpec_autolinked_build\CMakeFiles\react_codegen_RNWorkletsSpec.dir\react\renderer\components\RNWorkletsSpec\States.cpp.o