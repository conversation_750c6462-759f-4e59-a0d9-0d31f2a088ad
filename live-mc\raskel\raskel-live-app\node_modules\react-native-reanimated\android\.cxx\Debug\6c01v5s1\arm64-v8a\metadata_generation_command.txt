                        -HC:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\build\intermediates\cxx\Debug\6c01v5s1\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\build\intermediates\cxx\Debug\6c01v5s1\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\.cxx\Debug\6c01v5s1\prefab\arm64-v8a\prefab
-BC:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\.cxx\Debug\6c01v5s1\arm64-v8a
-GNinja
-DANDROID_STL=c++_shared
-DREACT_NATIVE_MINOR_VERSION=81
-DANDROID_TOOLCHAIN=clang
-DREACT_NATIVE_DIR=C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native
-DREACT_NATIVE_WORKLETS_DIR=C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets
-DIS_REANIMATED_EXAMPLE_APP=false
-DREANIMATED_PROFILING=false
-DREANIMATED_VERSION=4.1.0
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
-DREANIMATED_FEATURE_FLAGS=[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]
                        Build command args: []
                        Version: 2