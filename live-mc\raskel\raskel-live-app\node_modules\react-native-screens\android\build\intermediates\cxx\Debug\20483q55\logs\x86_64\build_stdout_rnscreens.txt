ninja: Entering directory `C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\.cxx\Debug\20483q55\x86_64'
[1/6] Building CXX object CMakeFiles/rnscreens.dir/e6b18d4f51e54e10ca0ead7d868a78dd/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[2/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[3/6] Building CXX object CMakeFiles/rnscreens.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o
[4/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o
[5/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o
[6/6] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\20483q55\obj\x86_64\librnscreens.so
