{"name": "Fish Kaster", "slug": "fish-kaster", "owner": "nate619", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.fishkaster.app", "infoPlist": {"NSCameraUsageDescription": "$(PRODUCT_NAME) needs access to your Camera to identify fish species and capture fishing moments.", "NSMicrophoneUsageDescription": "This app needs access to microphone for live streaming", "NSLocationWhenInUseUsageDescription": "This app needs location access to provide local fishing spot recommendations", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library for AI analysis and to save your catches"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.fishkaster.app", "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_MEDIA_IMAGES", "android.permission.READ_MEDIA_VIDEO", "android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.FOREGROUND_SERVICE"]}, "web": {"favicon": "./assets/favicon.png"}, "scheme": "fishkaster", "plugins": ["expo-dev-client", ["expo-build-properties", {"android": {"minSdkVersion": 24, "compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0"}}], ["react-native-vision-camera", {"cameraPermissionText": "$(PRODUCT_NAME) needs access to your Camera to identify fish species and capture fishing moments.", "enableCodeScanner": false, "enableFrameProcessors": true}]], "extra": {"eas": {"projectId": "2423cf96-169d-4944-a161-55944e9d55c6"}}, "sdkVersion": "54.0.0", "platforms": ["ios", "android", "web"]}