# C/C++ build system timings
generate_cxx_metadata
  [gap of 53ms]
  create-invalidation-state 23ms
  generate-prefab-packages
    [gap of 25ms]
    exec-prefab 575ms
    [gap of 69ms]
  generate-prefab-packages completed in 669ms
  execute-generate-process
    [gap of 14ms]
    exec-configure 1400ms
    [gap of 61ms]
  execute-generate-process completed in 1475ms
  [gap of 24ms]
generate_cxx_metadata completed in 2266ms

