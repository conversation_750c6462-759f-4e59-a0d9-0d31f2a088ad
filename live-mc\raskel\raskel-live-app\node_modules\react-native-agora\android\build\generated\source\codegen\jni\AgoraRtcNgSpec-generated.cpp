
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "AgoraRtcNgSpec.h"

namespace facebook::react {

static facebook::jsi::Value __hostFunction_NativeAgoraRtcNgSpecJSI_newIrisApiEngine(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, BooleanKind, "newIrisApiEngine", "()Z", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAgoraRtcNgSpecJSI_destroyIrisApiEngine(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, BooleanKind, "destroyIrisApiEngine", "()Z", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAgoraRtcNgSpecJSI_callApi(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, StringKind, "callApi", "(Lcom/facebook/react/bridge/ReadableMap;)Ljava/lang/String;", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAgoraRtcNgSpecJSI_showRPSystemBroadcastPickerView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "showRPSystemBroadcastPickerView", "(ZLcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAgoraRtcNgSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "addListener", "(Ljava/lang/String;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAgoraRtcNgSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "removeListeners", "(D)V", args, count, cachedMethodId);
}

NativeAgoraRtcNgSpecJSI::NativeAgoraRtcNgSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["newIrisApiEngine"] = MethodMetadata {0, __hostFunction_NativeAgoraRtcNgSpecJSI_newIrisApiEngine};
  methodMap_["destroyIrisApiEngine"] = MethodMetadata {0, __hostFunction_NativeAgoraRtcNgSpecJSI_destroyIrisApiEngine};
  methodMap_["callApi"] = MethodMetadata {1, __hostFunction_NativeAgoraRtcNgSpecJSI_callApi};
  methodMap_["showRPSystemBroadcastPickerView"] = MethodMetadata {1, __hostFunction_NativeAgoraRtcNgSpecJSI_showRPSystemBroadcastPickerView};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAgoraRtcNgSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAgoraRtcNgSpecJSI_removeListeners};
}

std::shared_ptr<TurboModule> AgoraRtcNgSpec_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "AgoraRtcNg") {
    return std::make_shared<NativeAgoraRtcNgSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
