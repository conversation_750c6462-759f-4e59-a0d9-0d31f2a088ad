C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\@react-native-async-storage\async-storage\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-safe-area-context\android\src\main\jni\CMakeLists.txt
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\src\main\jni\CMakeLists.txt
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup\CMakeLists.txt