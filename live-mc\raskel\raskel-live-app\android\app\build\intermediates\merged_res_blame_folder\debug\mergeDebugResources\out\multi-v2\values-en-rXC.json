{"logs": [{"outputFile": "com.fishkaster.app-mergeDebugResources-84:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\30f51c8a36c85c07b7e3abc1db11ccb6\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,2001,2186,2352,2538,2716,2891,3062,3241,3408", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,185,177,174,170,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1996,2181,2347,2533,2711,2886,3057,3236,3403,3641"}, "to": {"startLines": "36,37,39,40,41,45,46,47,48,49,50,52,53,54,55,56,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6955,7146,7531,7728,7930,8729,8914,9107,9295,9482,9663,10034,10200,10386,10564,10739,11114,11293,11460", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,185,177,174,170,178,166,237", "endOffsets": "7141,7326,7723,7925,8112,8909,9102,9290,9477,9658,9843,10195,10381,10559,10734,10905,11288,11455,11693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8aa23bce709bfe96db10568360097556\\transformed\\core-1.15.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "29,30,31,32,33,34,35,57", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5528,5724,5929,6130,6331,6538,6743,10910", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "5719,5924,6125,6326,6533,6738,6950,11109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\29b62c1e8951214205c1999e10893e42\\transformed\\foundation-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,243", "endColumns": "187,186", "endOffsets": "238,425"}, "to": {"startLines": "61,62", "startColumns": "4,4", "startOffsets": "11698,11886", "endColumns": "187,186", "endOffsets": "11881,12068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\02bec446bb5b07b8548f83ec26b6ae83\\transformed\\appcompat-1.7.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,9848", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,10029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f198488ab5a5112dc7ed7a95c5218eda\\transformed\\browser-1.6.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "38,42,43,44", "startColumns": "4,4,4,4", "startOffsets": "7331,8117,8316,8527", "endColumns": "199,198,210,201", "endOffsets": "7526,8311,8522,8724"}}]}]}