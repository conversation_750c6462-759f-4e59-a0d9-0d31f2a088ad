{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "rnworklets", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "rnworklets::@6890427a1f51a3e7e1df", "jsonFile": "target-rnworklets-Debug-a723e53fc0d85eb4ceee.json", "name": "rnworklets", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/.cxx/Debug/5t5f5h6s/x86_64", "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android"}, "version": {"major": 2, "minor": 3}}