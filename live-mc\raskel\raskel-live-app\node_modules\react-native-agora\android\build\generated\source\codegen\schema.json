{"libraryName": "", "modules": {"AgoraRtcSurfaceView": {"type": "Component", "components": {"AgoraRtcSurfaceView": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "callApi", "optional": false, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "funcName", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "params", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "buffers", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}]}}, {"name": "zOrderOnTop", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "zOrderMediaOverlay", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}], "commands": []}}}, "AgoraRtcTextureView": {"type": "Component", "components": {"AgoraRtcTextureView": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "callApi", "optional": false, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "funcName", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "params", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "buffers", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}]}}], "commands": []}}}, "NativeAgoraRtcNg": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "newIrisApiEngine", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "BooleanTypeAnnotation"}, "params": []}}, {"name": "destroyIrisApiEngine", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "BooleanTypeAnnotation"}, "params": []}}, {"name": "callApi", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "StringTypeAnnotation"}, "params": [{"name": "args", "optional": false, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "funcName", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}, {"name": "params", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}, {"name": "buffers", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}]}}]}}, {"name": "showRPSystemBroadcastPickerView", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "PromiseTypeAnnotation", "elementType": {"type": "VoidTypeAnnotation"}}, "params": [{"name": "showsMicrophoneButton", "optional": false, "typeAnnotation": {"type": "BooleanTypeAnnotation"}}]}}, {"name": "addListener", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "eventName", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}, {"name": "removeListeners", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "count", "optional": false, "typeAnnotation": {"type": "NumberTypeAnnotation"}}]}}]}, "moduleName": "AgoraRtcNg"}}}