"com.apollographql.apollo.api.Query'com.apollographql.apollo.api.Query.Data$com.apollographql.apollo.api.Adapter<EMAIL>+expo.modules.devlauncher.compose.AuthResult(androidx.appcompat.app.AppCompatActivity8androidx.activity.result.contract.ActivityResultContractandroid.widget.LinearLayout4expo.modules.devlauncher.compose.models.BranchActionandroidx.lifecycle.ViewModel6expo.modules.devlauncher.compose.models.BranchesAction3expo.modules.devlauncher.compose.models.ErrorAction2expo.modules.devlauncher.compose.models.HomeAction4expo.modules.devlauncher.compose.models.ProfileState?expo.modules.devlauncher.compose.models.ProfileViewModel.Action6expo.modules.devlauncher.compose.models.SettingsAction#androidx.lifecycle.AndroidViewModel2kotlinx.serialization.internal.GeneratedSerializerkotlin.collections.Iteratorkotlin.collections.Iterablejava.io.Closeable?expo.modules.kotlin.devtools.ExpoRequestCdpInterceptor.Delegate)java.lang.Thread.UncaughtExceptionHandlerNexpo.modules.devlauncher.launcher.loaders.DevLauncherAppLoaderFactoryInterface>expo.modules.devlauncher.launcher.loaders.DevLauncherAppLoaderBexpo.modules.devlauncher.launcher.loaders.DevLauncherExpoAppLoader4com.facebook.react.bridge.ReactContextBaseJavaModule=com.facebook.react.devsupport.NonFinalBridgeDevSupportManagerAcom.facebook.react.devsupport.NonFinalBridgelessDevSupportManager6com.facebook.react.devsupport.DevSupportManagerFactory3expo.modules.core.interfaces.ReactNativeHostHandler1expo.modules.devlauncher.services.ApplicationInfo+expo.modules.devlauncher.services.UserState-com.facebook.react.devsupport.DevServerHelper1com.facebook.react.devsupport.DevMenuSettingsBase3com.facebook.react.devsupport.DevSupportManagerBase$expo.modules.core.interfaces.Packagecom.facebook.react.ReactPackage%org.koin.core.component.KoinComponent7expo.modules.updatesinterface.UpdatesInterfaceCallbacksDexpo.modules.devlauncher.launcher.DevLauncherIntentRegistryInterface<EMAIL>(com.facebook.react.ReactActivityDelegateTexpo.modules.devlauncher.react.activitydelegates.DevLauncherReactActivityNOPDelegateandroid.widget.RelativeLayout9expo.modules.devlauncher.tests.DevLauncherTestInterceptor                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               