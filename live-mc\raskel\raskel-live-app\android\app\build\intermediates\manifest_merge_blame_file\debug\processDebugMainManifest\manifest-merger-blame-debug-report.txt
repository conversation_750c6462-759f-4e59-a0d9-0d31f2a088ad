1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fishkaster.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:3-75
11-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:2:3-78
12-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:3:3-76
13-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:4:3-76
14-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:4:20-74
15    <uses-permission android:name="android.permission.BLUETOOTH" />
15-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:5:3-65
15-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:5:20-63
16    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
16-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:6:3-71
16-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:6:20-69
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:7:3-62
17-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:7:20-60
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:8:3-74
18-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:8:20-72
19    <uses-permission android:name="android.permission.INTERNET" />
19-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:9:3-64
19-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:9:20-62
20    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
20-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:10:3-77
20-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:10:20-75
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:11:3-77
21-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:11:20-75
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:12:3-73
22-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:12:20-71
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:13:3-72
23-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:13:20-70
24    <uses-permission android:name="android.permission.RECORD_AUDIO" />
24-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:14:3-68
24-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:14:20-66
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:16:3-63
25-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:16:20-61
26    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
26-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:17:3-78
26-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:17:20-76
27
28    <queries>
28-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:18:3-24:13
29        <intent>
29-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:19:5-23:14
30            <action android:name="android.intent.action.VIEW" />
30-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
30-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
31
32            <category android:name="android.intent.category.BROWSABLE" />
32-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
32-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
33
34            <data android:scheme="https" />
34-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
34-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
35        </intent>
36
37        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
37-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
37-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
38        <intent>
38-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:10:9-17:18
39            <action android:name="android.intent.action.OPEN_DOCUMENT" />
39-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:11:13-74
39-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:11:21-71
40
41            <category android:name="android.intent.category.DEFAULT" />
41-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
41-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
42            <category android:name="android.intent.category.OPENABLE" />
42-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:13-73
42-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:23-70
43
44            <data android:mimeType="*/*" />
44-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
45        </intent>
46        <intent>
46-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:15:9-19:18
47
48            <!-- Required for picking images from the camera roll if targeting API 30 -->
49            <action android:name="android.media.action.IMAGE_CAPTURE" />
49-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:18:13-73
49-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:18:21-70
50        </intent>
51        <intent>
51-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:20:9-24:18
52
53            <!-- Required for picking images from the camera if targeting API 30 -->
54            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
54-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:23:13-80
54-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:23:21-77
55        </intent> <!-- Needs to be explicitly declared on Android R+ -->
56        <package android:name="com.google.android.apps.maps" />
56-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
56-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
57
58        <intent>
58-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:23:9-25:18
59            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
59-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:13-86
59-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:21-83
60        </intent>
61        <intent>
61-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
62            <action android:name="android.intent.action.GET_CONTENT" />
62-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
62-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
63
64            <category android:name="android.intent.category.OPENABLE" />
64-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:13-73
64-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:23-70
65
66            <data android:mimeType="*/*" />
66-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
67        </intent>
68        <intent>
68-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:13:9-15:18
69            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
69-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:14:13-91
69-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:14:21-88
70        </intent>
71        <intent>
71-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:16:9-18:18
72            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
72-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:17:13-116
72-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:17:21-113
73        </intent>
74    </queries>
75
76    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
76-->[:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-76
76-->[:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-73
77
78    <uses-feature
78-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
79        android:glEsVersion="0x00020000"
79-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
80        android:required="true" />
80-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
81
82    <uses-permission android:name="com.android.vending.BILLING" />
82-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:10:5-67
82-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:10:22-64
83    <uses-permission android:name="android.permission.WAKE_LOCK" />
83-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
83-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:22-65
84    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
84-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
84-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:22-78
85
86    <permission
86-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
87        android:name="com.fishkaster.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
87-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
88        android:protectionLevel="signature" />
88-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
89
90    <uses-permission android:name="com.fishkaster.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
90-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
90-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
91    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
91-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:8:5-94
91-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:8:22-91
92
93    <application
93-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:3-42:17
94        android:name="com.fishkaster.app.MainApplication"
94-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:16-47
95        android:allowBackup="true"
95-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:162-188
96        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
96-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
97        android:debuggable="true"
98        android:enableOnBackInvokedCallback="false"
98-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:248-291
99        android:extractNativeLibs="false"
100        android:icon="@mipmap/ic_launcher"
100-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:81-115
101        android:label="@string/app_name"
101-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:48-80
102        android:roundIcon="@mipmap/ic_launcher_round"
102-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:116-161
103        android:supportsRtl="true"
103-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:221-247
104        android:theme="@style/AppTheme"
104-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:189-220
105        android:usesCleartextTraffic="true" >
105-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:6:18-53
106        <meta-data
106-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:5-83
107            android:name="expo.modules.updates.ENABLED"
107-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:16-59
108            android:value="false" />
108-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:60-81
109        <meta-data
109-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:5-105
110            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
110-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:16-80
111            android:value="ALWAYS" />
111-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:81-103
112        <meta-data
112-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:5-99
113            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
113-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:16-79
114            android:value="0" />
114-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:80-97
115
116        <activity
116-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:5-41:16
117            android:name="com.fishkaster.app.MainActivity"
117-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:15-43
118            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
118-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:44-134
119            android:exported="true"
119-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:256-279
120            android:launchMode="singleTask"
120-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:135-166
121            android:screenOrientation="portrait"
121-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:280-316
122            android:theme="@style/Theme.App.SplashScreen"
122-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:210-255
123            android:windowSoftInputMode="adjustResize" >
123-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:167-209
124            <intent-filter>
124-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:30:7-33:23
125                <action android:name="android.intent.action.MAIN" />
125-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:31:9-60
125-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:31:17-58
126
127                <category android:name="android.intent.category.LAUNCHER" />
127-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:32:9-68
127-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:32:19-66
128            </intent-filter>
129            <intent-filter>
129-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:34:7-40:23
130                <action android:name="android.intent.action.VIEW" />
130-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
130-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
131
132                <category android:name="android.intent.category.DEFAULT" />
132-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
132-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
133                <category android:name="android.intent.category.BROWSABLE" />
133-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
133-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
134
135                <data android:scheme="fishkaster" />
135-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
135-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
136                <data android:scheme="exp+fish-kaster" />
136-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
136-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
137            </intent-filter>
138        </activity>
139        <activity
139-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:75
140            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
140-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
141            android:exported="true"
141-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
142            android:launchMode="singleTask"
142-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
143            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
143-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-72
144        <activity
144-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-30:20
145            android:name="expo.modules.devlauncher.compose.AuthActivity"
145-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
146            android:exported="true"
146-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-36
147            android:launchMode="singleTask"
147-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-44
148            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" >
148-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-72
149            <intent-filter>
149-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-29:29
150                <action android:name="android.intent.action.VIEW" />
150-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
150-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
151
152                <category android:name="android.intent.category.DEFAULT" />
152-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
152-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
153                <category android:name="android.intent.category.BROWSABLE" />
153-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
153-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
154
155                <data android:scheme="expo-dev-launcher" />
155-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
155-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
156            </intent-filter>
157        </activity>
158        <activity
158-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:9-34:70
159            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
159-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-93
160            android:screenOrientation="portrait"
160-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-49
161            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
161-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-67
162
163        <meta-data
163-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:9-38:42
164            android:name="com.google.mlkit.vision.DEPENDENCIES"
164-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-64
165            android:value="barcode_ui" />
165-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-39
166        <meta-data
166-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
167            android:name="org.unimodules.core.AppLoader#react-native-headless"
167-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
168            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
168-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
169        <meta-data
169-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
170            android:name="com.facebook.soloader.enabled"
170-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
171            android:value="true" />
171-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
172
173        <activity
173-->[com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:19:9-21:40
174            android:name="com.facebook.react.devsupport.DevSettingsActivity"
174-->[com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:20:13-77
175            android:exported="false" />
175-->[com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:21:13-37
176
177        <service
177-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:28:9-40:19
178            android:name="com.google.android.gms.metadata.ModuleDependencies"
178-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:29:13-78
179            android:enabled="false"
179-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:30:13-36
180            android:exported="false" >
180-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:31:13-37
181            <intent-filter>
181-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:33:13-35:29
182                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
182-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:34:17-94
182-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:34:25-91
183            </intent-filter>
184
185            <meta-data
185-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:37:13-39:36
186                android:name="photopicker_activity:0:required"
186-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:38:17-63
187                android:value="" />
187-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:39:17-33
188        </service>
189
190        <activity
190-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:42:9-46:48
191            android:name="expo.modules.imagepicker.ExpoCropImageActivity"
191-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:43:13-74
192            android:exported="false"
192-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:44:13-37
193            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
193-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:45:13-56
194        <provider
194-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:48:9-56:20
195            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
195-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:49:13-89
196            android:authorities="com.fishkaster.app.ImagePickerFileProvider"
196-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:50:13-75
197            android:exported="false"
197-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:51:13-37
198            android:grantUriPermissions="true" >
198-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:52:13-47
199            <meta-data
199-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:53:13-55:71
200                android:name="android.support.FILE_PROVIDER_PATHS"
200-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:54:17-67
201                android:resource="@xml/image_picker_provider_paths" />
201-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:55:17-68
202        </provider>
203
204        <service
204-->[host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:11:9-14:56
205            android:name="expo.modules.location.services.LocationTaskService"
205-->[host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:12:13-78
206            android:exported="false"
206-->[host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:13:13-37
207            android:foregroundServiceType="location" />
207-->[host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:14:13-53
208
209        <meta-data
209-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
210            android:name="com.google.android.gms.version"
210-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
211            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
211-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
212        <uses-library
212-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
213            android:name="org.apache.http.legacy"
213-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
214            android:required="false" />
214-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
215        <uses-library
215-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:29:9-31:40
216            android:name="androidx.camera.extensions.impl"
216-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:30:13-59
217            android:required="false" />
217-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:31:13-37
218
219        <service
219-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:24:9-33:19
220            android:name="androidx.camera.core.impl.MetadataHolderService"
220-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:25:13-75
221            android:enabled="false"
221-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:26:13-36
222            android:exported="false" >
222-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:27:13-37
223            <meta-data
223-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:30:13-32:89
224                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
224-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:31:17-103
225                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
225-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:32:17-86
226        </service>
227
228        <provider
228-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
229            android:name="com.canhub.cropper.CropFileProvider"
229-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
230            android:authorities="com.fishkaster.app.cropper.fileprovider"
230-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
231            android:exported="false"
231-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
232            android:grantUriPermissions="true" >
232-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
233            <meta-data
233-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:53:13-55:71
234                android:name="android.support.FILE_PROVIDER_PATHS"
234-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:54:17-67
235                android:resource="@xml/library_file_paths" />
235-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:55:17-68
236        </provider>
237
238        <activity
238-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
239            android:name="com.canhub.cropper.CropImageActivity"
239-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:32:13-64
240            android:exported="true" />
240-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
241
242        <service
242-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
243            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
243-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
244            android:directBootAware="true"
244-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
245            android:exported="false" >
245-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
246            <meta-data
246-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
247                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
247-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
248                android:value="com.google.firebase.components.ComponentRegistrar" />
248-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
249            <meta-data
249-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
250                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
250-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
251                android:value="com.google.firebase.components.ComponentRegistrar" />
251-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
252            <meta-data
252-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
253                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
253-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
254                android:value="com.google.firebase.components.ComponentRegistrar" />
254-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
255        </service>
256        <!--
257        This activity is an invisible delegate activity to start scanner activity
258        and receive result, so it's unnecessary to support screen orientation and
259        we can avoid any side effect from activity recreation in any case.
260        -->
261        <activity
261-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
262            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
262-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
263            android:exported="false"
263-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
264            android:screenOrientation="portrait" >
264-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
265        </activity>
266
267        <provider
267-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
268            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
268-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
269            android:authorities="com.fishkaster.app.mlkitinitprovider"
269-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
270            android:exported="false"
270-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
271            android:initOrder="99" />
271-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
272
273        <receiver
273-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:8:9-15:20
274            android:name="com.amazon.device.iap.ResponseReceiver"
274-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:9:13-66
275            android:exported="true"
275-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:10:13-36
276            android:permission="com.amazon.inapp.purchasing.Permission.NOTIFY" >
276-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:11:13-79
277            <intent-filter>
277-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:12:13-14:29
278                <action android:name="com.amazon.inapp.purchasing.NOTIFY" />
278-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:13:17-77
278-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:13:25-74
279            </intent-filter>
280        </receiver>
281
282        <activity
282-->[com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:10:9-13:75
283            android:name="com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity"
283-->[com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:11:13-97
284            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
284-->[com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:12:13-96
285            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
285-->[com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:13:13-72
286
287        <meta-data
287-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:22:9-24:37
288            android:name="com.google.android.play.billingclient.version"
288-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:23:13-73
289            android:value="8.0.0" />
289-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:24:13-34
290
291        <activity
291-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:26:9-30:75
292            android:name="com.android.billingclient.api.ProxyBillingActivity"
292-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:27:13-78
293            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
293-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:28:13-96
294            android:exported="false"
294-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:29:13-37
295            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
295-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:30:13-72
296        <activity
296-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:31:9-35:75
297            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
297-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:32:13-80
298            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
298-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:33:13-96
299            android:exported="false"
299-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:34:13-37
300            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
300-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:35:13-72
301        <activity
301-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
302            android:name="com.google.android.gms.common.api.GoogleApiActivity"
302-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
303            android:exported="false"
303-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
304            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
304-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
305        <activity
305-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
306            android:name="androidx.compose.ui.tooling.PreviewActivity"
306-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:24:13-71
307            android:exported="true" />
307-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:25:13-36
308
309        <provider
309-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
310            android:name="androidx.startup.InitializationProvider"
310-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
311            android:authorities="com.fishkaster.app.androidx-startup"
311-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
312            android:exported="false" >
312-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
313            <meta-data
313-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
314                android:name="androidx.work.WorkManagerInitializer"
314-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
315                android:value="androidx.startup" />
315-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
316            <meta-data
316-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
317                android:name="androidx.emoji2.text.EmojiCompatInitializer"
317-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
318                android:value="androidx.startup" />
318-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
319            <meta-data
319-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
320                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
320-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
321                android:value="androidx.startup" />
321-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
322            <meta-data
322-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
323                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
323-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
324                android:value="androidx.startup" />
324-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
325        </provider>
326
327        <service
327-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
328            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
328-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
329            android:directBootAware="false"
329-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
330            android:enabled="@bool/enable_system_alarm_service_default"
330-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
331            android:exported="false" />
331-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
332        <service
332-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
333            android:name="androidx.work.impl.background.systemjob.SystemJobService"
333-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
335            android:enabled="@bool/enable_system_job_service_default"
335-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
336            android:exported="true"
336-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
337            android:permission="android.permission.BIND_JOB_SERVICE" />
337-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
338        <service
338-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
339            android:name="androidx.work.impl.foreground.SystemForegroundService"
339-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
340            android:directBootAware="false"
340-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
341            android:enabled="@bool/enable_system_foreground_service_default"
341-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
342            android:exported="false" />
342-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
343
344        <receiver
344-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
345            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
345-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
346            android:directBootAware="false"
346-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
347            android:enabled="true"
347-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
348            android:exported="false" />
348-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
349        <receiver
349-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
350            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
350-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
351            android:directBootAware="false"
351-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
352            android:enabled="false"
352-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
353            android:exported="false" >
353-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
354            <intent-filter>
354-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
355                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
355-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
355-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
356                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
356-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
356-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
357            </intent-filter>
358        </receiver>
359        <receiver
359-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
360            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
360-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
361            android:directBootAware="false"
361-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
362            android:enabled="false"
362-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
363            android:exported="false" >
363-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
364            <intent-filter>
364-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
365                <action android:name="android.intent.action.BATTERY_OKAY" />
365-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
365-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
366                <action android:name="android.intent.action.BATTERY_LOW" />
366-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
366-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
367            </intent-filter>
368        </receiver>
369        <receiver
369-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
370            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
370-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
371            android:directBootAware="false"
371-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
372            android:enabled="false"
372-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
373            android:exported="false" >
373-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
374            <intent-filter>
374-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
375                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
375-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
375-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
376                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
376-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
376-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
377            </intent-filter>
378        </receiver>
379        <receiver
379-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
380            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
380-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
381            android:directBootAware="false"
381-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
382            android:enabled="false"
382-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
383            android:exported="false" >
383-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
384            <intent-filter>
384-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
385                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
385-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
385-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
386            </intent-filter>
387        </receiver>
388        <receiver
388-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
389            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
389-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
390            android:directBootAware="false"
390-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
391            android:enabled="false"
391-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
392            android:exported="false" >
392-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
393            <intent-filter>
393-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
394                <action android:name="android.intent.action.BOOT_COMPLETED" />
394-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:17-79
394-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:25-76
395                <action android:name="android.intent.action.TIME_SET" />
395-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
395-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
396                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
396-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
396-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
397            </intent-filter>
398        </receiver>
399        <receiver
399-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
400            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
400-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
401            android:directBootAware="false"
401-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
402            android:enabled="@bool/enable_system_alarm_service_default"
402-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
403            android:exported="false" >
403-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
404            <intent-filter>
404-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
405                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
405-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
405-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
406            </intent-filter>
407        </receiver>
408        <receiver
408-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
409            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
409-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
410            android:directBootAware="false"
410-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
411            android:enabled="true"
411-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
412            android:exported="true"
412-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
413            android:permission="android.permission.DUMP" >
413-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
414            <intent-filter>
414-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
415                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
415-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
415-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
416            </intent-filter>
417        </receiver>
418        <receiver
418-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
419            android:name="androidx.profileinstaller.ProfileInstallReceiver"
419-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
420            android:directBootAware="false"
420-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
421            android:enabled="true"
421-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
422            android:exported="true"
422-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
423            android:permission="android.permission.DUMP" >
423-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
424            <intent-filter>
424-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
425                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
426            </intent-filter>
427            <intent-filter>
427-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
428                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
428-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
428-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
429            </intent-filter>
430            <intent-filter>
430-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
431                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
431-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
431-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
432            </intent-filter>
433            <intent-filter>
433-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
434                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
434-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
434-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
435            </intent-filter>
436        </receiver>
437
438        <service
438-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
439            android:name="androidx.room.MultiInstanceInvalidationService"
439-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
440            android:directBootAware="true"
440-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
441            android:exported="false" />
441-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
442        <service
442-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
443            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
443-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
444            android:exported="false" >
444-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
445            <meta-data
445-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
446                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
446-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
447                android:value="cct" />
447-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
448        </service>
449        <service
449-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
450            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
450-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
451            android:exported="false"
451-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
452            android:permission="android.permission.BIND_JOB_SERVICE" >
452-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
453        </service>
454
455        <receiver
455-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
456            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
456-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
457            android:exported="false" />
457-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
458
459        <activity
459-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:11:9-14:63
460            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenCaptureAssistantActivity"
460-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:11:19-89
461            android:configChanges="screenSize|orientation"
461-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:12:13-59
462            android:screenOrientation="unspecified"
462-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:13:13-52
463            android:theme="@android:style/Theme.Translucent" />
463-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:14:13-61
464
465        <service
465-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:16:9-19:19
466            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenSharingService"
466-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:17:13-73
467            android:foregroundServiceType="mediaProjection" >
467-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:18:13-60
468        </service>
469    </application>
470
471</manifest>
