1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fishkaster.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:3-75
11-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:2:3-78
12-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:3:3-76
13-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:4:3-76
14-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:4:20-74
15    <uses-permission android:name="android.permission.BLUETOOTH" />
15-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:5:3-65
15-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:5:20-63
16    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
16-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:6:3-71
16-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:6:20-69
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:7:3-62
17-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:7:20-60
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:8:3-74
18-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:8:20-72
19    <uses-permission android:name="android.permission.INTERNET" />
19-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:9:3-64
19-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:9:20-62
20    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
20-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:10:3-77
20-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:10:20-75
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:11:3-77
21-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:11:20-75
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:12:3-73
22-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:12:20-71
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:13:3-72
23-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:13:20-70
24    <uses-permission android:name="android.permission.RECORD_AUDIO" />
24-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:14:3-68
24-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:14:20-66
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:16:3-63
25-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:16:20-61
26    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
26-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:17:3-78
26-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:17:20-76
27
28    <queries>
28-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:18:3-24:13
29        <intent>
29-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:19:5-23:14
30            <action android:name="android.intent.action.VIEW" />
30-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
30-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
31
32            <category android:name="android.intent.category.BROWSABLE" />
32-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
32-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
33
34            <data android:scheme="https" />
34-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
34-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
35        </intent>
36
37        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
37-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
37-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
38        <intent>
38-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:10:9-17:18
39            <action android:name="android.intent.action.OPEN_DOCUMENT" />
39-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:11:13-74
39-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:11:21-71
40
41            <category android:name="android.intent.category.DEFAULT" />
41-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
41-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
42            <category android:name="android.intent.category.OPENABLE" />
42-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:13-73
42-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:23-70
43
44            <data android:mimeType="*/*" />
44-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
45        </intent>
46        <intent>
46-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:15:9-19:18
47
48            <!-- Required for picking images from the camera roll if targeting API 30 -->
49            <action android:name="android.media.action.IMAGE_CAPTURE" />
49-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:18:13-73
49-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:18:21-70
50        </intent>
51        <intent>
51-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:20:9-24:18
52
53            <!-- Required for picking images from the camera if targeting API 30 -->
54            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
54-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:23:13-80
54-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:23:21-77
55        </intent> <!-- Needs to be explicitly declared on Android R+ -->
56        <package android:name="com.google.android.apps.maps" />
56-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
56-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
57
58        <intent>
58-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:23:9-25:18
59            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
59-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:13-86
59-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:21-83
60        </intent>
61        <intent>
61-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
62            <action android:name="android.intent.action.GET_CONTENT" />
62-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
62-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
63
64            <category android:name="android.intent.category.OPENABLE" />
64-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:13-73
64-->[host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:23-70
65
66            <data android:mimeType="*/*" />
66-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
67        </intent>
68        <intent>
68-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:13:9-15:18
69            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
69-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:14:13-91
69-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:14:21-88
70        </intent>
71        <intent>
71-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:16:9-18:18
72            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
72-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:17:13-116
72-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:17:21-113
73        </intent>
74    </queries>
75
76    <uses-feature
76-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
77        android:glEsVersion="0x00020000"
77-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
78        android:required="true" />
78-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
79
80    <uses-permission android:name="com.android.vending.BILLING" />
80-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:10:5-67
80-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:10:22-64
81    <uses-permission android:name="android.permission.WAKE_LOCK" />
81-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
81-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:22-65
82    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
82-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
82-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:22-78
83
84    <permission
84-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
85        android:name="com.fishkaster.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
85-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
86        android:protectionLevel="signature" />
86-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
87
88    <uses-permission android:name="com.fishkaster.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
88-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
88-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
89
90    <application
90-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:3-42:17
91        android:name="com.fishkaster.app.MainApplication"
91-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:16-47
92        android:allowBackup="true"
92-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:162-188
93        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
93-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
94        android:debuggable="true"
95        android:enableOnBackInvokedCallback="false"
95-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:248-291
96        android:extractNativeLibs="false"
97        android:icon="@mipmap/ic_launcher"
97-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:81-115
98        android:label="@string/app_name"
98-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:48-80
99        android:roundIcon="@mipmap/ic_launcher_round"
99-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:116-161
100        android:supportsRtl="true"
100-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:221-247
101        android:theme="@style/AppTheme"
101-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:189-220
102        android:usesCleartextTraffic="true" >
102-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:6:18-53
103        <meta-data
103-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:5-83
104            android:name="expo.modules.updates.ENABLED"
104-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:16-59
105            android:value="false" />
105-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:60-81
106        <meta-data
106-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:5-105
107            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
107-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:16-80
108            android:value="ALWAYS" />
108-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:81-103
109        <meta-data
109-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:5-99
110            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
110-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:16-79
111            android:value="0" />
111-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:80-97
112
113        <activity
113-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:5-41:16
114            android:name="com.fishkaster.app.MainActivity"
114-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:15-43
115            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
115-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:44-134
116            android:exported="true"
116-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:256-279
117            android:launchMode="singleTask"
117-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:135-166
118            android:screenOrientation="portrait"
118-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:280-316
119            android:theme="@style/Theme.App.SplashScreen"
119-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:210-255
120            android:windowSoftInputMode="adjustResize" >
120-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:167-209
121            <intent-filter>
121-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:30:7-33:23
122                <action android:name="android.intent.action.MAIN" />
122-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:31:9-60
122-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:31:17-58
123
124                <category android:name="android.intent.category.LAUNCHER" />
124-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:32:9-68
124-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:32:19-66
125            </intent-filter>
126            <intent-filter>
126-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:34:7-40:23
127                <action android:name="android.intent.action.VIEW" />
127-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
127-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
129-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
130                <category android:name="android.intent.category.BROWSABLE" />
130-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
130-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
131
132                <data android:scheme="fishkaster" />
132-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
132-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
133                <data android:scheme="exp+fish-kaster" />
133-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
133-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
134            </intent-filter>
135        </activity>
136        <activity
136-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:75
137            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
137-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
138            android:exported="true"
138-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
139            android:launchMode="singleTask"
139-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
140            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
140-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-72
141        <activity
141-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-30:20
142            android:name="expo.modules.devlauncher.compose.AuthActivity"
142-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
143            android:exported="true"
143-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-36
144            android:launchMode="singleTask"
144-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-44
145            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" >
145-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-72
146            <intent-filter>
146-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-29:29
147                <action android:name="android.intent.action.VIEW" />
147-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
147-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
148
149                <category android:name="android.intent.category.DEFAULT" />
149-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
149-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
150                <category android:name="android.intent.category.BROWSABLE" />
150-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
150-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
151
152                <data android:scheme="expo-dev-launcher" />
152-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
152-->C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
153            </intent-filter>
154        </activity>
155        <activity
155-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:9-34:70
156            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
156-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-93
157            android:screenOrientation="portrait"
157-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-49
158            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
158-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-67
159
160        <meta-data
160-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:9-38:42
161            android:name="com.google.mlkit.vision.DEPENDENCIES"
161-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-64
162            android:value="barcode_ui" />
162-->[:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-39
163        <meta-data
163-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
164            android:name="org.unimodules.core.AppLoader#react-native-headless"
164-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
165            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
165-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
166        <meta-data
166-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
167            android:name="com.facebook.soloader.enabled"
167-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
168            android:value="true" />
168-->[:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
169
170        <activity
170-->[com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:19:9-21:40
171            android:name="com.facebook.react.devsupport.DevSettingsActivity"
171-->[com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:20:13-77
172            android:exported="false" />
172-->[com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:21:13-37
173
174        <service
174-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:28:9-40:19
175            android:name="com.google.android.gms.metadata.ModuleDependencies"
175-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:29:13-78
176            android:enabled="false"
176-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:30:13-36
177            android:exported="false" >
177-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:31:13-37
178            <intent-filter>
178-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:33:13-35:29
179                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
179-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:34:17-94
179-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:34:25-91
180            </intent-filter>
181
182            <meta-data
182-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:37:13-39:36
183                android:name="photopicker_activity:0:required"
183-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:38:17-63
184                android:value="" />
184-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:39:17-33
185        </service>
186
187        <activity
187-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:42:9-46:48
188            android:name="expo.modules.imagepicker.ExpoCropImageActivity"
188-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:43:13-74
189            android:exported="false"
189-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:44:13-37
190            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
190-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:45:13-56
191        <provider
191-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:48:9-56:20
192            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
192-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:49:13-89
193            android:authorities="com.fishkaster.app.ImagePickerFileProvider"
193-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:50:13-75
194            android:exported="false"
194-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:51:13-37
195            android:grantUriPermissions="true" >
195-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:52:13-47
196            <meta-data
196-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:53:13-55:71
197                android:name="android.support.FILE_PROVIDER_PATHS"
197-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:54:17-67
198                android:resource="@xml/image_picker_provider_paths" />
198-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:55:17-68
199        </provider>
200
201        <service
201-->[host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:11:9-14:56
202            android:name="expo.modules.location.services.LocationTaskService"
202-->[host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:12:13-78
203            android:exported="false"
203-->[host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:13:13-37
204            android:foregroundServiceType="location" />
204-->[host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:14:13-53
205
206        <meta-data
206-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
207            android:name="com.google.android.gms.version"
207-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
208            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
208-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
209        <uses-library
209-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
210            android:name="org.apache.http.legacy"
210-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
211            android:required="false" />
211-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
212        <uses-library
212-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:29:9-31:40
213            android:name="androidx.camera.extensions.impl"
213-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:30:13-59
214            android:required="false" />
214-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:31:13-37
215
216        <service
216-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:24:9-33:19
217            android:name="androidx.camera.core.impl.MetadataHolderService"
217-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:25:13-75
218            android:enabled="false"
218-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:26:13-36
219            android:exported="false" >
219-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:27:13-37
220            <meta-data
220-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:30:13-32:89
221                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
221-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:31:17-103
222                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
222-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:32:17-86
223        </service>
224
225        <provider
225-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
226            android:name="com.canhub.cropper.CropFileProvider"
226-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
227            android:authorities="com.fishkaster.app.cropper.fileprovider"
227-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
228            android:exported="false"
228-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
229            android:grantUriPermissions="true" >
229-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
230            <meta-data
230-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:53:13-55:71
231                android:name="android.support.FILE_PROVIDER_PATHS"
231-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:54:17-67
232                android:resource="@xml/library_file_paths" />
232-->[host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:55:17-68
233        </provider>
234
235        <activity
235-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
236            android:name="com.canhub.cropper.CropImageActivity"
236-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:32:13-64
237            android:exported="true" />
237-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
238
239        <service
239-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
240            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
240-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
241            android:directBootAware="true"
241-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
242            android:exported="false" >
242-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
243            <meta-data
243-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
244                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
244-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
245                android:value="com.google.firebase.components.ComponentRegistrar" />
245-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
246            <meta-data
246-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
247                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
247-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
248                android:value="com.google.firebase.components.ComponentRegistrar" />
248-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
249            <meta-data
249-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
250                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
250-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
251                android:value="com.google.firebase.components.ComponentRegistrar" />
251-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
252        </service>
253        <!--
254        This activity is an invisible delegate activity to start scanner activity
255        and receive result, so it's unnecessary to support screen orientation and
256        we can avoid any side effect from activity recreation in any case.
257        -->
258        <activity
258-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
259            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
259-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
260            android:exported="false"
260-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
261            android:screenOrientation="portrait" >
261-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
262        </activity>
263
264        <provider
264-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
265            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
265-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
266            android:authorities="com.fishkaster.app.mlkitinitprovider"
266-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
267            android:exported="false"
267-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
268            android:initOrder="99" />
268-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
269
270        <receiver
270-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:8:9-15:20
271            android:name="com.amazon.device.iap.ResponseReceiver"
271-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:9:13-66
272            android:exported="true"
272-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:10:13-36
273            android:permission="com.amazon.inapp.purchasing.Permission.NOTIFY" >
273-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:11:13-79
274            <intent-filter>
274-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:12:13-14:29
275                <action android:name="com.amazon.inapp.purchasing.NOTIFY" />
275-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:13:17-77
275-->[com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:13:25-74
276            </intent-filter>
277        </receiver>
278
279        <activity
279-->[com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:10:9-13:75
280            android:name="com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity"
280-->[com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:11:13-97
281            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
281-->[com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:12:13-96
282            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
282-->[com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:13:13-72
283
284        <meta-data
284-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:22:9-24:37
285            android:name="com.google.android.play.billingclient.version"
285-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:23:13-73
286            android:value="8.0.0" />
286-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:24:13-34
287
288        <activity
288-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:26:9-30:75
289            android:name="com.android.billingclient.api.ProxyBillingActivity"
289-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:27:13-78
290            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
290-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:28:13-96
291            android:exported="false"
291-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:29:13-37
292            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
292-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:30:13-72
293        <activity
293-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:31:9-35:75
294            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
294-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:32:13-80
295            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
295-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:33:13-96
296            android:exported="false"
296-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:34:13-37
297            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
297-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:35:13-72
298        <activity
298-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
299            android:name="com.google.android.gms.common.api.GoogleApiActivity"
299-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
300            android:exported="false"
300-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
301            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
301-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
302        <activity
302-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
303            android:name="androidx.compose.ui.tooling.PreviewActivity"
303-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:24:13-71
304            android:exported="true" />
304-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:25:13-36
305
306        <provider
306-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
307            android:name="androidx.startup.InitializationProvider"
307-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
308            android:authorities="com.fishkaster.app.androidx-startup"
308-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
309            android:exported="false" >
309-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
310            <meta-data
310-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
311                android:name="androidx.work.WorkManagerInitializer"
311-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
312                android:value="androidx.startup" />
312-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
313            <meta-data
313-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
314                android:name="androidx.emoji2.text.EmojiCompatInitializer"
314-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
315                android:value="androidx.startup" />
315-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
316            <meta-data
316-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
317                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
317-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
318                android:value="androidx.startup" />
318-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
319            <meta-data
319-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
320                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
320-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
321                android:value="androidx.startup" />
321-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
322        </provider>
323
324        <service
324-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
325            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
325-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
326            android:directBootAware="false"
326-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
327            android:enabled="@bool/enable_system_alarm_service_default"
327-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
328            android:exported="false" />
328-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
329        <service
329-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
330            android:name="androidx.work.impl.background.systemjob.SystemJobService"
330-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
331            android:directBootAware="false"
331-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
332            android:enabled="@bool/enable_system_job_service_default"
332-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
333            android:exported="true"
333-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
334            android:permission="android.permission.BIND_JOB_SERVICE" />
334-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
335        <service
335-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
336            android:name="androidx.work.impl.foreground.SystemForegroundService"
336-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
337            android:directBootAware="false"
337-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
338            android:enabled="@bool/enable_system_foreground_service_default"
338-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
339            android:exported="false" />
339-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
340
341        <receiver
341-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
342            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
342-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
343            android:directBootAware="false"
343-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
344            android:enabled="true"
344-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
345            android:exported="false" />
345-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
346        <receiver
346-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
347            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
347-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
348            android:directBootAware="false"
348-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
349            android:enabled="false"
349-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
350            android:exported="false" >
350-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
351            <intent-filter>
351-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
352                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
352-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
352-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
353                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
353-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
353-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
354            </intent-filter>
355        </receiver>
356        <receiver
356-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
357            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
357-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
358            android:directBootAware="false"
358-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
359            android:enabled="false"
359-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
360            android:exported="false" >
360-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
361            <intent-filter>
361-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
362                <action android:name="android.intent.action.BATTERY_OKAY" />
362-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
362-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
363                <action android:name="android.intent.action.BATTERY_LOW" />
363-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
363-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
364            </intent-filter>
365        </receiver>
366        <receiver
366-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
367            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
367-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
368            android:directBootAware="false"
368-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
369            android:enabled="false"
369-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
370            android:exported="false" >
370-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
371            <intent-filter>
371-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
372                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
372-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
372-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
373                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
373-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
373-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
374            </intent-filter>
375        </receiver>
376        <receiver
376-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
377            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
377-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
378            android:directBootAware="false"
378-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
379            android:enabled="false"
379-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
380            android:exported="false" >
380-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
381            <intent-filter>
381-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
382                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
382-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
382-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
383            </intent-filter>
384        </receiver>
385        <receiver
385-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
386            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
386-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
387            android:directBootAware="false"
387-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
388            android:enabled="false"
388-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
389            android:exported="false" >
389-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
390            <intent-filter>
390-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
391                <action android:name="android.intent.action.BOOT_COMPLETED" />
391-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:17-79
391-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:25-76
392                <action android:name="android.intent.action.TIME_SET" />
392-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
392-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
393                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
393-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
393-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
394            </intent-filter>
395        </receiver>
396        <receiver
396-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
397            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
397-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
398            android:directBootAware="false"
398-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
399            android:enabled="@bool/enable_system_alarm_service_default"
399-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
400            android:exported="false" >
400-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
401            <intent-filter>
401-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
402                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
402-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
402-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
403            </intent-filter>
404        </receiver>
405        <receiver
405-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
406            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
406-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
407            android:directBootAware="false"
407-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
408            android:enabled="true"
408-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
409            android:exported="true"
409-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
410            android:permission="android.permission.DUMP" >
410-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
411            <intent-filter>
411-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
412                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
412-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
412-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
413            </intent-filter>
414        </receiver>
415        <receiver
415-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
416            android:name="androidx.profileinstaller.ProfileInstallReceiver"
416-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
417            android:directBootAware="false"
417-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
418            android:enabled="true"
418-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
419            android:exported="true"
419-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
420            android:permission="android.permission.DUMP" >
420-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
421            <intent-filter>
421-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
422                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
422-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
422-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
423            </intent-filter>
424            <intent-filter>
424-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
425                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
426            </intent-filter>
427            <intent-filter>
427-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
428                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
428-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
428-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
429            </intent-filter>
430            <intent-filter>
430-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
431                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
431-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
431-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
432            </intent-filter>
433        </receiver>
434
435        <service
435-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
436            android:name="androidx.room.MultiInstanceInvalidationService"
436-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
437            android:directBootAware="true"
437-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
438            android:exported="false" />
438-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
439        <service
439-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
440            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
440-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
441            android:exported="false" >
441-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
442            <meta-data
442-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
443                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
443-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
444                android:value="cct" />
444-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
445        </service>
446        <service
446-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
447            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
447-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
448            android:exported="false"
448-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
449            android:permission="android.permission.BIND_JOB_SERVICE" >
449-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
450        </service>
451
452        <receiver
452-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
453            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
453-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
454            android:exported="false" />
454-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
455    </application>
456
457</manifest>
