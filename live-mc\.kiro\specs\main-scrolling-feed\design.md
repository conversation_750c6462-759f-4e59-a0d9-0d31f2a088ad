# Main Scrolling Feed Design Document

## Overview

The Main Scrolling Feed will be implemented as a new screen called `MainFeedScreen` that opens full-screen when the app launches (like TikTok/Twitch). This provides a **completely immersive vertical scrolling experience** with full-screen content items that take up the entire viewport - no stream cards, no traditional layouts, just pure full-screen content and videos. 

**Key Design Principles:**
- **Full-screen immersion**: Each content item fills the entire screen (like TikTok)
- **Seamless vertical scrolling**: Smooth transitions between full-screen content
- **Video-first experience**: Videos and live streams are the primary content type
- **Minimal UI overlay**: Clean interface with floating controls that don't obstruct content
- **Gesture-driven navigation**: Swipe up/down to navigate, tap for interactions

## Architecture

### Component Hierarchy
```
MainFeedScreen (Full-Screen - 100% viewport)
├── FullScreenFeedList (FlatList - pagingEnabled, snapToInterval)
│   ├── FullScreenContentItem (each item = full screen height)
│   │   ├── BackgroundVideo/Image (full screen, aspect fill)
│   │   ├── VideoPlayer (for video content - full screen)
│   │   ├── LiveStreamPlayer (for live streams - full screen)
│   │   └── ContentOverlays (floating on top of media)
│   │       ├── TopOverlay (minimal - back button, menu)
│   │       ├── SideActionBar (like, comment, share, follow)
│   │       └── BottomInfoOverlay (title, description, hashtags)
│   └── LoadingIndicator (between content items)
└── GlobalOverlays
    ├── CommentModal (slides up from bottom)
    ├── ShareModal (slides up from bottom)
    └── ProfileModal (slides in from right)
```

### Full-Screen Content Specifications
- **Item Height**: Exactly `Dimensions.get('window').height` (full screen)
- **Item Width**: Exactly `Dimensions.get('window').width` (full width)
- **Scroll Behavior**: Snap to each item (like TikTok) using `snapToInterval`
- **Video Playback**: Auto-play when item is active, pause when scrolled away
- **Overlay Positioning**: Absolute positioned floating elements over content

### Navigation Integration
- **Primary Entry Point**: Set `MainFeed` as the initial screen when user is authenticated
- **Full-Screen Experience**: No traditional navigation bar - use floating back button
- **Access Other Features**: 
  - Swipe from left edge to open navigation drawer
  - Tap profile icon to access user features
  - Tap search icon for content discovery
  - Long press for additional options menu
- **Stream Integration**: Tap "Go Live" floating button to start streaming
- **Exit Strategy**: Swipe down gesture or back button to minimize/exit feed

## Components and Interfaces

### MainFeedScreen Component
```typescript
interface MainFeedScreenProps {
  navigation: NativeStackNavigationProp<RootStackParamList, 'MainFeed'>;
  route: RouteProp<RootStackParamList, 'MainFeed'>;
}

interface FeedState {
  content: FeedItem[];
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  filters: FeedFilters;
  scrollPosition: number;
}
```

### Feed Item Types
```typescript
interface BaseFeedItem {
  id: string;
  type: 'live_stream' | 'video_content' | 'project_showcase' | 'tutorial_video';
  timestamp: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    verified: boolean;
    specialties: string[];
    location?: string;
  };
  engagement: {
    likes: number;
    comments: number;
    shares: number;
    userLiked: boolean;
    userBookmarked: boolean;
  };
  media: {
    type: 'video' | 'image_carousel';
    url: string;
    thumbnail_url?: string;
    duration?: number; // for videos
    aspect_ratio: number;
  };
}

interface LiveStreamFeedItem extends BaseFeedItem {
  type: 'live_stream';
  stream: {
    title: string;
    description: string;
    viewer_count: number;
    is_live: boolean;
    channel_name: string;
    host_uid: string;
    category: string;
    difficulty_level: 'beginner' | 'intermediate' | 'advanced';
    tags: string[];
  };
}

interface VideoContentFeedItem extends BaseFeedItem {
  type: 'video_content';
  video: {
    title: string;
    description: string;
    category: string;
    difficulty_level: 'beginner' | 'intermediate' | 'advanced';
    tags: string[];
    tools_used?: string[];
    materials_used?: string[];
  };
}
```

### Feed Service Architecture
```typescript
class FeedService {
  async getFeedContent(page: number, filters: FeedFilters): Promise<FeedResponse>
  async likeContent(itemId: string): Promise<void>
  async shareContent(itemId: string): Promise<void>
  async bookmarkContent(itemId: string): Promise<void>
  async reportContent(itemId: string, reason: string): Promise<void>
  async joinLiveStream(streamData: LiveStreamData): Promise<StreamJoinResponse>
  
  // Real-time updates
  subscribeToLiveUpdates(callback: (update: FeedUpdate) => void): () => void
  
  // Video playback
  preloadNextVideos(currentIndex: number, items: FeedItem[]): void
  pauseAllVideos(): void
  playVideoAtIndex(index: number): void
}
```

### Full-Screen Item Components
```typescript
interface FullScreenContentItemProps {
  item: FeedItem;
  isActive: boolean; // whether this item is currently in viewport
  screenDimensions: { width: number; height: number };
  onLike: () => void;
  onComment: () => void;
  onShare: () => void;
  onFollow: () => void;
  onJoinStream?: () => void; // for live streams
  onViewProfile: (authorId: string) => void;
  onVideoLoad: () => void;
  onVideoError: (error: string) => void;
}

interface FullScreenFeedListProps {
  data: FeedItem[];
  onEndReached: () => void;
  refreshing: boolean;
  onRefresh: () => void;
  initialScrollIndex?: number;
  onScrollToIndexFailed: (info: any) => void;
  onViewableItemsChanged: (info: any) => void;
}

// Full-screen video player component
interface FullScreenVideoPlayerProps {
  videoUrl: string;
  isActive: boolean;
  shouldAutoPlay: boolean;
  onLoadStart: () => void;
  onLoad: () => void;
  onError: (error: any) => void;
  onProgress: (progress: any) => void;
  style: ViewStyle;
}
```

## Data Models

### Feed Content Storage
- Use existing Supabase integration for data persistence
- Implement caching layer using AsyncStorage for offline support
- Real-time subscriptions for live stream updates
- Video preloading and caching for smooth playback

### Full-Screen Feed Schema
```sql
-- Feed items table (full-screen content)
CREATE TABLE feed_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type TEXT NOT NULL CHECK (type IN ('live_stream', 'video_content', 'project_showcase', 'tutorial_video')),
  author_id UUID REFERENCES profiles(id),
  title TEXT NOT NULL,
  description TEXT,
  media_url TEXT NOT NULL, -- video URL or image URL
  thumbnail_url TEXT,
  duration INTEGER, -- in seconds for videos
  aspect_ratio DECIMAL DEFAULT 9.0/16.0, -- vertical video ratio
  category TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
  engagement JSONB DEFAULT '{"likes": 0, "comments": 0, "shares": 0, "views": 0}',
  is_featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Live stream integration (extends existing streams table)
ALTER TABLE streams ADD COLUMN feed_item_id UUID REFERENCES feed_items(id);

-- User interactions table
CREATE TABLE feed_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  feed_item_id UUID REFERENCES feed_items(id),
  interaction_type TEXT NOT NULL CHECK (interaction_type IN ('like', 'comment', 'share', 'view', 'bookmark')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, feed_item_id, interaction_type)
);
```

## Error Handling

### Network Error Handling
- Implement exponential backoff for failed requests
- Show cached content when offline
- Provide manual retry options for failed loads
- Display appropriate error states with actionable messages

### Content Loading Errors
- Graceful degradation for missing images/videos
- Placeholder content for failed loads
- Skip corrupted or invalid content items
- Log errors for monitoring and debugging

### Stream Integration Errors
- Validate stream parameters before navigation
- Handle mock stream warnings consistently
- Provide fallback for unavailable streams
- Maintain existing environment checks

## Testing Strategy

### Unit Tests
- Feed service methods (getFeedContent, likeContent, etc.)
- Content filtering and sorting logic
- Engagement interaction handlers
- Error handling scenarios

### Integration Tests
- Navigation flow to/from feed
- Stream joining from feed cards
- Real-time update handling
- Offline/online state transitions

### Performance Tests
- Scroll performance with large datasets
- Memory usage during extended scrolling
- Image loading and caching efficiency
- Network request optimization

### User Experience Tests
- Smooth scrolling on various devices
- Content discovery effectiveness
- Filter and search functionality
- Accessibility compliance

## Implementation Phases

### Phase 1: Core Feed Structure
- Create MainFeedScreen component
- Implement basic FlatList with mock data
- Add navigation integration
- Create basic card components

### Phase 2: Content Integration
- Integrate with existing stream data
- Add project update content type
- Implement engagement interactions
- Add filtering capabilities

### Phase 3: Performance Optimization
- Implement lazy loading and caching
- Add infinite scroll optimization
- Optimize image loading
- Add scroll position persistence

### Phase 4: Advanced Features
- Real-time content updates
- Personalization algorithms
- Advanced filtering options
- Social features enhancement

## Technical Considerations

### Performance Optimizations
- **FlatList Configuration**:
  - `getItemLayout` with fixed full-screen height for optimal performance
  - `pagingEnabled={true}` for snap-to-item behavior
  - `snapToInterval={screenHeight}` for precise scrolling
  - `decelerationRate="fast"` for TikTok-like scroll feel
  - `removeClippedSubviews={true}` for memory efficiency
  - `maxToRenderPerBatch={3}` to limit simultaneous renders
  - `windowSize={5}` to optimize memory usage
- **Video Performance**:
  - Preload next 2 videos while current is playing
  - Pause all videos except the active one
  - Use video thumbnails as placeholders
  - Implement video caching for smooth playback
- **Memory Management**:
  - Unload videos that are more than 3 items away
  - Use image compression for thumbnails
  - Implement garbage collection for unused media

### Accessibility
- Proper semantic labels for screen readers
- Keyboard navigation support
- High contrast mode compatibility
- Font scaling support

### Platform Considerations
- iOS/Android specific optimizations
- Safe area handling
- Platform-specific navigation patterns
- Native performance optimizations

### Security
- Content validation and sanitization
- Rate limiting for interactions
- User permission checks
- Secure image loading

## Integration Points

### Existing Components
- Reuse StreamControls for embedded stream previews
- Leverage existing navigation patterns
- Use current authentication context
- Integrate with existing error boundaries

### API Integration
- Extend existing streamService for feed content
- Use current Supabase client configuration
- Maintain existing error handling patterns
- Leverage current caching strategies

### State Management
- Use React hooks for local state
- Integrate with existing auth context
- Maintain scroll position in navigation state
- Handle real-time updates efficiently