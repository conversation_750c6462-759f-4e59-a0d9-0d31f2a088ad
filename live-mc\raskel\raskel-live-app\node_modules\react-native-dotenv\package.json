{"name": "react-native-dotenv", "version": "3.4.11", "description": "Load environment variables using import statements.", "repository": "github:goatandsheep/react-native-dotenv", "homepage": "https://github.com/goatandsheep/react-native-dotenv", "bugs": "https://github.com/goatandsheep/react-native-dotenv/issues", "main": "index.js", "scripts": {"lint": "xo", "lint:fix": "xo --fix", "test": "jest"}, "keywords": ["dotenv", "babel-plugin", "babel", "dotenv-flow", "react", "react-native", "config", "env", "12factor"], "dependencies": {"dotenv": "^16.4.5"}, "devDependencies": {"@babel/core": "^7.23.9", "codecov": "^3.8.3", "jest": "29.7.0", "jest-junit": "^16.0.0", "xo": "^0.57.0"}, "author": "<PERSON><PERSON>", "license": "MIT", "files": ["index.js"], "jest": {"testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/", "/__fixtures__/"], "reporters": ["default", ["jest-junit", {"outputDirectory": "reports/tests"}]], "collectCoverage": true, "collectCoverageFrom": ["index.js"], "coverageReporters": ["lcov", "text-summary"]}, "peerDependencies": {"@babel/runtime": "^7.20.6"}, "resolutions": {"@babel/core": "^7.20.5", "@babel/runtime": "^7.20.6"}, "xo": {"semicolon": false, "space": 2, "overrides": [{"files": "**/*", "rules": {"unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "node/prefer-global/process": "off", "n/prefer-global/process": "off", "prefer-destructuring": "off"}}, {"files": "__tests__/**/*.js", "env": ["jest"]}, {"files": "__tests__/__fixtures__/**/*.js", "rules": {"import/no-unresolved": ["error", {"ignore": ["@env", "foo", "react-native-dotenv"]}], "unicorn/import-style": "off", "no-unused-vars": "off"}}]}}