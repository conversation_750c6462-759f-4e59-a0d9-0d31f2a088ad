
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsCpp.js
 */

#include <react/renderer/components/AgoraRtcNgSpec/Props.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>

namespace facebook::react {

AgoraRtcSurfaceViewProps::AgoraRtcSurfaceViewProps(
    const PropsParserContext &context,
    const AgoraRtcSurfaceViewProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    callApi(convertRawProp(context, rawProps, "callApi", sourceProps.callApi, {})),
    zOrderOnTop(convertRawProp(context, rawProps, "zOrderOnTop", sourceProps.zOrderOnTop, {false})),
    zOrderMediaOverlay(convertRawProp(context, rawProps, "zOrderMediaOverlay", sourceProps.zOrderMediaOverlay, {false})) {}
    
#ifdef RN_SERIALIZABLE_STATE
ComponentName AgoraRtcSurfaceViewProps::getDiffPropsImplementationTarget() const {
  return "AgoraRtcSurfaceView";
}

folly::dynamic AgoraRtcSurfaceViewProps::getDiffProps(
    const Props* prevProps) const {
  static const auto defaultProps = AgoraRtcSurfaceViewProps();
  const AgoraRtcSurfaceViewProps* oldProps = prevProps == nullptr
      ? &defaultProps
      : static_cast<const AgoraRtcSurfaceViewProps*>(prevProps);
  if (this == oldProps) {
    return folly::dynamic::object();
  }
  folly::dynamic result = HostPlatformViewProps::getDiffProps(prevProps);
  
  if (callApi != oldProps->callApi) {
    result["callApi"] = toDynamic(callApi);
  }
    
  if (zOrderOnTop != oldProps->zOrderOnTop) {
    result["zOrderOnTop"] = zOrderOnTop;
  }
    
  if (zOrderMediaOverlay != oldProps->zOrderMediaOverlay) {
    result["zOrderMediaOverlay"] = zOrderMediaOverlay;
  }
  return result;
}
#endif
AgoraRtcTextureViewProps::AgoraRtcTextureViewProps(
    const PropsParserContext &context,
    const AgoraRtcTextureViewProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    callApi(convertRawProp(context, rawProps, "callApi", sourceProps.callApi, {})) {}
    
#ifdef RN_SERIALIZABLE_STATE
ComponentName AgoraRtcTextureViewProps::getDiffPropsImplementationTarget() const {
  return "AgoraRtcTextureView";
}

folly::dynamic AgoraRtcTextureViewProps::getDiffProps(
    const Props* prevProps) const {
  static const auto defaultProps = AgoraRtcTextureViewProps();
  const AgoraRtcTextureViewProps* oldProps = prevProps == nullptr
      ? &defaultProps
      : static_cast<const AgoraRtcTextureViewProps*>(prevProps);
  if (this == oldProps) {
    return folly::dynamic::object();
  }
  folly::dynamic result = HostPlatformViewProps::getDiffProps(prevProps);
  
  if (callApi != oldProps->callApi) {
    result["callApi"] = toDynamic(callApi);
  }
  return result;
}
#endif

} // namespace facebook::react
