{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.9.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "VisionCamera", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "VisionCamera::@6890427a1f51a3e7e1df", "jsonFile": "target-VisionCamera-Debug-0c660c6ef374ff6fac32.json", "name": "VisionCamera", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-vision-camera/android/.cxx/Debug/3h292v2g/arm64-v8a", "source": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-vision-camera/android"}, "version": {"major": 2, "minor": 3}}