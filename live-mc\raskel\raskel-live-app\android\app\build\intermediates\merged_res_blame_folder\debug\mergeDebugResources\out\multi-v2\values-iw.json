{"logs": [{"outputFile": "com.fishkaster.app-mergeDebugResources-106:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\72fd1eaed4249ec9561f48df05200d45\\transformed\\play-services-basement-18.4.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "6605", "endColumns": "117", "endOffsets": "6718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\02bec446bb5b07b8548f83ec26b6ae83\\transformed\\appcompat-1.7.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1181,1286,1386,1494,1578,1680,1796,1875,1953,2044,2138,2232,2326,2426,2519,2614,2707,2798,2890,2971,3076,3179,3277,3382,3484,3586,3740,18541", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "1281,1381,1489,1573,1675,1791,1870,1948,2039,2133,2227,2321,2421,2514,2609,2702,2793,2885,2966,3071,3174,3272,3377,3479,3581,3735,3832,18618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,341,422,490,558,636,714,796,875,946,1024,1104,1177,1257,1335,1410,1482,1554,1641,1712,1791,1860", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,197,267,336,417,485,553,631,709,791,870,941,1019,1099,1172,1252,1330,1405,1477,1549,1636,1707,1786,1855,1930"}, "to": {"startLines": "55,74,155,162,163,167,181,182,183,233,234,237,238,245,246,247,249,250,252,254,255,257,260,262,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3837,5562,12249,12677,12746,13052,14023,14091,14169,18071,18153,18392,18463,19009,19089,19162,19316,19394,19547,19695,19767,19955,20171,20369,20438", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3901,5635,12314,12741,12822,13115,14086,14164,14242,18148,18227,18458,18536,19084,19157,19237,19389,19464,19614,19762,19849,20021,20245,20433,20508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\29b62c1e8951214205c1999e10893e42\\transformed\\foundation-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,222", "endColumns": "79,86,87", "endOffsets": "130,217,305"}, "to": {"startLines": "56,264,265", "startColumns": "4,4,4", "startOffsets": "3906,20513,20600", "endColumns": "79,86,87", "endOffsets": "3981,20595,20683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\5deb70712b1e9df4e3892e963cadfc60\\transformed\\material-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1077,1142,1230,1300,1363,1455,1518,1578,1637,1700,1761,1815,1917,1974,2033,2087,2155,2266,2347,2422,2509,2589,2671,2803,2874,2947,3071,3159,3235,3288,3342,3408,3481,3557,3628,3706,3776,3851,3933,4001,4102,4187,4257,4347,4438,4512,4585,4674,4725,4806,4873,4955,5040,5102,5166,5229,5297,5391,5486,5576,5673,5730,5788,5863,5945,6020", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "306,383,458,535,635,726,819,932,1012,1072,1137,1225,1295,1358,1450,1513,1573,1632,1695,1756,1810,1912,1969,2028,2082,2150,2261,2342,2417,2504,2584,2666,2798,2869,2942,3066,3154,3230,3283,3337,3403,3476,3552,3623,3701,3771,3846,3928,3996,4097,4182,4252,4342,4433,4507,4580,4669,4720,4801,4868,4950,5035,5097,5161,5224,5292,5386,5481,5571,5668,5725,5783,5858,5940,6015,6091"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,98,99,151,161,166,168,169,170,171,172,173,174,175,176,177,178,179,180,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,231,240,241,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,3986,4063,4138,4215,4315,5103,5196,5309,8127,8187,11853,12607,12989,13120,13212,13275,13335,13394,13457,13518,13572,13674,13731,13790,13844,13912,14247,14328,14403,14490,14570,14652,14784,14855,14928,15052,15140,15216,15269,15323,15389,15462,15538,15609,15687,15757,15832,15914,15982,16083,16168,16238,16328,16419,16493,16566,16655,16706,16787,16854,16936,17021,17083,17147,17210,17278,17372,17467,17557,17654,17711,17934,18623,18705,18849", "endLines": "27,57,58,59,60,61,69,70,71,98,99,151,161,166,168,169,170,171,172,173,174,175,176,177,178,179,180,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,231,240,241,243", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "1176,4058,4133,4210,4310,4401,5191,5304,5384,8182,8247,11936,12672,13047,13207,13270,13330,13389,13452,13513,13567,13669,13726,13785,13839,13907,14018,14323,14398,14485,14565,14647,14779,14850,14923,15047,15135,15211,15264,15318,15384,15457,15533,15604,15682,15752,15827,15909,15977,16078,16163,16233,16323,16414,16488,16561,16650,16701,16782,16849,16931,17016,17078,17142,17205,17273,17367,17462,17552,17649,17706,17764,18004,18700,18775,18920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8aa23bce709bfe96db10568360097556\\transformed\\core-1.15.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "62,63,64,65,66,67,68,256", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4406,4500,4602,4699,4796,4897,4997,19854", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "4495,4597,4694,4791,4892,4992,5098,19950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3ef9533b8d5a0884be92f5c6e6c0dbd2\\transformed\\exoplayer-ui-2.18.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,582,875,954,1032,1108,1193,1277,1339,1401,1490,1576,1641,1705,1768,1836,1956,2066,2184,2255,2332,2401,2462,2552,2641,2705,2768,2822,2893,2941,3002,3061,3128,3189,3252,3313,3370,3436,3500,3566,3618,3672,3740,3808", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,577,870,949,1027,1103,1188,1272,1334,1396,1485,1571,1636,1700,1763,1831,1951,2061,2179,2250,2327,2396,2457,2547,2636,2700,2763,2817,2888,2936,2997,3056,3123,3184,3247,3308,3365,3431,3495,3561,3613,3667,3735,3803,3857"}, "to": {"startLines": "2,11,17,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,677,8252,8331,8409,8485,8570,8654,8716,8778,8867,8953,9018,9082,9145,9213,9333,9443,9561,9632,9709,9778,9839,9929,10018,10082,10759,10813,10884,10932,10993,11052,11119,11180,11243,11304,11361,11427,11491,11557,11609,11663,11731,11799", "endLines": "10,16,22,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "374,672,965,8326,8404,8480,8565,8649,8711,8773,8862,8948,9013,9077,9140,9208,9328,9438,9556,9627,9704,9773,9834,9924,10013,10077,10140,10808,10879,10927,10988,11047,11114,11175,11238,11299,11356,11422,11486,11552,11604,11658,11726,11794,11848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\30f51c8a36c85c07b7e3abc1db11ccb6\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,275,359,452,547,630,707,792,878,957,1035,1117,1186,1270,1344,1422,1498,1572,1643", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,83,73,77,75,73,70,118", "endOffsets": "270,354,447,542,625,702,787,873,952,1030,1112,1181,1265,1339,1417,1493,1567,1638,1757"}, "to": {"startLines": "72,73,95,96,97,164,165,229,230,235,236,242,244,248,251,253,258,259,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5389,5478,7856,7949,8044,12827,12904,17769,17855,18232,18310,18780,18925,19242,19469,19619,20026,20100,20250", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,83,73,77,75,73,70,118", "endOffsets": "5473,5557,7944,8039,8122,12899,12984,17850,17929,18305,18387,18844,19004,19311,19542,19690,20095,20166,20364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f198488ab5a5112dc7ed7a95c5218eda\\transformed\\browser-1.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "93,152,153,154", "startColumns": "4,4,4,4", "startOffsets": "7711,11941,12041,12147", "endColumns": "90,99,105,101", "endOffsets": "7797,12036,12142,12244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\025e05451a60883d4b88ffb9b910f451\\transformed\\android-image-cropper-4.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,109,153,217,278,345,397", "endColumns": "53,43,63,60,66,51,61", "endOffsets": "104,148,212,273,340,392,454"}, "to": {"startLines": "94,156,157,158,159,160,232", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7802,12319,12363,12427,12488,12555,18009", "endColumns": "53,43,63,60,66,51,61", "endOffsets": "7851,12358,12422,12483,12550,12602,18066"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cf4e6ff07dbeac5bcd8d8e9bb606754a\\transformed\\exoplayer-core-2.18.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "124,125,126,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10145,10210,10269,10336,10401,10475,10537,10617,10697", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "10205,10264,10331,10396,10470,10532,10612,10692,10754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\1ff7e39d1dce962b72e0e33d3ff513a9\\transformed\\play-services-base-18.5.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "75,76,77,78,79,80,81,82,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5640,5743,5897,6022,6126,6265,6390,6502,6723,6859,6963,7108,7231,7365,7510,7570,7630", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "5738,5892,6017,6121,6260,6385,6497,6600,6854,6958,7103,7226,7360,7505,7565,7625,7706"}}]}]}