C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\.cxx\Debug\20483q55\prefab\x86_64\prefab\lib\x86_64-linux-android\cmake\fbjni\fbjniConfig.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\.cxx\Debug\20483q55\prefab\x86_64\prefab\lib\x86_64-linux-android\cmake\fbjni\fbjniConfigVersion.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\.cxx\Debug\20483q55\prefab\x86_64\prefab\lib\x86_64-linux-android\cmake\ReactAndroid\ReactAndroidConfig.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\.cxx\Debug\20483q55\prefab\x86_64\prefab\lib\x86_64-linux-android\cmake\ReactAndroid\ReactAndroidConfigVersion.cmake
C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\CMakeLists.txt