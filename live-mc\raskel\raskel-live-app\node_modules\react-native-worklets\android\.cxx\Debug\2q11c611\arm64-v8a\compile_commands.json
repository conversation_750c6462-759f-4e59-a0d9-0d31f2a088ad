[{"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\30ba5a965fc93da46f5459dfad50d214\\worklets\\AnimationFrameQueue\\AnimationFrameBatchinator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\AnimationFrameQueue\\AnimationFrameBatchinator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\AnimationFrameQueue\\AnimationFrameBatchinator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\e751c91732a5ab9c8eacba856b5cb7bb\\cpp\\worklets\\NativeModules\\JSIWorkletsModuleProxy.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\NativeModules\\JSIWorkletsModuleProxy.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\NativeModules\\JSIWorkletsModuleProxy.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Resources\\SynchronizableUnpacker.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Resources\\SynchronizableUnpacker.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Resources\\SynchronizableUnpacker.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Resources\\ValueUnpacker.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Resources\\ValueUnpacker.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Resources\\ValueUnpacker.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\RunLoop\\AsyncQueueImpl.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\RunLoop\\AsyncQueueImpl.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\RunLoop\\AsyncQueueImpl.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\RunLoop\\EventLoop.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\RunLoop\\EventLoop.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\RunLoop\\EventLoop.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\SharedItems\\Serializable.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\SharedItems\\Serializable.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\SharedItems\\Serializable.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\SharedItems\\Synchronizable.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\SharedItems\\Synchronizable.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\SharedItems\\Synchronizable.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\SharedItems\\SynchronizableAccess.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\SharedItems\\SynchronizableAccess.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\SharedItems\\SynchronizableAccess.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\FeatureFlags.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\FeatureFlags.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\FeatureFlags.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\VersionUtils.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\VersionUtils.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\VersionUtils.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\WorkletsJSIUtils.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\WorkletsJSIUtils.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\WorkletsJSIUtils.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\Tools\\WorkletsVersion.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\WorkletsVersion.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\Tools\\WorkletsVersion.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\e751c91732a5ab9c8eacba856b5cb7bb\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\WorkletRuntime\\RuntimeData.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\RuntimeData.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\RuntimeData.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\WorkletRuntime\\RuntimeManager.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\RuntimeManager.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\RuntimeManager.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\WorkletRuntime\\UIRuntimeDecorator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\UIRuntimeDecorator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\UIRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletHermesRuntime.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletHermesRuntime.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletHermesRuntime.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\af42619d32e2b556c34d3987e9e9347a\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\e751c91732a5ab9c8eacba856b5cb7bb\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dworklets_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/3cefa1ac864c6b2d83e4f85211b38ee6/transformed/hermes-android-0.81.4-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81         -DWORKLETS_VERSION=0.5.1         -DWORKLETS_FEATURE_FLAGS=\"[RUNTIME_TEST_FLAG:false][IOS_DYNAMIC_FRAMERATE_ENABLED:true]\" -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\worklets.dir\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp"}]