{"buildFiles": ["C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\.cxx\\Debug\\b1j5m1r3\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\.cxx\\Debug\\b1j5m1r3\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_AgoraRtcNgSpec::@b3e2fa00649b55f06a11": {"artifactName": "react_codegen_AgoraRtcNgSpec", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86_64", "output": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_RNWorkletsSpec::@c787b30dd2b0f948dced": {"artifactName": "react_codegen_RNWorkletsSpec", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86_64", "output": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86_64", "output": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\x86_64\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_rnworklets::@68f58d84d4754f193387": {"artifactName": "react_codegen_rnworklets", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "x86_64", "runtimeFiles": []}}}