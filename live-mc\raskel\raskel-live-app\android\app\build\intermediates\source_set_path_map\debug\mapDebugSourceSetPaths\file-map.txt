com.fishkaster.app-purchases-9.7.0-0 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\res
com.fishkaster.app-android-image-cropper-4.6.0-1 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\res
com.fishkaster.app-appcompat-1.7.0-2 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\02bec446bb5b07b8548f83ec26b6ae83\transformed\appcompat-1.7.0\res
com.fishkaster.app-appcompat-resources-1.7.0-3 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\02d8a2704c6918921ed1201e93ea593d\transformed\appcompat-resources-1.7.0\res
com.fishkaster.app-play-services-maps-18.2.0-4 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\res
com.fishkaster.app-ains-4.5.2-5 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0deec9455bf20b0529adda60023b5e34\transformed\ains-4.5.2\res
com.fishkaster.app-audio-beauty-4.5.2-6 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10d5afe10da867477c209167f6155891\transformed\audio-beauty-4.5.2\res
com.fishkaster.app-camera-lifecycle-1.5.0-alpha03-7 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\132cd50f6bea34a6b5815786fa04ba94\transformed\camera-lifecycle-1.5.0-alpha03\res
com.fishkaster.app-core-ktx-1.15.0-8 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\13e56a2ae02ed4f70cf0844a94cbdd84\transformed\core-ktx-1.15.0\res
com.fishkaster.app-material-ripple-1.0.0-9 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1afa0e82c401e8214283b8519a847771\transformed\material-ripple-1.0.0\res
com.fishkaster.app-aiaec-ll-4.5.2-10 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c5136930f9417c837c1099137c692f8\transformed\aiaec-ll-4.5.2\res
com.fishkaster.app-lifecycle-livedata-core-ktx-2.9.0-11 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1f949f27dd5a336d4d974516dad185fc\transformed\lifecycle-livedata-core-ktx-2.9.0\res
com.fishkaster.app-emoji2-1.4.0-12 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\res
com.fishkaster.app-play-services-base-18.5.0-13 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\res
com.fishkaster.app-savedstate-compose-release-14 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\20008ac0d85dfe6ca78de6e531f8c41e\transformed\savedstate-compose-release\res
com.fishkaster.app-foundation-release-15 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\29b62c1e8951214205c1999e10893e42\transformed\foundation-release\res
com.fishkaster.app-activity-ktx-1.10.0-16 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ab8996d72179ddcbbdde9962833165a\transformed\activity-ktx-1.10.0\res
com.fishkaster.app-viewpager2-1.0.0-17 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2af37017b7d296925114ff313d334e1a\transformed\viewpager2-1.0.0\res
com.fishkaster.app-full-rtc-basic-4.5.2-18 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2edb3e0560fcd9ed0b85d3876a990ca8\transformed\full-rtc-basic-4.5.2\res
com.fishkaster.app-lifecycle-viewmodel-ktx-2.9.0-19 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30cb6c01264a0379f15b8e152aac2862\transformed\lifecycle-viewmodel-ktx-2.9.0\res
com.fishkaster.app-aiaec-4.5.2-20 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30deb1b687fbddf5338393a4bce767aa\transformed\aiaec-4.5.2\res
com.fishkaster.app-ui-release-21 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30f51c8a36c85c07b7e3abc1db11ccb6\transformed\ui-release\res
com.fishkaster.app-annotation-experimental-1.4.1-22 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\33a3d926fb159696c4a593553230961e\transformed\annotation-experimental-1.4.1\res
com.fishkaster.app-ains-ll-4.5.2-23 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34e1e07fe2d26e3bb1cd9a723f962d79\transformed\ains-ll-4.5.2\res
com.fishkaster.app-customview-poolingcontainer-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35b862dd04998bdf6ac2b60e22952262\transformed\customview-poolingcontainer-1.0.0\res
com.fishkaster.app-full-content-inspect-4.5.2-25 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3865c2c16ec5a7e07df6a8d3069e7058\transformed\full-content-inspect-4.5.2\res
com.fishkaster.app-navigation-common-release-26 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3d447d27eb34477eb4521f9eaad9a4d8\transformed\navigation-common-release\res
com.fishkaster.app-camera-core-1.5.0-alpha03-27 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3e67b0964ce1fc5a9b7c1a5cc158a056\transformed\camera-core-1.5.0-alpha03\res
com.fishkaster.app-exoplayer-ui-2.18.1-28 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ef9533b8d5a0884be92f5c6e6c0dbd2\transformed\exoplayer-ui-2.18.1\res
com.fishkaster.app-core-viewtree-1.0.0-29 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3f625b92d9561c1e638a83e469a388fa\transformed\core-viewtree-1.0.0\res
com.fishkaster.app-cardview-1.0.0-30 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ffc0aaaa2dfc7c60cb5170492960db0\transformed\cardview-1.0.0\res
com.fishkaster.app-lifecycle-viewmodel-savedstate-release-31 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\43799691d059536940acde5eccb53fc1\transformed\lifecycle-viewmodel-savedstate-release\res
com.fishkaster.app-spatial-audio-4.5.2-32 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4489de5ddeaa36a54534c73b55773983\transformed\spatial-audio-4.5.2\res
com.fishkaster.app-android-maps-utils-3.8.2-33 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\res
com.fishkaster.app-coil-core-release-34 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45ea012571f679db5c2a24848ffb6a48\transformed\coil-core-release\res
com.fishkaster.app-aosl-1.2.13.1-35 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47a8c24638f7806a7d7949740d88a970\transformed\aosl-1.2.13.1\res
com.fishkaster.app-full-video-av1-codec-dec-4.5.2-36 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4d9641c210f9be34b7515035d8b90c78\transformed\full-video-av1-codec-dec-4.5.2\res
com.fishkaster.app-full-video-codec-dec-4.5.2-37 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4fd0bc4d1a348ef958e55abc3160a9a5\transformed\full-video-codec-dec-4.5.2\res
com.fishkaster.app-full-virtual-background-4.5.2-38 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5af46a4618534e09fdfe491394fc563e\transformed\full-virtual-background-4.5.2\res
com.fishkaster.app-material-1.12.0-39 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5deb70712b1e9df4e3892e963cadfc60\transformed\material-1.12.0\res
com.fishkaster.app-material-1.0.0-40 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\63ee2875b4892a910b8c00f2fba34dc7\transformed\material-1.0.0\res
com.fishkaster.app-documentfile-1.1.0-41 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\669e00e915a3eab7cb40abd1fd29b039\transformed\documentfile-1.1.0\res
com.fishkaster.app-full-video-codec-enc-4.5.2-42 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67013ada8d40c23f135a4cae1b3ec60c\transformed\full-video-codec-enc-4.5.2\res
com.fishkaster.app-react-android-0.81.4-debug-43 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\res
com.fishkaster.app-ui-graphics-release-44 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6ca79c161877f883d691c1fc6f23e634\transformed\ui-graphics-release\res
com.fishkaster.app-play-services-basement-18.4.0-45 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72fd1eaed4249ec9561f48df05200d45\transformed\play-services-basement-18.4.0\res
com.fishkaster.app-full-vqa-4.5.2-46 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799ee7dcb1efc559d5d56638065b9294\transformed\full-vqa-4.5.2\res
com.fishkaster.app-core-runtime-2.2.0-47 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7b233d7c842b8eaddaa18bfcc22da232\transformed\core-runtime-2.2.0\res
com.fishkaster.app-work-runtime-2.7.1-48 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\res
com.fishkaster.app-billing-8.0.0-49 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\res
com.fishkaster.app-lifecycle-process-2.9.0-50 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\res
com.fishkaster.app-full-voice-drive-4.5.2-51 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\871cf47407cf8dc1249fb74b735ac3ab\transformed\full-voice-drive-4.5.2\res
com.fishkaster.app-profileinstaller-1.4.1-52 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\res
com.fishkaster.app-core-1.15.0-53 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\res
com.fishkaster.app-full-face-detect-4.5.2-54 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8af0240122297b32cfeda1bd179812f3\transformed\full-face-detect-4.5.2\res
com.fishkaster.app-graphics-path-1.0.1-55 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e33ac33f6428f1495fb654d3382f42e\transformed\graphics-path-1.0.1\res
com.fishkaster.app-lifecycle-viewmodel-compose-release-56 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e422563754deea564571ebeafe3fce2\transformed\lifecycle-viewmodel-compose-release\res
com.fishkaster.app-savedstate-release-57 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e9ab3e6c2626eb33e24c85c0cc67dfc\transformed\savedstate-release\res
com.fishkaster.app-constraintlayout-2.0.1-58 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\915ded6ad2ba3f8c7995501c34ddf542\transformed\constraintlayout-2.0.1\res
com.fishkaster.app-lifecycle-runtime-release-59 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\937dea5d455b8c8024fcae436543927e\transformed\lifecycle-runtime-release\res
com.fishkaster.app-lifecycle-viewmodel-release-60 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99c50cc3f06d98f86ba0abc90f87fc08\transformed\lifecycle-viewmodel-release\res
com.fishkaster.app-lifecycle-livedata-2.9.0-61 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c03e1285a57ce224067fcefd337b45f\transformed\lifecycle-livedata-2.9.0\res
com.fishkaster.app-recyclerview-1.2.1-62 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9de930f2063b42f1e1871b63cbc7a1a8\transformed\recyclerview-1.2.1\res
com.fishkaster.app-camera-video-1.5.0-alpha03-63 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9e234ec31c85d3e25abc275beb1c1d2b\transformed\camera-video-1.5.0-alpha03\res
com.fishkaster.app-lifecycle-service-2.9.0-64 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9f2371705f9dc6c5890cd95f38da38de\transformed\lifecycle-service-2.9.0\res
com.fishkaster.app-camera-view-1.5.0-alpha03-65 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a354e5acf6eadb17d26a8cdfd21c03cf\transformed\camera-view-1.5.0-alpha03\res
com.fishkaster.app-full-video-av1-codec-enc-4.5.2-66 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3dae6a4cabb556ae0808dcaab442c95\transformed\full-video-av1-codec-enc-4.5.2\res
com.fishkaster.app-camera-extensions-1.5.0-alpha03-67 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\res
com.fishkaster.app-swiperefreshlayout-1.1.0-68 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a71771dac3d408e1edb1a8cee7c39a2b\transformed\swiperefreshlayout-1.1.0\res
com.fishkaster.app-full-face-capture-4.5.2-69 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a93c16f569432f4b7d0fb0bc65691921\transformed\full-face-capture-4.5.2\res
com.fishkaster.app-screen-capture-4.5.2-70 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b1b08ff95de0e1cd1dcbbe1cae19c3e3\transformed\screen-capture-4.5.2\res
com.fishkaster.app-expo.modules.filesystem-19.0.14-71 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2d138d3c2f844c22648f38321293112\transformed\expo.modules.filesystem-19.0.14\res
com.fishkaster.app-tracing-ktx-1.2.0-72 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b3c5dbd5785893fc9c2ff5e8103b581e\transformed\tracing-ktx-1.2.0\res
com.fishkaster.app-autofill-1.1.0-73 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b748c3e0fd2d720d73ec351f95e1e7f0\transformed\autofill-1.1.0\res
com.fishkaster.app-glide-4.16.0-74 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\baf9fd92db1dbf86434347a684118ae9\transformed\glide-4.16.0\res
com.fishkaster.app-core-debug-75 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bb478696cb4d967b4b7b530fa737c4ad\transformed\core-debug\res
com.fishkaster.app-emoji2-views-helper-1.4.0-76 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\be48fb11a8ff5d51d812b547bbe33b68\transformed\emoji2-views-helper-1.4.0\res
com.fishkaster.app-expo.modules.imagepicker-17.0.8-77 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\res
com.fishkaster.app-transition-1.5.0-78 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2d58965c6e5688e18e3d3211485660e\transformed\transition-1.5.0\res
com.fishkaster.app-activity-compose-1.10.0-79 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c5547f6898b8f18659388e7f05e2c211\transformed\activity-compose-1.10.0\res
com.fishkaster.app-material-icons-core-1.0.0-80 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ca620201fa38c0266f7e5566ad638d04\transformed\material-icons-core-1.0.0\res
com.fishkaster.app-exoplayer-core-2.18.1-81 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf4e6ff07dbeac5bcd8d8e9bb606754a\transformed\exoplayer-core-2.18.1\res
com.fishkaster.app-iris-rtc-4.5.2-build.1-82 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfa89d7c2b589e03630064b12eb207dc\transformed\iris-rtc-4.5.2-build.1\res
com.fishkaster.app-activity-1.10.0-83 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d18d5d5c3dd684bfb5d9d3674fe557d6\transformed\activity-1.10.0\res
com.fishkaster.app-lifecycle-livedata-core-2.9.0-84 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2752ce4275a3cf6149fdad46ba8503b\transformed\lifecycle-livedata-core-2.9.0\res
com.fishkaster.app-fragment-ktx-1.6.1-85 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d756d0658cd7f25a8fcfd0e49b466b30\transformed\fragment-ktx-1.6.1\res
com.fishkaster.app-camera-camera2-1.5.0-alpha03-86 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\res
com.fishkaster.app-drawee-3.6.0-87 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dbcfd095fcd29cdf02774208a30fff53\transformed\drawee-3.6.0\res
com.fishkaster.app-navigation-compose-release-88 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dc22f65dfa4a5a700cc6f6e7903b2e8c\transformed\navigation-compose-release\res
com.fishkaster.app-startup-runtime-1.1.1-89 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd6d37c418f70c7ed28293ed75a6ac76\transformed\startup-runtime-1.1.1\res
com.fishkaster.app-media-1.4.3-90 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\de6fe266d05e9e5ac9e926be6e2f70e0\transformed\media-1.4.3\res
com.fishkaster.app-expo.modules.av-16.0.7-91 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e01f8237b96e5856cbafa2aab2342fa4\transformed\expo.modules.av-16.0.7\res
com.fishkaster.app-coordinatorlayout-1.2.0-92 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3730efb4e7abb0e262b3cce10c3babd\transformed\coordinatorlayout-1.2.0\res
com.fishkaster.app-savedstate-ktx-1.3.0-93 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e5b3795fc7caaf9facabeaaa86551164\transformed\savedstate-ktx-1.3.0\res
com.fishkaster.app-tracing-1.2.0-94 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e82331a0e144b8b89cc7da84e5be6620\transformed\tracing-1.2.0\res
com.fishkaster.app-lifecycle-runtime-compose-release-95 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb95318c1eff87dd8a091701a096f6ad\transformed\lifecycle-runtime-compose-release\res
com.fishkaster.app-navigation-runtime-release-96 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ecf046a9f1c844d96a141a1f6d88232d\transformed\navigation-runtime-release\res
com.fishkaster.app-browser-1.6.0-97 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f198488ab5a5112dc7ed7a95c5218eda\transformed\browser-1.6.0\res
com.fishkaster.app-expo.modules.systemui-6.0.7-98 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f616f319249bece65e349dd7b5e64d5d\transformed\expo.modules.systemui-6.0.7\res
com.fishkaster.app-fragment-1.6.1-99 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f91835d5b2b014d79603c3f3ee79cc74\transformed\fragment-1.6.1\res
com.fishkaster.app-androidsvg-aar-1.4-100 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f9bb7228a7fc828ca9611a7f5e7f89c5\transformed\androidsvg-aar-1.4\res
com.fishkaster.app-drawerlayout-1.1.1-101 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fb1298e98d569c6243725248e88abb96\transformed\drawerlayout-1.1.1\res
com.fishkaster.app-full-sdk-4.5.2-102 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fc304888e2ec161dee6b1e644b213bfa\transformed\full-sdk-4.5.2\res
com.fishkaster.app-clear-vision-4.5.2-103 C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd626e99e262b25795c754ac8b8c2825\transformed\clear-vision-4.5.2\res
com.fishkaster.app-pngs-104 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\build\generated\res\pngs\debug
com.fishkaster.app-resValues-105 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\build\generated\res\resValues\debug
com.fishkaster.app-packageDebugResources-106 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.fishkaster.app-packageDebugResources-107 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.fishkaster.app-debug-108 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.fishkaster.app-debug-109 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\res
com.fishkaster.app-main-110 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\res
com.fishkaster.app-debug-111 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-112 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-113 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-client\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-114 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-115 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-116 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-117 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-118 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-119 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-120 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-121 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-122 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-123 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-maps\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-124 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-purchases\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-125 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-126 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-127 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-128 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-129 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.fishkaster.app-debug-130 C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets\android\build\intermediates\packaged_res\debug\packageDebugResources
