{"archive": {}, "artifacts": [{"path": "src/fabric/libfabric.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_link_libraries", "target_include_directories", "target_precompile_headers"], "files": ["src/fabric/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 7, "parent": 0}, {"command": 1, "file": 0, "line": 16, "parent": 0}, {"command": 2, "file": 0, "line": 36, "parent": 0}, {"command": 3, "file": 0, "line": 30, "parent": 0}, {"command": 2, "file": 0, "line": 42, "parent": 0}, {"command": 4, "file": 0, "line": 12, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=81 -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 2, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 2, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 2, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}, {"backtrace": 3, "fragment": "-O2"}, {"backtrace": 3, "fragment": "-frtti"}, {"backtrace": 3, "fragment": "-fexceptions"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-fstack-protector-all"}, {"backtrace": 3, "fragment": "-DUSE_HERMES=0"}, {"backtrace": 3, "fragment": "-DUNIT_TEST=0"}, {"backtrace": 3, "fragment": "-DIS_NEW_ARCHITECTURE_ENABLED=1"}, {"backtrace": 3, "fragment": "-DRN_FABRIC_ENABLED=1"}, {"backtrace": 3, "fragment": "-DRN_SERIALIZABLE_STATE=1"}, {"fragment": "-std=gnu++20"}, {"fragment": "-Winvalid-pch -fpch-instantiate-templates -Xclang -emit-pch -Xclang -include -Xclang C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/335l6r1b/arm64-v8a/src/fabric/CMakeFiles/fabric.dir/cmake_pch.hxx -x c++-header"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon"}, {"backtrace": 4, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp/fabric"}, {"backtrace": 4, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include/react/fabric"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "precompileHeaders": [{"backtrace": 6, "header": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/ExpoHeader.pch"}], "sourceIndexes": [0], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, {"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=81 -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 2, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 2, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 2, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}, {"backtrace": 3, "fragment": "-O2"}, {"backtrace": 3, "fragment": "-frtti"}, {"backtrace": 3, "fragment": "-fexceptions"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-fstack-protector-all"}, {"backtrace": 3, "fragment": "-DUSE_HERMES=0"}, {"backtrace": 3, "fragment": "-DUNIT_TEST=0"}, {"backtrace": 3, "fragment": "-DIS_NEW_ARCHITECTURE_ENABLED=1"}, {"backtrace": 3, "fragment": "-DRN_FABRIC_ENABLED=1"}, {"backtrace": 3, "fragment": "-DRN_SERIALIZABLE_STATE=1"}, {"fragment": "-std=gnu++20"}, {"fragment": "-Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/335l6r1b/arm64-v8a/src/fabric/CMakeFiles/fabric.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/335l6r1b/arm64-v8a/src/fabric/CMakeFiles/fabric.dir/cmake_pch.hxx"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon"}, {"backtrace": 4, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp/fabric"}, {"backtrace": 4, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include/react/fabric"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "precompileHeaders": [{"backtrace": 6, "header": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/ExpoHeader.pch"}], "sourceIndexes": [1, 2, 3, 4, 5], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "fabric::@3c04bbf757b97f4dae7c", "name": "fabric", "nameOnDisk": "libfabric.a", "paths": {"build": "src/fabric", "source": "src/fabric"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}, {"name": "Precompile Header File", "sourceIndexes": [6]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "path": ".cxx/Debug/335l6r1b/arm64-v8a/src/fabric/CMakeFiles/fabric.dir/cmake_pch.hxx.cxx", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewComponentDescriptor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewEventEmitter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewProps.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewShadowNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/fabric/FabricComponentsRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "path": ".cxx/Debug/335l6r1b/arm64-v8a/src/fabric/CMakeFiles/fabric.dir/cmake_pch.hxx", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}