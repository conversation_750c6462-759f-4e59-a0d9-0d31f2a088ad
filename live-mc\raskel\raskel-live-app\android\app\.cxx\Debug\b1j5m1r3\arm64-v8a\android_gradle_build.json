{"buildFiles": ["C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\.cxx\\Debug\\b1j5m1r3\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\.cxx\\Debug\\b1j5m1r3\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "appmodules", "output": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_AgoraRtcNgSpec::@b3e2fa00649b55f06a11": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_AgoraRtcNgSpec"}, "react_codegen_RNWorkletsSpec::@c787b30dd2b0f948dced": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNWorkletsSpec"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnscreens", "output": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_rnworklets::@68f58d84d4754f193387": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnworklets"}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_safeareacontext", "output": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\intermediates\\cxx\\Debug\\b1j5m1r3\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}