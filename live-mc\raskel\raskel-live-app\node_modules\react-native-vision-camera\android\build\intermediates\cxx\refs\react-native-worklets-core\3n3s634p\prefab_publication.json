{"installationFolder": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-worklets-core", "packageInfo": {"packageName": "react-native-worklets-core", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "rnworklets", "moduleHeaders": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\headers\\rnworklets", "moduleExportLibraries": [], "abis": [{"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\intermediates\\cxx\\Debug\\5t5f5h6s\\obj\\x86_64\\librnworklets.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\.cxx\\Debug\\5t5f5h6s\\x86_64\\android_gradle_build.json"}]}]}}