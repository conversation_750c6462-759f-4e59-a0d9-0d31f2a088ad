ninja: Entering directory `C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\.cxx\Debug\3h292v2g\x86_64'
[1/7] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/VisionCamera.cpp.o
[2/7] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JSharedArray.cpp.o
[3/7] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrameProcessor.cpp.o
[4/7] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/VisionCameraProxy.cpp.o
[5/7] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/JSIJNIConversion.cpp.o
[6/7] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JVisionCameraProxy.cpp.o
[7/7] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\3h292v2g\obj\x86_64\libVisionCamera.so
