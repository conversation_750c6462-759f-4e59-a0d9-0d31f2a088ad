ninja: Entering directory `C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\.cxx\Debug\3h292v2g\x86_64'
[1/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/MutableJByteBuffer.cpp.o
[2/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrame.cpp.o
[3/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrameProcessorPlugin.cpp.o
[4/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/FrameProcessorPluginHostObject.cpp.o
[5/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JVisionCameraScheduler.cpp.o
[6/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/VisionCamera.cpp.o
[7/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/FrameHostObject.cpp.o
[8/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JSharedArray.cpp.o
[9/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/VisionCameraProxy.cpp.o
[10/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JFrameProcessor.cpp.o
[11/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/JSIJNIConversion.cpp.o
[12/13] Building CXX object CMakeFiles/VisionCamera.dir/src/main/cpp/frameprocessors/java-bindings/JVisionCameraProxy.cpp.o
[13/13] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\3h292v2g\obj\x86_64\libVisionCamera.so
