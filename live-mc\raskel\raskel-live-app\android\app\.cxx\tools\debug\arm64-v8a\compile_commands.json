[{"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dappmodules_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\6875011670f4259eebb9dffd6fd545ff\\raskel-live-app\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dappmodules_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\29c6ac0a1dd6ebb9204efeee5b088e58\\rnasyncstorageJSI-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o AgoraRtcNgSpec_autolinked_build\\CMakeFiles\\react_codegen_AgoraRtcNgSpec.dir\\AgoraRtcNgSpec-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\AgoraRtcNgSpec-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\AgoraRtcNgSpec-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o AgoraRtcNgSpec_autolinked_build\\CMakeFiles\\react_codegen_AgoraRtcNgSpec.dir\\70cbe3ded53985a98bc8414c5f1a035d\\AgoraRtcNgSpecJSI-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\AgoraRtcNgSpecJSI-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\AgoraRtcNgSpecJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o AgoraRtcNgSpec_autolinked_build\\CMakeFiles\\react_codegen_AgoraRtcNgSpec.dir\\react\\renderer\\components\\AgoraRtcNgSpec\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o AgoraRtcNgSpec_autolinked_build\\CMakeFiles\\react_codegen_AgoraRtcNgSpec.dir\\react\\renderer\\components\\AgoraRtcNgSpec\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o AgoraRtcNgSpec_autolinked_build\\CMakeFiles\\react_codegen_AgoraRtcNgSpec.dir\\react\\renderer\\components\\AgoraRtcNgSpec\\Props.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\Props.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\Props.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o AgoraRtcNgSpec_autolinked_build\\CMakeFiles\\react_codegen_AgoraRtcNgSpec.dir\\react\\renderer\\components\\AgoraRtcNgSpec\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/react/renderer/components/AgoraRtcNgSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o AgoraRtcNgSpec_autolinked_build\\CMakeFiles\\react_codegen_AgoraRtcNgSpec.dir\\react\\renderer\\components\\AgoraRtcNgSpec\\States.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\States.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\AgoraRtcNgSpec\\States.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\002469d5b84800fdd549c5e175ca642e\\RNCSafeAreaViewShadowNode.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\002469d5b84800fdd549c5e175ca642e\\RNCSafeAreaViewState.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\2e4509cb0d0a1d5cc7411f483425a060\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\51215515fe21556a3837eeba2b49d411\\safeareacontext\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\cd470dadb21de9e1a8a9b540771142e1\\components\\safeareacontext\\Props.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\51215515fe21556a3837eeba2b49d411\\safeareacontext\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\51215515fe21556a3837eeba2b49d411\\safeareacontext\\States.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\2e4509cb0d0a1d5cc7411f483425a060\\safeareacontextJSI-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\34e83742130ce8fa5cb0bd5b71d73d82\\jni\\safeareacontext-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\e7ccc897f1f91c69642c8739e8d03702\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\e7ccc897f1f91c69642c8739e8d03702\\components\\rnscreens\\RNSBottomTabsState.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5695f77ec92f70f29e128dd3dad296b3\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5695f77ec92f70f29e128dd3dad296b3\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\e7ccc897f1f91c69642c8739e8d03702\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\e64928fbfda1b3f277e4ea6d26deec00\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5695f77ec92f70f29e128dd3dad296b3\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\e64928fbfda1b3f277e4ea6d26deec00\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5695f77ec92f70f29e128dd3dad296b3\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\19c14822b9fd6da13ca8af6ae442fcdf\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5695f77ec92f70f29e128dd3dad296b3\\rnscreens\\RNSSplitViewScreenShadowNode.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\79a671fe4e2ad3a12207216f48eb05ff\\components\\rnscreens\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f60e6a02b5031c13be66380d8fa3a5a7\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a328c61c957b5da5800f94032115c8ec\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f60e6a02b5031c13be66380d8fa3a5a7\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a328c61c957b5da5800f94032115c8ec\\react\\renderer\\components\\rnscreens\\States.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\79a671fe4e2ad3a12207216f48eb05ff\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\Props.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\Props.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\Props.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\States.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\States.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\States.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\rnworklets-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\rnworklets-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\rnworklets-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNWorkletsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNWorkletsSpec.dir\\RNWorkletsSpec-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\RNWorkletsSpec-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\RNWorkletsSpec-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNWorkletsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNWorkletsSpec.dir\\react\\renderer\\components\\RNWorkletsSpec\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNWorkletsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNWorkletsSpec.dir\\react\\renderer\\components\\RNWorkletsSpec\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNWorkletsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNWorkletsSpec.dir\\react\\renderer\\components\\RNWorkletsSpec\\Props.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\Props.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\Props.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNWorkletsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNWorkletsSpec.dir\\f62a571eb6863fc2b74961b257b7d8df\\RNWorkletsSpecJSI-generated.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\RNWorkletsSpecJSI-generated.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\RNWorkletsSpecJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNWorkletsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNWorkletsSpec.dir\\react\\renderer\\components\\RNWorkletsSpec\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/android/app/.cxx/Debug/b1j5m1r3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/build/generated/source/codegen/jni/react/renderer/components/RNWorkletsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNWorkletsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNWorkletsSpec.dir\\react\\renderer\\components\\RNWorkletsSpec\\States.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\States.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-worklets-core\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNWorkletsSpec\\States.cpp"}]