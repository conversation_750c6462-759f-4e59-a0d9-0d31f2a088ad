## Little Color Management System (LCMS) v2.16

### LCMS License
<pre>

MIT License

Copyright (C) 1998-2023 <PERSON><PERSON>

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the "Software"),
to deal in the Software without restriction, including without limitation
the rights to use, copy, modify, merge, publish, distribute, sublicense,
and/or sell copies of the Software, and to permit persons to whom the Software
is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------
The below license applies to the following files:
liblcms/cmssm.c

Copyright 2001, softSurfer (www.softsurfer.com)

This code may be freely used and modified for any purpose
providing that this copyright notice is included with it.
SoftSurfer makes no warranty for this code, and cannot be held
liable for any real or imagined damage resulting from its use.
Users of this code must verify correctness for their application.

</pre>

### AUTHORS File Information
```

Main Author
------------
Marti Maria


Contributors
------------
Bob Friesenhahn
Kai-Uwe Behrmann
Stuart Nixon
Jordi Vilar
Richard Hughes
Auke Nauta
Chris Evans (Google)
Lorenzo Ridolfi
Robin Watts (Artifex)
Shawn Pedersen
Andrew Brygin
Samuli Suominen
Florian Hˆch
Aurelien Jarno
Claudiu Cebuc
Michael Vhrel (Artifex)
Michal Cihar
Daniel Kaneider
Mateusz Jurczyk (Google)
Paul Miller
SÈbastien LÈon
Christian Schmitz
XhmikosR
Stanislav Brabec (SuSe)
Leonhard Gruenschloss (Google)
Patrick Noffke
Christopher James Halse Rogers
John Hein
Thomas Weber (Debian)
Mark Allen
Noel Carboni
Sergei Trofimovic
Philipp Knechtges
Amyspark
Lovell Fuller
Eli Schwartz
Diogo Teles Sant'Anna

Special Thanks
--------------
Artifex software
AlienSkin software
libVIPS
Jan Morovic
Jos Vernon (WebSupergoo)
Harald Schneider (Maxon)
Christian Albrecht
Dimitrios Anastassakis
Lemke Software
Tim Zaman

```
