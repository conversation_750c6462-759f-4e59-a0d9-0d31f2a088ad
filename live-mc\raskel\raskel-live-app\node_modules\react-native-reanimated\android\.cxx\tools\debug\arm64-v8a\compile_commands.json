[{"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\common\\transforms\\Quaternion.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\Quaternion.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\Quaternion.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\CSS\\common\\transforms\\TransformMatrix2D.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\TransformMatrix2D.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\TransformMatrix2D.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\CSS\\common\\transforms\\TransformMatrix3D.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\TransformMatrix3D.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\TransformMatrix3D.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\common\\transforms\\TransformOp.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\TransformOp.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\TransformOp.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\vectors.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\vectors.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\transforms\\vectors.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSAngle.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSAngle.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSAngle.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSBoolean.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSBoolean.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSBoolean.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSColor.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSColor.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSColor.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\common\\values\\CSSDiscreteArray.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSDiscreteArray.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSDiscreteArray.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSKeyword.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSKeyword.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSKeyword.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSLength.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSLength.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSLength.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSNumber.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSNumber.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\common\\values\\CSSNumber.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\configs\\CSSAnimationConfig.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\CSSAnimationConfig.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\CSSAnimationConfig.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\configs\\CSSKeyframesConfig.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\CSSKeyframesConfig.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\CSSKeyframesConfig.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\configs\\CSSTransitionConfig.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\CSSTransitionConfig.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\CSSTransitionConfig.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\configs\\common.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\common.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\common.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\configs\\interpolators\\registry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\interpolators\\registry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\configs\\interpolators\\registry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\core\\CSSAnimation.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\core\\CSSAnimation.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\core\\CSSAnimation.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\core\\CSSTransition.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\core\\CSSTransition.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\core\\CSSTransition.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\easing\\EasingFunctions.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\easing\\EasingFunctions.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\easing\\EasingFunctions.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\easing\\cubicBezier.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\easing\\cubicBezier.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\easing\\cubicBezier.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\easing\\linear.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\easing\\linear.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\easing\\linear.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\easing\\steps.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\easing\\steps.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\easing\\steps.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\CSS\\interpolation\\InterpolatorFactory.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\InterpolatorFactory.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\InterpolatorFactory.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\CSS\\interpolation\\PropertyInterpolator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\PropertyInterpolator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\PropertyInterpolator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5e10170989c27bf941bfae90f134ce6a\\interpolation\\groups\\ArrayPropertiesInterpolator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\groups\\ArrayPropertiesInterpolator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\groups\\ArrayPropertiesInterpolator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5e10170989c27bf941bfae90f134ce6a\\interpolation\\groups\\GroupPropertiesInterpolator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\groups\\GroupPropertiesInterpolator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\groups\\GroupPropertiesInterpolator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5e10170989c27bf941bfae90f134ce6a\\interpolation\\groups\\RecordPropertiesInterpolator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\groups\\RecordPropertiesInterpolator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\groups\\RecordPropertiesInterpolator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5e10170989c27bf941bfae90f134ce6a\\interpolation\\styles\\TransitionStyleInterpolator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\styles\\TransitionStyleInterpolator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\styles\\TransitionStyleInterpolator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\1248ca40d672b7c351b3242bc120b137\\CSS\\interpolation\\transforms\\TransformOperation.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\transforms\\TransformOperation.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\transforms\\TransformOperation.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\927cb6a155b88f1ef245c13aab4c73ae\\transforms\\TransformOperationInterpolator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\transforms\\TransformOperationInterpolator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\transforms\\TransformOperationInterpolator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\927cb6a155b88f1ef245c13aab4c73ae\\transforms\\TransformsStyleInterpolator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\transforms\\TransformsStyleInterpolator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\transforms\\TransformsStyleInterpolator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\1248ca40d672b7c351b3242bc120b137\\CSS\\interpolation\\transforms\\operations\\matrix.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\transforms\\operations\\matrix.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\interpolation\\transforms\\operations\\matrix.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\misc\\ViewStylesRepository.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\misc\\ViewStylesRepository.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\misc\\ViewStylesRepository.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\CSS\\progress\\AnimationProgressProvider.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\progress\\AnimationProgressProvider.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\progress\\AnimationProgressProvider.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\progress\\RawProgressProvider.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\progress\\RawProgressProvider.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\progress\\RawProgressProvider.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\CSS\\progress\\TransitionProgressProvider.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\progress\\TransitionProgressProvider.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\progress\\TransitionProgressProvider.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\registries\\CSSAnimationsRegistry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\registries\\CSSAnimationsRegistry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\registries\\CSSAnimationsRegistry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\registries\\CSSKeyframesRegistry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\registries\\CSSKeyframesRegistry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\registries\\CSSKeyframesRegistry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\CSS\\registries\\CSSTransitionsRegistry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\registries\\CSSTransitionsRegistry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\registries\\CSSTransitionsRegistry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\registries\\StaticPropsRegistry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\registries\\StaticPropsRegistry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\registries\\StaticPropsRegistry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\svg\\configs\\init.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\svg\\configs\\init.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\svg\\configs\\init.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\svg\\values\\SVGLength.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\svg\\values\\SVGLength.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\svg\\values\\SVGLength.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\CSS\\svg\\values\\SVGStrokeDashArray.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\svg\\values\\SVGStrokeDashArray.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\svg\\values\\SVGStrokeDashArray.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\utils\\DelayedItemsManager.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\DelayedItemsManager.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\DelayedItemsManager.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\utils\\algorithms.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\algorithms.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\algorithms.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\utils\\interpolators.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\interpolators.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\interpolators.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\utils\\keyframes.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\keyframes.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\keyframes.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\CSS\\utils\\props.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\props.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\CSS\\utils\\props.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\Fabric\\updates\\AnimatedPropsRegistry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\updates\\AnimatedPropsRegistry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\updates\\AnimatedPropsRegistry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\Fabric\\updates\\UpdatesRegistry.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\updates\\UpdatesRegistry.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\updates\\UpdatesRegistry.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\Fabric\\updates\\UpdatesRegistryManager.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\updates\\UpdatesRegistryManager.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\updates\\UpdatesRegistryManager.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\NativeModules\\PropValueProcessor.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\PropValueProcessor.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\PropValueProcessor.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\280c933f74635b381103377f9c8a194d\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b8dbf49644c886a79fa2f778638138b\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\Tools\\FeatureFlags.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeatureFlags.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeatureFlags.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\0b6fd718b95e3ce6b623f4cdea9dd595\\Common\\cpp\\reanimated\\Tools\\ReanimatedVersion.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\ReanimatedVersion.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\ReanimatedVersion.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp"}, {"directory": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS=\"[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]\" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp.o -c C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp", "file": "C:\\Users\\<USER>\\gemini-cli\\live-mc\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp"}]