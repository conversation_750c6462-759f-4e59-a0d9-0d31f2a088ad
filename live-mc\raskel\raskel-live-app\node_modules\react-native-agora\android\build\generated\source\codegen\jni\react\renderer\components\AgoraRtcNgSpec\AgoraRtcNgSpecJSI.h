/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeAgoraRtcNgCxxSpecJSI : public TurboModule {
protected:
  NativeAgoraRtcNgCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual bool newIrisApiEngine(jsi::Runtime &rt) = 0;
  virtual bool destroyIrisApiEngine(jsi::Runtime &rt) = 0;
  virtual jsi::String callApi(jsi::Runtime &rt, jsi::Object args) = 0;
  virtual jsi::Value showRPSystemBroadcastPickerView(jsi::Runtime &rt, bool showsMicrophoneButton) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeAgoraRtcNgCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "AgoraRtcNg";

protected:
  NativeAgoraRtcNgCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAgoraRtcNgCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAgoraRtcNgCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAgoraRtcNgCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    bool newIrisApiEngine(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::newIrisApiEngine) == 1,
          "Expected newIrisApiEngine(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::newIrisApiEngine, jsInvoker_, instance_);
    }
    bool destroyIrisApiEngine(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::destroyIrisApiEngine) == 1,
          "Expected destroyIrisApiEngine(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::destroyIrisApiEngine, jsInvoker_, instance_);
    }
    jsi::String callApi(jsi::Runtime &rt, jsi::Object args) override {
      static_assert(
          bridging::getParameterCount(&T::callApi) == 2,
          "Expected callApi(...) to have 2 parameters");

      return bridging::callFromJs<jsi::String>(
          rt, &T::callApi, jsInvoker_, instance_, std::move(args));
    }
    jsi::Value showRPSystemBroadcastPickerView(jsi::Runtime &rt, bool showsMicrophoneButton) override {
      static_assert(
          bridging::getParameterCount(&T::showRPSystemBroadcastPickerView) == 2,
          "Expected showRPSystemBroadcastPickerView(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::showRPSystemBroadcastPickerView, jsInvoker_, instance_, std::move(showsMicrophoneButton));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeAgoraRtcNgCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
