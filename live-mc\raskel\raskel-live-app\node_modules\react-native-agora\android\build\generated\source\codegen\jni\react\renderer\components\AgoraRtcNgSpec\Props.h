
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <vector>

namespace facebook::react {

struct AgoraRtcSurfaceViewCallApiStruct {
  std::string funcName{};
  std::string params{};
  std::vector<std::string> buffers{};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const AgoraRtcSurfaceViewCallApiStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["funcName"] = funcName;
    result["params"] = params;
    result["buffers"] = ::facebook::react::toDynamic(buffers);
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, AgoraRtcSurfaceViewCallApiStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_funcName = map.find("funcName");
  if (tmp_funcName != map.end()) {
    fromRawValue(context, tmp_funcName->second, result.funcName);
  }
  auto tmp_params = map.find("params");
  if (tmp_params != map.end()) {
    fromRawValue(context, tmp_params->second, result.params);
  }
  auto tmp_buffers = map.find("buffers");
  if (tmp_buffers != map.end()) {
    fromRawValue(context, tmp_buffers->second, result.buffers);
  }
}

static inline std::string toString(const AgoraRtcSurfaceViewCallApiStruct &value) {
  return "[Object AgoraRtcSurfaceViewCallApiStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const AgoraRtcSurfaceViewCallApiStruct &value) {
  return value.toDynamic();
}
#endif
class AgoraRtcSurfaceViewProps final : public ViewProps {
 public:
  AgoraRtcSurfaceViewProps() = default;
  AgoraRtcSurfaceViewProps(const PropsParserContext& context, const AgoraRtcSurfaceViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  AgoraRtcSurfaceViewCallApiStruct callApi{};
  bool zOrderOnTop{false};
  bool zOrderMediaOverlay{false};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

struct AgoraRtcTextureViewCallApiStruct {
  std::string funcName{};
  std::string params{};
  std::vector<std::string> buffers{};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const AgoraRtcTextureViewCallApiStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["funcName"] = funcName;
    result["params"] = params;
    result["buffers"] = ::facebook::react::toDynamic(buffers);
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, AgoraRtcTextureViewCallApiStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_funcName = map.find("funcName");
  if (tmp_funcName != map.end()) {
    fromRawValue(context, tmp_funcName->second, result.funcName);
  }
  auto tmp_params = map.find("params");
  if (tmp_params != map.end()) {
    fromRawValue(context, tmp_params->second, result.params);
  }
  auto tmp_buffers = map.find("buffers");
  if (tmp_buffers != map.end()) {
    fromRawValue(context, tmp_buffers->second, result.buffers);
  }
}

static inline std::string toString(const AgoraRtcTextureViewCallApiStruct &value) {
  return "[Object AgoraRtcTextureViewCallApiStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const AgoraRtcTextureViewCallApiStruct &value) {
  return value.toDynamic();
}
#endif
class AgoraRtcTextureViewProps final : public ViewProps {
 public:
  AgoraRtcTextureViewProps() = default;
  AgoraRtcTextureViewProps(const PropsParserContext& context, const AgoraRtcTextureViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  AgoraRtcTextureViewCallApiStruct callApi{};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

} // namespace facebook::react
