-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:1:1-43:12
MERGED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:1:1-43:12
INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vision-camera] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:react-native-maps] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-purchases] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-purchases\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-worklets] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-worklets-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-41:12
MERGED from [:expo-dev-menu] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.av:16.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e01f8237b96e5856cbafa2aab2342fa4\transformed\expo.modules.av-16.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.font:14.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d0b20dea2146a4f3398b096a2aecfe7d\transformed\expo.modules.font-14.0.8\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:6.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f616f319249bece65e349dd7b5e64d5d\transformed\expo.modules.systemui-6.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:12.0.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\001aa35d3d3ea21b63a5ca189ae185ab\transformed\expo.modules.asset-12.0.9\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d0b7db2af6cf362ced377f660af6a343\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:2:1-20:12
MERGED from [host.exp.exponent:expo.modules.filesystem:19.0.14] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2d138d3c2f844c22648f38321293112\transformed\expo.modules.filesystem-19.0.14\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.imageloader:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f7bb96ccb3e56178fa24338ba1aae2c3\transformed\expo.modules.imageloader-6.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:2:1-59:12
MERGED from [host.exp.exponent:expo.modules.keepawake:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6050a25c543773086ce71891c752ec3b\transformed\expo.modules.keepawake-15.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5deb70712b1e9df4e3892e963cadfc60\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5c9efe2de6410443b525cce16b440b52\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\85e679f8c6e4d1f8af9127be064a6c37\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.apollographql.apollo:apollo-runtime-android-debug:4.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d79f4668696fb3aa3d83a3af931aeb2f\transformed\apollo-runtime-debug\AndroidManifest.xml:2:1-8:12
MERGED from [com.revenuecat.purchases:purchases-hybrid-common:17.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53dfbdda721f846dd27b516ea07ecb96\transformed\purchases-hybrid-common-17.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9e234ec31c85d3e25abc275beb1c1d2b\transformed\camera-video-1.5.0-alpha03\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\132cd50f6bea34a6b5815786fa04ba94\transformed\camera-lifecycle-1.5.0-alpha03\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3e67b0964ce1fc5a9b7c1a5cc158a056\transformed\camera-core-1.5.0-alpha03\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a354e5acf6eadb17d26a8cdfd21c03cf\transformed\camera-view-1.5.0-alpha03\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af1127eeb4bc4ca24acc89ad1699e13a\transformed\coil-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-svg-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ae033cc74a058b83259c13c95d1f3179\transformed\coil-svg-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3f85ea1e6337c5e0ed2dee37ec326f63\transformed\coil-network-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-core-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45ea012571f679db5c2a24848ffb6a48\transformed\coil-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\02d8a2704c6918921ed1201e93ea593d\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:2:1-36:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\915ded6ad2ba3f8c7995501c34ddf542\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\836dcec7963b6cec7ad750d2129187af\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9565240118b09d3a6b315ce3df5b7d2e\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\02bec446bb5b07b8548f83ec26b6ae83\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a1fe1477a0177b3d113834e0b7c3ce7a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d4f691be84b26e413ca5f3209b8a254\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\baf9fd92db1dbf86434347a684118ae9\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2af37017b7d296925114ff313d334e1a\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:2:1-38:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c95b36f1bb2e5ba6998fd08b0a6928d4\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82269fbaa300b3e49759a1b13378dd9e\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\98c01c18832fd1225d6353c1f3d2f2f2\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e90d49d3378aef8a966a58a915da03af\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c1bfffa7072a797e9b07c749b7a5cec4\transformed\play-services-ads-identifier-17.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72fd1eaed4249ec9561f48df05200d45\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f91835d5b2b014d79603c3f3ee79cc74\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d756d0658cd7f25a8fcfd0e49b466b30\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ecf046a9f1c844d96a141a1f6d88232d\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3d447d27eb34477eb4521f9eaad9a4d8\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dc22f65dfa4a5a700cc6f6e7903b2e8c\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1afa0e82c401e8214283b8519a847771\transformed\material-ripple-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.composables:core-android-debug:1.37.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bb478696cb4d967b4b7b530fa737c4ad\transformed\core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b962a9dce59daaf463c2bbae8c1a62b0\transformed\foundation-layout\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feb5cfae13f0c4a8022024f90127daf4\transformed\animation-core\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11c4cc960c460c0732050588d4382732\transformed\animation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\532d9b0ee82f7cd2b4f4b9dd83f7685d\transformed\ui-tooling-preview\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0e114587c82130b34a0db1e23769bcb\transformed\ui-tooling-data\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e694981c1fbe622313f457f86d207bd6\transformed\ui-unit\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6ca79c161877f883d691c1fc6f23e634\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\463153df65adbd597fa88f70be2e6477\transformed\ui-geometry\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f8eb0eb094c0e6c5b8d795f94e7012af\transformed\ui-util\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7103019a489ebfd172161a140c903eb4\transformed\ui-text\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\63ee2875b4892a910b8c00f2fba34dc7\transformed\material-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\29b62c1e8951214205c1999e10893e42\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ab8996d72179ddcbbdde9962833165a\transformed\activity-ktx-1.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d18d5d5c3dd684bfb5d9d3674fe557d6\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c5547f6898b8f18659388e7f05e2c211\transformed\activity-compose-1.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ca620201fa38c0266f7e5566ad638d04\transformed\material-icons-core-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2d58965c6e5688e18e3d3211485660e\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\86afb8f10f81c140c2c827efd493714b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f66e31063bac5482807f5d7516521cc8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\31ea5b6d0667470dca9a286a576aa85c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53e4ee5ecfe8fd4eabb6fc291babface\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\be48fb11a8ff5d51d812b547bbe33b68\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3730efb4e7abb0e262b3cce10c3babd\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a71771dac3d408e1edb1a8cee7c39a2b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b748c3e0fd2d720d73ec351f95e1e7f0\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fafea6e416fa3e4b8701c365ccf63ab3\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ad537bf30d5eae293529d3776b6a0780\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e653453311b978405543fb57283bd596\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b88c83072cd58d0cc6b16793b059d25e\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\faf21e1d7ced897793a7635f982b3609\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\92c2fc9cb001216cdf10bda10a5ca970\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dbcfd095fcd29cdf02774208a30fff53\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\caf90b6e6e50ab63351eca83df5c5cdf\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47094ab56682d3aa166b7eb5ebe0aa39\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67c772feee5668ff747778ea1d50d14c\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d33ca6e8834f99bbe697827cca7514e\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4c6e84b58a3513bef2750adc4f4a0576\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d16fede5da782fdb6cf4acbb268f43c\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\155bc5026fb0ef2abb4038d1dccd0f08\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac91f2df926a17d5567c218333db2809\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10e24e90d892c4f4f2617be1fca3d47b\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a98cd147f21842fd002b2eef6ed701b\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ab9c24df38c6c7d5bdf01c1a2b9efa62\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72f83870993c881c9907c83427c4e865\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3964518108ce6b170aa00a94b83fb53\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\588580d161914cb2310c90f375c39beb\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f198488ab5a5112dc7ed7a95c5218eda\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\669e00e915a3eab7cb40abd1fd29b039\transformed\documentfile-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fb1298e98d569c6243725248e88abb96\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2355de596d8500e1b78a9c7fd2f6dd85\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e7b6c55338c12c9404969801d070be75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\842bb636427967e5c4af231691ed097c\transformed\exoplayer-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00b2f1bbe2895b6cc07fc5d939a86c73\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9ea6c0404ff92893540f528cabdeede4\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1a04bb32de6076408a0dd27bd4c3172e\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\90478c58c3ec0d271c91917aaac4c9a4\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf4e6ff07dbeac5bcd8d8e9bb606754a\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9507c001cdb4b7d4c47b6c48689d3202\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ef9533b8d5a0884be92f5c6e6c0dbd2\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9de930f2063b42f1e1871b63cbc7a1a8\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8ad991e84ac8005e38396591cedfbf8d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\023aba4a518122130be4dca10be105f7\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\de6fe266d05e9e5ac9e926be6e2f70e0\transformed\media-1.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\15ed152dd678ae3fc885b9bafb3d6984\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35b862dd04998bdf6ac2b60e22952262\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\13e56a2ae02ed4f70cf0844a94cbdd84\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e33ac33f6428f1495fb654d3382f42e\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8188219d1d2bc7568a1bcbcc0c0361e\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f66a516956d347b42aecc6efc5010707\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\937dea5d455b8c8024fcae436543927e\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30cb6c01264a0379f15b8e152aac2862\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9f2371705f9dc6c5890cd95f38da38de\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1f949f27dd5a336d4d974516dad185fc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2752ce4275a3cf6149fdad46ba8503b\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c03e1285a57ce224067fcefd337b45f\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4348edc83c5c90b7ebbb510f3729368\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99c50cc3f06d98f86ba0abc90f87fc08\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\43799691d059536940acde5eccb53fc1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e5b3795fc7caaf9facabeaaa86551164\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e9ab3e6c2626eb33e24c85c0cc67dfc\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\20008ac0d85dfe6ca78de6e531f8c41e\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b508b473480c7c6950b857bcadd576d9\transformed\runtime-annotation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e97b1fbf34959c77686ce24f6fad3f64\transformed\runtime\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\434ddfc9465c7b0f7f45ee6d315c5998\transformed\runtime-saveable\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb95318c1eff87dd8a091701a096f6ad\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e422563754deea564571ebeafe3fce2\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30f51c8a36c85c07b7e3abc1db11ccb6\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd6d37c418f70c7ed28293ed75a6ac76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e82331a0e144b8b89cc7da84e5be6620\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b3c5dbd5785893fc9c2ff5e8103b581e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\33a3d926fb159696c4a593553230961e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82c76e521fd169d049dff486bce20abc\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.lukmccall:radix-ui-colors-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a9c093530058f8f4af62ccf82ee7f67\transformed\library-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e6482bec8c59cadb7818dc063106fac8\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5f81816092ba295681c8f49fdd053b97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.facebook.react:hermes-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3cefa1ac864c6b2d83e4f85211b38ee6\transformed\hermes-android-0.81.4-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\117f111b39243b3189468d8b20173550\transformed\viewbinding-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ffbbaa8847c7ae53183b81dfeee67063\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4bd4a42a51b52f8b78a7475d22100557\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\815e3e77a212fac2a57219225e2c1090\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46f1c1c18af1bfb7567fbb3e702c6655\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ffc0aaaa2dfc7c60cb5170492960db0\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6df9a144b46bc8c88df5e86f668fa4e5\transformed\firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\caeffbe2efe2ed5b2ac2beeb6fab50bc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\276a590b0672299d7645bc3b4c0c1871\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ace74405ccbea86803afdd987f4985ca\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ce4355941374c3a47fb1a14de132b2c0\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aad63c18819d46d7c4916466de7cf5ab\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ccd963f9901cf7118cb9db6d20cd734\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ab7f04f8dae60c20fa501fc1ba742bd\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\74d1273d698bfa64e70f3033fb1df3c3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b71d856f4d30c89241e1c8d67444828b\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b468b7189fcdbfda2eeb4f850876d328\transformed\transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd7f81bb243d1284218df098009860f7\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7b233d7c842b8eaddaa18bfcc22da232\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3f625b92d9561c1e638a83e469a388fa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fc304888e2ec161dee6b1e644b213bfa\transformed\full-sdk-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:2:1-21:12
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfa89d7c2b589e03630064b12eb207dc\transformed\iris-rtc-4.5.2-build.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\90aef289aa3b9d5bc378af6dcd7b813d\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fdecd52ba96443316cf7a62d68b90b82\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2edb3e0560fcd9ed0b85d3876a990ca8\transformed\full-rtc-basic-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0deec9455bf20b0529adda60023b5e34\transformed\ains-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34e1e07fe2d26e3bb1cd9a723f962d79\transformed\ains-ll-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10d5afe10da867477c209167f6155891\transformed\audio-beauty-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd626e99e262b25795c754ac8b8c2825\transformed\clear-vision-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3865c2c16ec5a7e07df6a8d3069e7058\transformed\full-content-inspect-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b1b08ff95de0e1cd1dcbbe1cae19c3e3\transformed\screen-capture-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5af46a4618534e09fdfe491394fc563e\transformed\full-virtual-background-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4489de5ddeaa36a54534c73b55773983\transformed\spatial-audio-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30deb1b687fbddf5338393a4bce767aa\transformed\aiaec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c5136930f9417c837c1099137c692f8\transformed\aiaec-ll-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799ee7dcb1efc559d5d56638065b9294\transformed\full-vqa-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8af0240122297b32cfeda1bd179812f3\transformed\full-face-detect-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a93c16f569432f4b7d0fb0bc65691921\transformed\full-face-capture-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\871cf47407cf8dc1249fb74b735ac3ab\transformed\full-voice-drive-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67013ada8d40c23f135a4cae1b3ec60c\transformed\full-video-codec-enc-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4fd0bc4d1a348ef958e55abc3160a9a5\transformed\full-video-codec-dec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3dae6a4cabb556ae0808dcaab442c95\transformed\full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4d9641c210f9be34b7515035d8b90c78\transformed\full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9682e4222b9b8ba4c0b0088a1a935b02\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47a8c24638f7806a7d7949740d88a970\transformed\aosl-1.2.13.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f9bb7228a7fc828ca9611a7f5e7f89c5\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:2:3-78
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:7:5-81
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:3:3-76
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:3:20-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:4:3-76
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-79
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf4e6ff07dbeac5bcd8d8e9bb606754a\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf4e6ff07dbeac5bcd8d8e9bb606754a\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ccd963f9901cf7118cb9db6d20cd734\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ccd963f9901cf7118cb9db6d20cd734\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:4:20-74
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:5:3-65
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-68
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-68
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:5:20-63
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:6:3-71
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:6:20-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:7:3-62
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:8:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:8:5-65
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:7:20-60
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:8:3-74
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:7:5-77
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:7:5-77
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:8:20-72
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:9:3-64
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:7:5-67
MERGED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:9:20-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:10:3-77
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:10:20-75
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:11:3-77
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:12:5-80
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:12:5-80
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:11:20-75
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:12:3-73
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:12:20-71
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:13:3-72
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:13:20-70
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:14:3-68
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-71
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-71
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:14:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:3-75
MERGED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:3-75
MERGED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:3-75
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:16:3-63
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:16:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:17:3-78
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:11:5-81
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:11:5-81
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:17:20-76
queries
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:18:3-24:13
MERGED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:7:5-18:15
MERGED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:7:5-18:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:14:5-25:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:14:5-25:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:22:5-26:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:12:5-19:15
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:12:5-19:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:19:5-23:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
data
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
	android:scheme
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
application
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:3-42:17
MERGED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:3-42:17
MERGED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:3-42:17
INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-39:19
MERGED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-39:19
MERGED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:18:5-22:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:27:5-57:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:27:5-57:19
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:10:5-15:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5deb70712b1e9df4e3892e963cadfc60\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5deb70712b1e9df4e3892e963cadfc60\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3e67b0964ce1fc5a9b7c1a5cc158a056\transformed\camera-core-1.5.0-alpha03\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3e67b0964ce1fc5a9b7c1a5cc158a056\transformed\camera-core-1.5.0-alpha03\AndroidManifest.xml:23:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\915ded6ad2ba3f8c7995501c34ddf542\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\915ded6ad2ba3f8c7995501c34ddf542\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a1fe1477a0177b3d113834e0b7c3ce7a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a1fe1477a0177b3d113834e0b7c3ce7a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:7:5-16:19
MERGED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:7:5-16:19
MERGED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:9:5-14:19
MERGED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:9:5-14:19
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:21:5-36:19
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:21:5-36:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c95b36f1bb2e5ba6998fd08b0a6928d4\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c95b36f1bb2e5ba6998fd08b0a6928d4\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82269fbaa300b3e49759a1b13378dd9e\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82269fbaa300b3e49759a1b13378dd9e\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e90d49d3378aef8a966a58a915da03af\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e90d49d3378aef8a966a58a915da03af\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c1bfffa7072a797e9b07c749b7a5cec4\transformed\play-services-ads-identifier-17.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c1bfffa7072a797e9b07c749b7a5cec4\transformed\play-services-ads-identifier-17.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72fd1eaed4249ec9561f48df05200d45\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72fd1eaed4249ec9561f48df05200d45\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd6d37c418f70c7ed28293ed75a6ac76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd6d37c418f70c7ed28293ed75a6ac76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5f81816092ba295681c8f49fdd053b97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5f81816092ba295681c8f49fdd053b97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:10:5-20:19
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:10:5-20:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fdecd52ba96443316cf7a62d68b90b82\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fdecd52ba96443316cf7a62d68b90b82\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9682e4222b9b8ba4c0b0088a1a935b02\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9682e4222b9b8ba4c0b0088a1a935b02\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml
	tools:ignore
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:116-161
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:116-161
	android:icon
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:81-115
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:81-115
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:221-247
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:221-247
	android:label
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:48-80
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:48-80
	tools:targetApi
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:allowBackup
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:162-188
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:162-188
	android:theme
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:189-220
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:189-220
	android:enableOnBackInvokedCallback
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:248-291
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:248-291
	tools:replace
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:16-47
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:5-83
	android:value
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:60-81
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:5-105
	android:value
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:81-103
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:5-99
	android:value
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:80-97
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:16-79
activity#com.fishkaster.app.MainActivity
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:5-41:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:280-316
	android:launchMode
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:135-166
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:167-209
	android:exported
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:256-279
	android:configChanges
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:44-134
	android:theme
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:210-255
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:30:7-33:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:31:9-60
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:31:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:32:9-68
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:32:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:exp+fish-kaster+data:scheme:fishkaster
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:34:7-40:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
	android:name
		ADDED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
uses-sdk
INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vision-camera] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vision-camera] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-vision-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-purchases] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-purchases\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-purchases] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-purchases\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-worklets] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-worklets] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-worklets-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-worklets-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-worklets-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.av:16.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e01f8237b96e5856cbafa2aab2342fa4\transformed\expo.modules.av-16.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.av:16.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e01f8237b96e5856cbafa2aab2342fa4\transformed\expo.modules.av-16.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:14.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d0b20dea2146a4f3398b096a2aecfe7d\transformed\expo.modules.font-14.0.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:14.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d0b20dea2146a4f3398b096a2aecfe7d\transformed\expo.modules.font-14.0.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:6.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f616f319249bece65e349dd7b5e64d5d\transformed\expo.modules.systemui-6.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:6.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f616f319249bece65e349dd7b5e64d5d\transformed\expo.modules.systemui-6.0.7\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:12.0.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\001aa35d3d3ea21b63a5ca189ae185ab\transformed\expo.modules.asset-12.0.9\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:12.0.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\001aa35d3d3ea21b63a5ca189ae185ab\transformed\expo.modules.asset-12.0.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d0b7db2af6cf362ced377f660af6a343\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d0b7db2af6cf362ced377f660af6a343\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:19.0.14] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2d138d3c2f844c22648f38321293112\transformed\expo.modules.filesystem-19.0.14\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:19.0.14] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2d138d3c2f844c22648f38321293112\transformed\expo.modules.filesystem-19.0.14\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.imageloader:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f7bb96ccb3e56178fa24338ba1aae2c3\transformed\expo.modules.imageloader-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.imageloader:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f7bb96ccb3e56178fa24338ba1aae2c3\transformed\expo.modules.imageloader-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6050a25c543773086ce71891c752ec3b\transformed\expo.modules.keepawake-15.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6050a25c543773086ce71891c752ec3b\transformed\expo.modules.keepawake-15.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5deb70712b1e9df4e3892e963cadfc60\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5deb70712b1e9df4e3892e963cadfc60\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5c9efe2de6410443b525cce16b440b52\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5c9efe2de6410443b525cce16b440b52\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\85e679f8c6e4d1f8af9127be064a6c37\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\85e679f8c6e4d1f8af9127be064a6c37\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.apollographql.apollo:apollo-runtime-android-debug:4.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d79f4668696fb3aa3d83a3af931aeb2f\transformed\apollo-runtime-debug\AndroidManifest.xml:6:5-44
MERGED from [com.apollographql.apollo:apollo-runtime-android-debug:4.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d79f4668696fb3aa3d83a3af931aeb2f\transformed\apollo-runtime-debug\AndroidManifest.xml:6:5-44
MERGED from [com.revenuecat.purchases:purchases-hybrid-common:17.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53dfbdda721f846dd27b516ea07ecb96\transformed\purchases-hybrid-common-17.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases-hybrid-common:17.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53dfbdda721f846dd27b516ea07ecb96\transformed\purchases-hybrid-common-17.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9e234ec31c85d3e25abc275beb1c1d2b\transformed\camera-video-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9e234ec31c85d3e25abc275beb1c1d2b\transformed\camera-video-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\132cd50f6bea34a6b5815786fa04ba94\transformed\camera-lifecycle-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\132cd50f6bea34a6b5815786fa04ba94\transformed\camera-lifecycle-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3e67b0964ce1fc5a9b7c1a5cc158a056\transformed\camera-core-1.5.0-alpha03\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3e67b0964ce1fc5a9b7c1a5cc158a056\transformed\camera-core-1.5.0-alpha03\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a354e5acf6eadb17d26a8cdfd21c03cf\transformed\camera-view-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a354e5acf6eadb17d26a8cdfd21c03cf\transformed\camera-view-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af1127eeb4bc4ca24acc89ad1699e13a\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af1127eeb4bc4ca24acc89ad1699e13a\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-svg-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ae033cc74a058b83259c13c95d1f3179\transformed\coil-svg-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-svg-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ae033cc74a058b83259c13c95d1f3179\transformed\coil-svg-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3f85ea1e6337c5e0ed2dee37ec326f63\transformed\coil-network-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3f85ea1e6337c5e0ed2dee37ec326f63\transformed\coil-network-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45ea012571f679db5c2a24848ffb6a48\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.0.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45ea012571f679db5c2a24848ffb6a48\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\02d8a2704c6918921ed1201e93ea593d\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\02d8a2704c6918921ed1201e93ea593d\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\915ded6ad2ba3f8c7995501c34ddf542\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\915ded6ad2ba3f8c7995501c34ddf542\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\836dcec7963b6cec7ad750d2129187af\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\836dcec7963b6cec7ad750d2129187af\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9565240118b09d3a6b315ce3df5b7d2e\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9565240118b09d3a6b315ce3df5b7d2e\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\02bec446bb5b07b8548f83ec26b6ae83\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\02bec446bb5b07b8548f83ec26b6ae83\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a1fe1477a0177b3d113834e0b7c3ce7a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a1fe1477a0177b3d113834e0b7c3ce7a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d4f691be84b26e413ca5f3209b8a254\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d4f691be84b26e413ca5f3209b8a254\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\baf9fd92db1dbf86434347a684118ae9\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\baf9fd92db1dbf86434347a684118ae9\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2af37017b7d296925114ff313d334e1a\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2af37017b7d296925114ff313d334e1a\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c95b36f1bb2e5ba6998fd08b0a6928d4\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c95b36f1bb2e5ba6998fd08b0a6928d4\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82269fbaa300b3e49759a1b13378dd9e\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82269fbaa300b3e49759a1b13378dd9e\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\98c01c18832fd1225d6353c1f3d2f2f2\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\98c01c18832fd1225d6353c1f3d2f2f2\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e90d49d3378aef8a966a58a915da03af\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e90d49d3378aef8a966a58a915da03af\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c1bfffa7072a797e9b07c749b7a5cec4\transformed\play-services-ads-identifier-17.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c1bfffa7072a797e9b07c749b7a5cec4\transformed\play-services-ads-identifier-17.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72fd1eaed4249ec9561f48df05200d45\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72fd1eaed4249ec9561f48df05200d45\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f91835d5b2b014d79603c3f3ee79cc74\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f91835d5b2b014d79603c3f3ee79cc74\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d756d0658cd7f25a8fcfd0e49b466b30\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d756d0658cd7f25a8fcfd0e49b466b30\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ecf046a9f1c844d96a141a1f6d88232d\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ecf046a9f1c844d96a141a1f6d88232d\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3d447d27eb34477eb4521f9eaad9a4d8\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3d447d27eb34477eb4521f9eaad9a4d8\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dc22f65dfa4a5a700cc6f6e7903b2e8c\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dc22f65dfa4a5a700cc6f6e7903b2e8c\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1afa0e82c401e8214283b8519a847771\transformed\material-ripple-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-ripple:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1afa0e82c401e8214283b8519a847771\transformed\material-ripple-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.composables:core-android-debug:1.37.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bb478696cb4d967b4b7b530fa737c4ad\transformed\core-debug\AndroidManifest.xml:5:5-44
MERGED from [com.composables:core-android-debug:1.37.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bb478696cb4d967b4b7b530fa737c4ad\transformed\core-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b962a9dce59daaf463c2bbae8c1a62b0\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b962a9dce59daaf463c2bbae8c1a62b0\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feb5cfae13f0c4a8022024f90127daf4\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feb5cfae13f0c4a8022024f90127daf4\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11c4cc960c460c0732050588d4382732\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11c4cc960c460c0732050588d4382732\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\532d9b0ee82f7cd2b4f4b9dd83f7685d\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\532d9b0ee82f7cd2b4f4b9dd83f7685d\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0e114587c82130b34a0db1e23769bcb\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0e114587c82130b34a0db1e23769bcb\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e694981c1fbe622313f457f86d207bd6\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e694981c1fbe622313f457f86d207bd6\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6ca79c161877f883d691c1fc6f23e634\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6ca79c161877f883d691c1fc6f23e634\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\463153df65adbd597fa88f70be2e6477\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\463153df65adbd597fa88f70be2e6477\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f8eb0eb094c0e6c5b8d795f94e7012af\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f8eb0eb094c0e6c5b8d795f94e7012af\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7103019a489ebfd172161a140c903eb4\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7103019a489ebfd172161a140c903eb4\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\63ee2875b4892a910b8c00f2fba34dc7\transformed\material-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\63ee2875b4892a910b8c00f2fba34dc7\transformed\material-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\29b62c1e8951214205c1999e10893e42\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\29b62c1e8951214205c1999e10893e42\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ab8996d72179ddcbbdde9962833165a\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ab8996d72179ddcbbdde9962833165a\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d18d5d5c3dd684bfb5d9d3674fe557d6\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d18d5d5c3dd684bfb5d9d3674fe557d6\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c5547f6898b8f18659388e7f05e2c211\transformed\activity-compose-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c5547f6898b8f18659388e7f05e2c211\transformed\activity-compose-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ca620201fa38c0266f7e5566ad638d04\transformed\material-icons-core-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-icons-core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ca620201fa38c0266f7e5566ad638d04\transformed\material-icons-core-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2d58965c6e5688e18e3d3211485660e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2d58965c6e5688e18e3d3211485660e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\86afb8f10f81c140c2c827efd493714b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\86afb8f10f81c140c2c827efd493714b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f66e31063bac5482807f5d7516521cc8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f66e31063bac5482807f5d7516521cc8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\31ea5b6d0667470dca9a286a576aa85c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\31ea5b6d0667470dca9a286a576aa85c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53e4ee5ecfe8fd4eabb6fc291babface\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53e4ee5ecfe8fd4eabb6fc291babface\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\be48fb11a8ff5d51d812b547bbe33b68\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\be48fb11a8ff5d51d812b547bbe33b68\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3730efb4e7abb0e262b3cce10c3babd\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3730efb4e7abb0e262b3cce10c3babd\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a71771dac3d408e1edb1a8cee7c39a2b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a71771dac3d408e1edb1a8cee7c39a2b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b748c3e0fd2d720d73ec351f95e1e7f0\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b748c3e0fd2d720d73ec351f95e1e7f0\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fafea6e416fa3e4b8701c365ccf63ab3\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fafea6e416fa3e4b8701c365ccf63ab3\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ad537bf30d5eae293529d3776b6a0780\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ad537bf30d5eae293529d3776b6a0780\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e653453311b978405543fb57283bd596\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e653453311b978405543fb57283bd596\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b88c83072cd58d0cc6b16793b059d25e\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b88c83072cd58d0cc6b16793b059d25e\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\faf21e1d7ced897793a7635f982b3609\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\faf21e1d7ced897793a7635f982b3609\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\92c2fc9cb001216cdf10bda10a5ca970\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\92c2fc9cb001216cdf10bda10a5ca970\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dbcfd095fcd29cdf02774208a30fff53\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dbcfd095fcd29cdf02774208a30fff53\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\caf90b6e6e50ab63351eca83df5c5cdf\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\caf90b6e6e50ab63351eca83df5c5cdf\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47094ab56682d3aa166b7eb5ebe0aa39\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47094ab56682d3aa166b7eb5ebe0aa39\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67c772feee5668ff747778ea1d50d14c\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67c772feee5668ff747778ea1d50d14c\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d33ca6e8834f99bbe697827cca7514e\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d33ca6e8834f99bbe697827cca7514e\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4c6e84b58a3513bef2750adc4f4a0576\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4c6e84b58a3513bef2750adc4f4a0576\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d16fede5da782fdb6cf4acbb268f43c\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d16fede5da782fdb6cf4acbb268f43c\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\155bc5026fb0ef2abb4038d1dccd0f08\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\155bc5026fb0ef2abb4038d1dccd0f08\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac91f2df926a17d5567c218333db2809\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac91f2df926a17d5567c218333db2809\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10e24e90d892c4f4f2617be1fca3d47b\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10e24e90d892c4f4f2617be1fca3d47b\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a98cd147f21842fd002b2eef6ed701b\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a98cd147f21842fd002b2eef6ed701b\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ab9c24df38c6c7d5bdf01c1a2b9efa62\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ab9c24df38c6c7d5bdf01c1a2b9efa62\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72f83870993c881c9907c83427c4e865\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72f83870993c881c9907c83427c4e865\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3964518108ce6b170aa00a94b83fb53\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3964518108ce6b170aa00a94b83fb53\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\588580d161914cb2310c90f375c39beb\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\588580d161914cb2310c90f375c39beb\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f198488ab5a5112dc7ed7a95c5218eda\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f198488ab5a5112dc7ed7a95c5218eda\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\669e00e915a3eab7cb40abd1fd29b039\transformed\documentfile-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\669e00e915a3eab7cb40abd1fd29b039\transformed\documentfile-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fb1298e98d569c6243725248e88abb96\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fb1298e98d569c6243725248e88abb96\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2355de596d8500e1b78a9c7fd2f6dd85\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2355de596d8500e1b78a9c7fd2f6dd85\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e7b6c55338c12c9404969801d070be75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e7b6c55338c12c9404969801d070be75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\842bb636427967e5c4af231691ed097c\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\842bb636427967e5c4af231691ed097c\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00b2f1bbe2895b6cc07fc5d939a86c73\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00b2f1bbe2895b6cc07fc5d939a86c73\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9ea6c0404ff92893540f528cabdeede4\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9ea6c0404ff92893540f528cabdeede4\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1a04bb32de6076408a0dd27bd4c3172e\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1a04bb32de6076408a0dd27bd4c3172e\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\90478c58c3ec0d271c91917aaac4c9a4\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\90478c58c3ec0d271c91917aaac4c9a4\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf4e6ff07dbeac5bcd8d8e9bb606754a\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf4e6ff07dbeac5bcd8d8e9bb606754a\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9507c001cdb4b7d4c47b6c48689d3202\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9507c001cdb4b7d4c47b6c48689d3202\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ef9533b8d5a0884be92f5c6e6c0dbd2\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ef9533b8d5a0884be92f5c6e6c0dbd2\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9de930f2063b42f1e1871b63cbc7a1a8\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9de930f2063b42f1e1871b63cbc7a1a8\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8ad991e84ac8005e38396591cedfbf8d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8ad991e84ac8005e38396591cedfbf8d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\023aba4a518122130be4dca10be105f7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\023aba4a518122130be4dca10be105f7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\de6fe266d05e9e5ac9e926be6e2f70e0\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\de6fe266d05e9e5ac9e926be6e2f70e0\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\15ed152dd678ae3fc885b9bafb3d6984\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\15ed152dd678ae3fc885b9bafb3d6984\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35b862dd04998bdf6ac2b60e22952262\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35b862dd04998bdf6ac2b60e22952262\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\13e56a2ae02ed4f70cf0844a94cbdd84\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\13e56a2ae02ed4f70cf0844a94cbdd84\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e33ac33f6428f1495fb654d3382f42e\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e33ac33f6428f1495fb654d3382f42e\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8188219d1d2bc7568a1bcbcc0c0361e\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8188219d1d2bc7568a1bcbcc0c0361e\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f66a516956d347b42aecc6efc5010707\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f66a516956d347b42aecc6efc5010707\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\937dea5d455b8c8024fcae436543927e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\937dea5d455b8c8024fcae436543927e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30cb6c01264a0379f15b8e152aac2862\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30cb6c01264a0379f15b8e152aac2862\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9f2371705f9dc6c5890cd95f38da38de\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9f2371705f9dc6c5890cd95f38da38de\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1f949f27dd5a336d4d974516dad185fc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1f949f27dd5a336d4d974516dad185fc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2752ce4275a3cf6149fdad46ba8503b\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2752ce4275a3cf6149fdad46ba8503b\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c03e1285a57ce224067fcefd337b45f\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c03e1285a57ce224067fcefd337b45f\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4348edc83c5c90b7ebbb510f3729368\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4348edc83c5c90b7ebbb510f3729368\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99c50cc3f06d98f86ba0abc90f87fc08\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99c50cc3f06d98f86ba0abc90f87fc08\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\43799691d059536940acde5eccb53fc1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\43799691d059536940acde5eccb53fc1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e5b3795fc7caaf9facabeaaa86551164\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e5b3795fc7caaf9facabeaaa86551164\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e9ab3e6c2626eb33e24c85c0cc67dfc\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e9ab3e6c2626eb33e24c85c0cc67dfc\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\20008ac0d85dfe6ca78de6e531f8c41e\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\20008ac0d85dfe6ca78de6e531f8c41e\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b508b473480c7c6950b857bcadd576d9\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b508b473480c7c6950b857bcadd576d9\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e97b1fbf34959c77686ce24f6fad3f64\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e97b1fbf34959c77686ce24f6fad3f64\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\434ddfc9465c7b0f7f45ee6d315c5998\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\434ddfc9465c7b0f7f45ee6d315c5998\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb95318c1eff87dd8a091701a096f6ad\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb95318c1eff87dd8a091701a096f6ad\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e422563754deea564571ebeafe3fce2\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e422563754deea564571ebeafe3fce2\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30f51c8a36c85c07b7e3abc1db11ccb6\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30f51c8a36c85c07b7e3abc1db11ccb6\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd6d37c418f70c7ed28293ed75a6ac76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd6d37c418f70c7ed28293ed75a6ac76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e82331a0e144b8b89cc7da84e5be6620\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e82331a0e144b8b89cc7da84e5be6620\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b3c5dbd5785893fc9c2ff5e8103b581e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b3c5dbd5785893fc9c2ff5e8103b581e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\33a3d926fb159696c4a593553230961e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\33a3d926fb159696c4a593553230961e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82c76e521fd169d049dff486bce20abc\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82c76e521fd169d049dff486bce20abc\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.lukmccall:radix-ui-colors-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a9c093530058f8f4af62ccf82ee7f67\transformed\library-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.lukmccall:radix-ui-colors-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a9c093530058f8f4af62ccf82ee7f67\transformed\library-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e6482bec8c59cadb7818dc063106fac8\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e6482bec8c59cadb7818dc063106fac8\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5f81816092ba295681c8f49fdd053b97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5f81816092ba295681c8f49fdd053b97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.react:hermes-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3cefa1ac864c6b2d83e4f85211b38ee6\transformed\hermes-android-0.81.4-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3cefa1ac864c6b2d83e4f85211b38ee6\transformed\hermes-android-0.81.4-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\117f111b39243b3189468d8b20173550\transformed\viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\117f111b39243b3189468d8b20173550\transformed\viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ffbbaa8847c7ae53183b81dfeee67063\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ffbbaa8847c7ae53183b81dfeee67063\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4bd4a42a51b52f8b78a7475d22100557\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4bd4a42a51b52f8b78a7475d22100557\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\815e3e77a212fac2a57219225e2c1090\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\815e3e77a212fac2a57219225e2c1090\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46f1c1c18af1bfb7567fbb3e702c6655\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46f1c1c18af1bfb7567fbb3e702c6655\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ffc0aaaa2dfc7c60cb5170492960db0\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ffc0aaaa2dfc7c60cb5170492960db0\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6df9a144b46bc8c88df5e86f668fa4e5\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6df9a144b46bc8c88df5e86f668fa4e5\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\caeffbe2efe2ed5b2ac2beeb6fab50bc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\caeffbe2efe2ed5b2ac2beeb6fab50bc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\276a590b0672299d7645bc3b4c0c1871\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\276a590b0672299d7645bc3b4c0c1871\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ace74405ccbea86803afdd987f4985ca\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ace74405ccbea86803afdd987f4985ca\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ce4355941374c3a47fb1a14de132b2c0\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ce4355941374c3a47fb1a14de132b2c0\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aad63c18819d46d7c4916466de7cf5ab\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aad63c18819d46d7c4916466de7cf5ab\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ccd963f9901cf7118cb9db6d20cd734\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ccd963f9901cf7118cb9db6d20cd734\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ab7f04f8dae60c20fa501fc1ba742bd\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ab7f04f8dae60c20fa501fc1ba742bd\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\74d1273d698bfa64e70f3033fb1df3c3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\74d1273d698bfa64e70f3033fb1df3c3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b71d856f4d30c89241e1c8d67444828b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b71d856f4d30c89241e1c8d67444828b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b468b7189fcdbfda2eeb4f850876d328\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b468b7189fcdbfda2eeb4f850876d328\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd7f81bb243d1284218df098009860f7\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd7f81bb243d1284218df098009860f7\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7b233d7c842b8eaddaa18bfcc22da232\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7b233d7c842b8eaddaa18bfcc22da232\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3f625b92d9561c1e638a83e469a388fa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3f625b92d9561c1e638a83e469a388fa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fc304888e2ec161dee6b1e644b213bfa\transformed\full-sdk-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fc304888e2ec161dee6b1e644b213bfa\transformed\full-sdk-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:5:5-73
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:5:5-73
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfa89d7c2b589e03630064b12eb207dc\transformed\iris-rtc-4.5.2-build.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfa89d7c2b589e03630064b12eb207dc\transformed\iris-rtc-4.5.2-build.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\90aef289aa3b9d5bc378af6dcd7b813d\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\90aef289aa3b9d5bc378af6dcd7b813d\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fdecd52ba96443316cf7a62d68b90b82\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fdecd52ba96443316cf7a62d68b90b82\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2edb3e0560fcd9ed0b85d3876a990ca8\transformed\full-rtc-basic-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2edb3e0560fcd9ed0b85d3876a990ca8\transformed\full-rtc-basic-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0deec9455bf20b0529adda60023b5e34\transformed\ains-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0deec9455bf20b0529adda60023b5e34\transformed\ains-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34e1e07fe2d26e3bb1cd9a723f962d79\transformed\ains-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34e1e07fe2d26e3bb1cd9a723f962d79\transformed\ains-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10d5afe10da867477c209167f6155891\transformed\audio-beauty-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10d5afe10da867477c209167f6155891\transformed\audio-beauty-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd626e99e262b25795c754ac8b8c2825\transformed\clear-vision-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd626e99e262b25795c754ac8b8c2825\transformed\clear-vision-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3865c2c16ec5a7e07df6a8d3069e7058\transformed\full-content-inspect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3865c2c16ec5a7e07df6a8d3069e7058\transformed\full-content-inspect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b1b08ff95de0e1cd1dcbbe1cae19c3e3\transformed\screen-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b1b08ff95de0e1cd1dcbbe1cae19c3e3\transformed\screen-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5af46a4618534e09fdfe491394fc563e\transformed\full-virtual-background-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5af46a4618534e09fdfe491394fc563e\transformed\full-virtual-background-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4489de5ddeaa36a54534c73b55773983\transformed\spatial-audio-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4489de5ddeaa36a54534c73b55773983\transformed\spatial-audio-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30deb1b687fbddf5338393a4bce767aa\transformed\aiaec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30deb1b687fbddf5338393a4bce767aa\transformed\aiaec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c5136930f9417c837c1099137c692f8\transformed\aiaec-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c5136930f9417c837c1099137c692f8\transformed\aiaec-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799ee7dcb1efc559d5d56638065b9294\transformed\full-vqa-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799ee7dcb1efc559d5d56638065b9294\transformed\full-vqa-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8af0240122297b32cfeda1bd179812f3\transformed\full-face-detect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8af0240122297b32cfeda1bd179812f3\transformed\full-face-detect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a93c16f569432f4b7d0fb0bc65691921\transformed\full-face-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a93c16f569432f4b7d0fb0bc65691921\transformed\full-face-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\871cf47407cf8dc1249fb74b735ac3ab\transformed\full-voice-drive-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\871cf47407cf8dc1249fb74b735ac3ab\transformed\full-voice-drive-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67013ada8d40c23f135a4cae1b3ec60c\transformed\full-video-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\67013ada8d40c23f135a4cae1b3ec60c\transformed\full-video-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4fd0bc4d1a348ef958e55abc3160a9a5\transformed\full-video-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4fd0bc4d1a348ef958e55abc3160a9a5\transformed\full-video-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3dae6a4cabb556ae0808dcaab442c95\transformed\full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a3dae6a4cabb556ae0808dcaab442c95\transformed\full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4d9641c210f9be34b7515035d8b90c78\transformed\full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4d9641c210f9be34b7515035d8b90c78\transformed\full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9682e4222b9b8ba4c0b0088a1a935b02\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9682e4222b9b8ba4c0b0088a1a935b02\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47a8c24638f7806a7d7949740d88a970\transformed\aosl-1.2.13.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\47a8c24638f7806a7d7949740d88a970\transformed\aosl-1.2.13.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f9bb7228a7fc828ca9611a7f5e7f89c5\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f9bb7228a7fc828ca9611a7f5e7f89c5\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-76
	android:name
		ADDED from [:react-native-agora] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-73
package#host.exp.exponent
ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:75
	android:launchMode
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-72
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
activity#expo.modules.devlauncher.compose.AuthActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-30:20
	android:launchMode
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-44
	android:exported
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-36
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-72
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-29:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:9-34:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-49
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-67
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-93
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:9-38:42
	android:value
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-39
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-64
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fdecd52ba96443316cf7a62d68b90b82\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fdecd52ba96443316cf7a62d68b90b82\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fdecd52ba96443316cf7a62d68b90b82\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\673a27f53479c4bbce3e2bb096fa9438\transformed\react-android-0.81.4-debug\AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:10:9-17:18
action#android.intent.action.OPEN_DOCUMENT
ADDED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:11:13-74
	android:name
		ADDED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:11:21-71
category#android.intent.category.OPENABLE
ADDED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:13-73
	android:name
		ADDED from [host.exp.exponent:expo.modules.documentpicker:14.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e1e1a3fd0c186a44d22d465ace6b1af7\transformed\expo.modules.documentpicker-14.0.7\AndroidManifest.xml:14:23-70
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:38:17-63
activity#expo.modules.imagepicker.ExpoCropImageActivity
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:42:9-46:48
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:44:13-37
	android:theme
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:45:13-56
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:46:13-45
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:43:13-74
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:48:9-56:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:52:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:50:13-75
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:51:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:49:13-89
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:53:13-55:71
	android:resource
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:55:17-68
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:17.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c03aa5819983262a432563527fb467eb\transformed\expo.modules.imagepicker-17.0.8\AndroidManifest.xml:54:17-67
service#expo.modules.location.services.LocationTaskService
ADDED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:14:13-53
	android:name
		ADDED from [host.exp.exponent:expo.modules.location:19.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ed5bf0aa9bfdd672bc06165af2dddfae\transformed\expo.modules.location-19.0.7\AndroidManifest.xml:12:13-78
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72fd1eaed4249ec9561f48df05200d45\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\72fd1eaed4249ec9561f48df05200d45\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\45026366ce98ec29499b19eaa1aea2c4\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\09642d7692a9611830870020253c1f20\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6c9ae2184334313a23ecbbcb02dc4f2\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3e67b0964ce1fc5a9b7c1a5cc158a056\transformed\camera-core-1.5.0-alpha03\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3e67b0964ce1fc5a9b7c1a5cc158a056\transformed\camera-core-1.5.0-alpha03\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\da26c74961a03cdda1c9c87f9d19a8bf\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:31:17-103
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
action#android.intent.action.GET_CONTENT
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
	android:grantUriPermissions
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\025e05451a60883d4b88ffb9b910f451\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:32:13-64
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2680d0b7ab69487753343e33f1cb0e1f\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
activity#com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity
ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
	android:screenOrientation
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
	android:exported
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:19:13-42
	android:name
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b8e15fbda824d64d740b264ba5e91aca\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d67ac7019c9f7804e4e5fa0a69df7aba\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b3f0926cb9d4a172cd6a497c9541f4b\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
receiver#com.amazon.device.iap.ResponseReceiver
ADDED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:8:9-15:20
	android:exported
		ADDED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:10:13-36
	android:permission
		ADDED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:11:13-79
	android:name
		ADDED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:9:13-66
intent-filter#action:name:com.amazon.inapp.purchasing.NOTIFY
ADDED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:12:13-14:29
action#com.amazon.inapp.purchasing.NOTIFY
ADDED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:13:17-77
	android:name
		ADDED from [com.revenuecat.purchases:purchases-store-amazon:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7a515a2d2a7907e12d2436f62f3471e\transformed\purchases-store-amazon-9.7.0\AndroidManifest.xml:13:25-74
activity#com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity
ADDED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:10:9-13:75
	android:configChanges
		ADDED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:12:13-96
	android:theme
		ADDED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:13:13-72
	android:name
		ADDED from [com.revenuecat.purchases:purchases:9.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\00d5201aebb260b525dd0312ac430ea0\transformed\purchases-9.7.0\AndroidManifest.xml:11:13-97
uses-permission#com.android.vending.BILLING
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:10:5-67
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:10:22-64
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:14:21-88
intent#action:name:com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:16:9-18:18
action#com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:17:13-116
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:17:21-113
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:22:9-24:37
	android:value
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:24:13-34
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:23:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:26:9-30:75
	android:exported
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:29:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:28:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:30:13-72
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:27:13-78
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:31:9-35:75
	android:exported
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:33:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:35:13-72
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8159aed505457724fb97486699662871\transformed\billing-8.0.0\AndroidManifest.xml:32:13-80
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ff7e39d1dce962b72e0e33d3ff513a9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2300fc24addf67d0dfae16815bffd1a3\transformed\ui-tooling\AndroidManifest.xml:24:13-71
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd6d37c418f70c7ed28293ed75a6ac76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd6d37c418f70c7ed28293ed75a6ac76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4f679923d1207f741996e17a3163dd\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1fee919077049c01ef2050cbec002027\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.fishkaster.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.fishkaster.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8aa23bce709bfe96db10568360097556\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\819d71d693a61662686bb9a4efb476a3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\89ccea9f83e034699c7b8a048d83b9f5\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\73e0c3dffc8c7108e2959f4441779720\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\61a1fa180d1688df69114646098b3a92\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\16c02f6427fae02ce19cfc6b700a2f1c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:8:5-94
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:8:22-91
activity#io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenCaptureAssistantActivity
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:11:9-14:63
	android:screenOrientation
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:13:13-52
	android:configChanges
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:12:13-59
	android:theme
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:14:13-61
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:11:19-89
service#io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenSharingService
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:16:9-19:19
	android:foregroundServiceType
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:18:13-60
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b640b9d90311e6c3835c25272413a94\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:17:13-73
