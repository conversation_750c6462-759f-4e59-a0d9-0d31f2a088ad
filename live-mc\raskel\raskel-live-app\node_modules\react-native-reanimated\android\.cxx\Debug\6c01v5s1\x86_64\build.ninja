# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Reanimated
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/x86_64/
# =============================================================================
# Object build statements for SHARED_LIBRARY target reanimated


#############################################
# Order-only phony target for reanimated

build cmake_object_order_depends_target_reanimated: phony || CMakeFiles/reanimated.dir

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\AnimatedSensor\AnimatedSensorModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\AnimatedSensor
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/transforms/Quaternion.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/transforms/Quaternion.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\transforms\Quaternion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformMatrix2D.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/transforms/TransformMatrix2D.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\common\transforms\TransformMatrix2D.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\common\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformMatrix3D.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/transforms/TransformMatrix3D.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\common\transforms\TransformMatrix3D.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\common\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformOp.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/transforms/TransformOp.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\common\transforms\TransformOp.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\common\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/transforms/vectors.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/transforms/vectors.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\transforms\vectors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values\CSSAngle.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSBoolean.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSBoolean.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values\CSSBoolean.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSColor.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSColor.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values\CSSColor.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/values/CSSDiscreteArray.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSDiscreteArray.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\common\values\CSSDiscreteArray.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSKeyword.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSKeyword.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values\CSSKeyword.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSLength.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSLength.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values\CSSLength.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSNumber.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSNumber.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values\CSSNumber.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSAnimationConfig.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/configs/CSSAnimationConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\configs\CSSAnimationConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\configs
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSKeyframesConfig.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/configs/CSSKeyframesConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\configs\CSSKeyframesConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\configs
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSTransitionConfig.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/configs/CSSTransitionConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\configs\CSSTransitionConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\configs
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/common.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/configs/common.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\configs\common.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\configs
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/configs/interpolators/registry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/configs/interpolators/registry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\configs\interpolators\registry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\configs\interpolators
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/core/CSSAnimation.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/core/CSSAnimation.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\core\CSSAnimation.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\core
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/core/CSSTransition.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/core/CSSTransition.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\core\CSSTransition.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\core
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\easing\EasingFunctions.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\easing
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/cubicBezier.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/cubicBezier.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\easing\cubicBezier.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\easing
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/linear.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/linear.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\easing\linear.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\easing
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/steps.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/steps.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\easing\steps.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\easing
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/interpolation/InterpolatorFactory.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/InterpolatorFactory.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\interpolation\InterpolatorFactory.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\interpolation
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/interpolation/PropertyInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/PropertyInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\interpolation\PropertyInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\interpolation
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/ArrayPropertiesInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/ArrayPropertiesInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\groups\ArrayPropertiesInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\groups
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/GroupPropertiesInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/GroupPropertiesInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\groups\GroupPropertiesInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\groups
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/RecordPropertiesInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/RecordPropertiesInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\groups\RecordPropertiesInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\groups
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/styles/TransitionStyleInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/styles/TransitionStyleInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\styles\TransitionStyleInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\styles
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/transforms/TransformOperation.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformOperation.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\transforms\TransformOperation.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/927cb6a155b88f1ef245c13aab4c73ae/transforms/TransformOperationInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformOperationInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\927cb6a155b88f1ef245c13aab4c73ae\transforms\TransformOperationInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\927cb6a155b88f1ef245c13aab4c73ae\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/5e10170989c27bf941bfae90f134ce6a/interpolation/transforms/TransformsStyleInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformsStyleInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\5e10170989c27bf941bfae90f134ce6a\interpolation\transforms\TransformsStyleInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\5e10170989c27bf941bfae90f134ce6a\interpolation\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/transforms/operations/matrix.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/operations/matrix.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\transforms\operations\matrix.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\1248ca40d672b7c351b3242bc120b137\CSS\interpolation\transforms\operations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\misc\ViewStylesRepository.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\misc
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/progress/AnimationProgressProvider.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/AnimationProgressProvider.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\progress\AnimationProgressProvider.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\progress
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/progress/RawProgressProvider.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/RawProgressProvider.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\progress\RawProgressProvider.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\progress
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/progress/TransitionProgressProvider.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/TransitionProgressProvider.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\progress\TransitionProgressProvider.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\progress
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSAnimationsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registries/CSSAnimationsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\registries\CSSAnimationsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\registries
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSKeyframesRegistry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registries/CSSKeyframesRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\registries\CSSKeyframesRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\registries
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSTransitionsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registries/CSSTransitionsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\registries\CSSTransitionsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\registries
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/StaticPropsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registries/StaticPropsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\registries\StaticPropsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\registries
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/svg/configs/init.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/svg/configs/init.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\svg\configs\init.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\svg\configs
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/svg/values/SVGLength.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/svg/values/SVGLength.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\svg\values\SVGLength.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\svg\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/svg/values/SVGStrokeDashArray.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/svg/values/SVGStrokeDashArray.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\svg\values\SVGStrokeDashArray.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\CSS\svg\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/DelayedItemsManager.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/utils/DelayedItemsManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils\DelayedItemsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/algorithms.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/utils/algorithms.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils\algorithms.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/interpolators.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/utils/interpolators.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils\interpolators.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/keyframes.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/utils/keyframes.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils\keyframes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/props.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/utils/props.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils\props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\CSS\utils
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Fabric\ReanimatedCommitHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Fabric\ReanimatedMountHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Fabric\ShadowTreeCloner.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\Fabric\updates\AnimatedPropsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\Fabric\updates
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Fabric\updates\UpdatesRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Fabric\updates
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/Fabric/updates/UpdatesRegistryManager.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/UpdatesRegistryManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\Fabric\updates\UpdatesRegistryManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\Fabric\updates
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/280c933f74635b381103377f9c8a194d/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\280c933f74635b381103377f9c8a194d\reanimated\LayoutAnimations\LayoutAnimationsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\280c933f74635b381103377f9c8a194d\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\LayoutAnimations\LayoutAnimationsProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\LayoutAnimations\LayoutAnimationsUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/NativeModules/PropValueProcessor.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/PropValueProcessor.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\NativeModules\PropValueProcessor.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\NativeModules
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\NativeModules\ReanimatedModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\NativeModules
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\NativeModules\ReanimatedModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\NativeModules
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\RuntimeDecorators\RNRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\RuntimeDecorators
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\RuntimeDecorators\UIRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b8dbf49644c886a79fa2f778638138b\cpp\reanimated\RuntimeDecorators
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Tools/FeatureFlags.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeatureFlags.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Tools\FeatureFlags.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Tools/ReanimatedVersion.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/ReanimatedVersion.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Tools\ReanimatedVersion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\0b6fd718b95e3ce6b623f4cdea9dd595\Common\cpp\reanimated\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/NativeProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\reanimated\android\NativeProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp\reanimated\android
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/OnLoad.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\reanimated\android\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -Wall -Werror -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/yoga -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/jsiexecutor -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/Common/cpp -IC:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/src/main/cpp -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp\reanimated\android
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target reanimated


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.so

build ../../../../build/intermediates/cxx/Debug/6c01v5s1/obj/x86_64/libreanimated.so: CXX_SHARED_LIBRARY_LINKER__reanimated_Debug CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/transforms/Quaternion.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformMatrix2D.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformMatrix3D.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/transforms/TransformOp.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/transforms/vectors.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSBoolean.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSColor.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/common/values/CSSDiscreteArray.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSKeyword.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSLength.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/common/values/CSSNumber.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSAnimationConfig.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSKeyframesConfig.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/CSSTransitionConfig.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/configs/common.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/configs/interpolators/registry.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/core/CSSAnimation.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/core/CSSTransition.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/cubicBezier.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/linear.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/easing/steps.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/interpolation/InterpolatorFactory.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/interpolation/PropertyInterpolator.cpp.o CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/ArrayPropertiesInterpolator.cpp.o CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/GroupPropertiesInterpolator.cpp.o CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/groups/RecordPropertiesInterpolator.cpp.o CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/styles/TransitionStyleInterpolator.cpp.o CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/transforms/TransformOperation.cpp.o CMakeFiles/reanimated.dir/927cb6a155b88f1ef245c13aab4c73ae/transforms/TransformOperationInterpolator.cpp.o CMakeFiles/reanimated.dir/5e10170989c27bf941bfae90f134ce6a/interpolation/transforms/TransformsStyleInterpolator.cpp.o CMakeFiles/reanimated.dir/1248ca40d672b7c351b3242bc120b137/CSS/interpolation/transforms/operations/matrix.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/progress/AnimationProgressProvider.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/progress/RawProgressProvider.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/progress/TransitionProgressProvider.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSAnimationsRegistry.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSKeyframesRegistry.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/CSSTransitionsRegistry.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/registries/StaticPropsRegistry.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/svg/configs/init.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/svg/values/SVGLength.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/CSS/svg/values/SVGStrokeDashArray.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/DelayedItemsManager.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/algorithms.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/interpolators.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/keyframes.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/CSS/utils/props.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/Fabric/updates/UpdatesRegistryManager.cpp.o CMakeFiles/reanimated.dir/280c933f74635b381103377f9c8a194d/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/NativeModules/PropValueProcessor.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o CMakeFiles/reanimated.dir/0b8dbf49644c886a79fa2f778638138b/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Tools/FeatureFlags.cpp.o CMakeFiles/reanimated.dir/0b6fd718b95e3ce6b623f4cdea9dd595/Common/cpp/reanimated/Tools/ReanimatedVersion.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/NativeProxy.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/OnLoad.cpp.o | C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/intermediates/cmake/debug/obj/x86_64/libworklets.so C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=81    -DREANIMATED_VERSION=4.1.0    -DREANIMATED_FEATURE_FLAGS="[EXPERIMENTAL_CSS_ANIMATIONS_FOR_SVG_COMPONENTS:false][RUNTIME_TEST_FLAG:false][DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][USE_SYNCHRONIZABLE_FOR_MUTABLES:false]" -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -llog  C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so  C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so  -landroid  C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/intermediates/cmake/debug/obj/x86_64/libworklets.so  C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\reanimated.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreanimated.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6c01v5s1\obj\x86_64\libreanimated.pdb
  RSP_FILE = CMakeFiles\reanimated.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\.cxx\Debug\6c01v5s1\x86_64 && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\.cxx\Debug\6c01v5s1\x86_64 && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android -BC:\Users\<USER>\gemini-cli\live-mc\raskel\raskel-live-app\node_modules\react-native-reanimated\android\.cxx\Debug\6c01v5s1\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build libreanimated.so: phony ../../../../build/intermediates/cxx/Debug/6c01v5s1/obj/x86_64/libreanimated.so

build reanimated: phony ../../../../build/intermediates/cxx/Debug/6c01v5s1/obj/x86_64/libreanimated.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/x86_64

build all: phony ../../../../build/intermediates/cxx/Debug/6c01v5s1/obj/x86_64/libreanimated.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/x86_64/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/x86_64/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/x86_64/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/x86_64/CMakeFiles/cmake.verify_globs | ../../../../CMakeLists.txt ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/x86_64/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6c01v5s1/x86_64/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake C$:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
