# ninja log v5
35	7700	7802455854138050	CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/wrappers/WKTJsiWrapper.cpp.o	90440319e335e090
1	15	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/.cxx/Debug/5t5f5h6s/x86_64/CMakeFiles/cmake.verify_globs	6bb47d7a09d6e979
25	5090	7802455828460394	CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/dispatch/WKTDispatchQueue.cpp.o	91c8fbb2a10d9131
8	11920	7802455896417914	CMakeFiles/rnworklets.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/react-native-worklets-core/cpp/WKTJsiWorkletContext.cpp.o	f4dac694e278f97c
41	7323	7802455850344266	CMakeFiles/rnworklets.dir/cpp-adapter.cpp.o	5a106f9cfa7bb81b
29	7299	7802455850169058	CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/wrappers/WKTJsiPromiseWrapper.cpp.o	76b66cbc79f98a17
2	5139	7802455828755604	CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/base/WKTRuntimeLifecycleMonitor.cpp.o	50b7e09eb6bb45e1
13	5575	7802455833302523	CMakeFiles/rnworklets.dir/25e89bb0dce145e6e891a3b405c1c2a2/cpp/base/WKTJsiHostObject.cpp.o	264bc1bd52642219
19	7209	7802455848619283	CMakeFiles/rnworklets.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/react-native-worklets-core/cpp/WKTJsiWorkletApi.cpp.o	3621d601af0cffe3
11920	13246	7802455906274923	../../../../build/intermediates/cxx/Debug/5t5f5h6s/obj/x86_64/librnworklets.so	a59b627caefba419
1	15	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets-core/android/.cxx/Debug/5t5f5h6s/x86_64/CMakeFiles/cmake.verify_globs	6bb47d7a09d6e979
