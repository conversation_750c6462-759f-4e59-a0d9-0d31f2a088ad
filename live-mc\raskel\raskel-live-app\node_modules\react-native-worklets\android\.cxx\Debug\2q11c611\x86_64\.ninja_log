# ninja log v5
17	70	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/x86_64/CMakeFiles/cmake.verify_globs	8a1b7e9cef24997
9	440	7802455779210851	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Resources/ValueUnpacker.cpp.o	e9802ef874816028
16	443	7802455778975623	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Resources/SynchronizableUnpacker.cpp.o	26ad0f03ca90113b
134	4138	7802455819016760	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/RuntimeData.cpp.o	cba71876addfb1e4
51	4286	7802455819947912	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/SharedItems/SynchronizableAccess.cpp.o	707c8a271085fdae
277	4454	7802455821360103	CMakeFiles/worklets.dir/src/main/cpp/worklets/android/PlatformLogger.cpp.o	b66ca2359df2b663
66	4564	7802455823157527	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/FeatureFlags.cpp.o	a5010c98e24d661d
23	5235	7802455829637100	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o	7bb11c502f235e48
127	5400	7802455831048939	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o	4e602957694b1258
73	5528	7802455832701568	CMakeFiles/worklets.dir/2bd9f9a61f19ceb025a4c4f50025c5a0/react-native-worklets/Common/cpp/worklets/Tools/JSLogger.cpp.o	aa71c1b0e4d5a415
2	5804	7802455832992125	CMakeFiles/worklets.dir/e751c91732a5ab9c8eacba856b5cb7bb/cpp/worklets/AnimationFrameQueue/AnimationFrameBatchinator.cpp.o	56b4a9f7d4ead346
120	5850	7802455834118788	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/UIScheduler.cpp.o	b0cd7f9a6023a47e
161	5885	7802455833848306	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/UIRuntimeDecorator.cpp.o	3d3ef5cf6b805833
44	5905	7802455834459134	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/SharedItems/Synchronizable.cpp.o	dc8ce11dc8eeb066
87	5929	7802455835099807	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o	da736568be865a68
113	5935	7802455835615698	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/JSScheduler.cpp.o	2b788d09213641b4
80	5939	7802455835675798	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/JSISerializer.cpp.o	54fd845e79643da9
30	5943	7802455836677224	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/RunLoop/AsyncQueueImpl.cpp.o	320a718f0de00035
37	6362	7802455840288449	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/RunLoop/EventLoop.cpp.o	9ade98acc1b35974
214	6364	7802455840473646	CMakeFiles/worklets.dir/src/main/cpp/worklets/android/AndroidUIScheduler.cpp.o	97697c20d2f7898c
203	6775	7802455845325237	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/WorkletHermesRuntime.cpp.o	db3f05d446ca77e0
59	6805	7802455845415228	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/SharedItems/Serializable.cpp.o	76e169f783b8b3b2
105	8089	7802455858006632	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/VersionUtils.cpp.o	32d61d241c0ff4fe
94	8865	7802455865082412	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o	f3a5ea104a25e98
251	9149	7802455868997810	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o	b5dfdbcd6f23019f
141	10444	7802455882106157	CMakeFiles/worklets.dir/e751c91732a5ab9c8eacba856b5cb7bb/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o	bdf830b04dac6154
191	10482	7802455882496584	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/RuntimeManager.cpp.o	a90dc0f629e178a7
261	10526	7802455882896590	CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsOnLoad.cpp.o	53a0559f38302fe3
224	10589	7802455883342149	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o	8c3be9159b689476
320	11377	7802455890755385	CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsModule.cpp.o	332b72e3cc8944f4
179	11810	7802455895211617	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o	e5e39560b08b56f6
148	13614	7802455912575062	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o	85cf27c2e589d9f6
235	14834	7802455925844360	CMakeFiles/worklets.dir/af42619d32e2b556c34d3987e9e9347a/Common/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp.o	2dd666a5743f473
14835	14971	7802455927310967	../../../../build/intermediates/cxx/Debug/2q11c611/obj/x86_64/libworklets.so	e3c38d1ef9c3e51d
1	21	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/x86_64/CMakeFiles/cmake.verify_globs	8a1b7e9cef24997
2	26	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/x86_64/CMakeFiles/cmake.verify_globs	8a1b7e9cef24997
2	22	0	C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native-worklets/android/.cxx/Debug/2q11c611/x86_64/CMakeFiles/cmake.verify_globs	8a1b7e9cef24997
