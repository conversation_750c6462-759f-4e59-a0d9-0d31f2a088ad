{"logs": [{"outputFile": "com.fishkaster.app-mergeDebugResources-106:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\72fd1eaed4249ec9561f48df05200d45\\transformed\\play-services-basement-18.4.0\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "136", "endOffsets": "331"}, "to": {"startLines": "79", "startColumns": "4", "startOffsets": "6623", "endColumns": "140", "endOffsets": "6759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\30f51c8a36c85c07b7e3abc1db11ccb6\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,297,385,479,578,664,741,833,925,1010,1091,1177,1250,1341,1418,1497,1574,1654,1724", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,90,76,78,76,79,69,117", "endOffsets": "292,380,474,573,659,736,828,920,1005,1086,1172,1245,1336,1413,1492,1569,1649,1719,1837"}, "to": {"startLines": "69,70,90,91,92,151,152,212,213,215,216,220,222,223,224,225,227,228,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5442,5543,7928,8022,8121,12710,12787,17595,17687,17852,17933,18268,18418,18509,18586,18665,18843,18923,18993", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,90,76,78,76,79,69,117", "endOffsets": "5538,5626,8017,8116,8202,12782,12874,17682,17767,17928,18014,18336,18504,18581,18660,18737,18918,18988,19106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8aa23bce709bfe96db10568360097556\\transformed\\core-1.15.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "59,60,61,62,63,64,65,226", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4414,4512,4614,4712,4816,4920,5022,18742", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "4507,4609,4707,4811,4915,5017,5134,18838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f198488ab5a5112dc7ed7a95c5218eda\\transformed\\browser-1.6.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,99", "endOffsets": "154,254,368,468"}, "to": {"startLines": "89,147,148,149", "startColumns": "4,4,4,4", "startOffsets": "7824,12319,12419,12533", "endColumns": "103,99,113,99", "endOffsets": "7923,12414,12528,12628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\29b62c1e8951214205c1999e10893e42\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,91", "endOffsets": "140,230,322"}, "to": {"startLines": "53,230,231", "startColumns": "4,4,4", "startOffsets": "3887,19111,19201", "endColumns": "89,89,91", "endOffsets": "3972,19196,19288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\1ff7e39d1dce962b72e0e33d3ff513a9\\transformed\\play-services-base-18.5.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,573,679,825,947,1055,1153,1303,1406,1563,1686,1832,1974,2038,2096", "endColumns": "101,155,121,105,145,121,107,97,149,102,156,122,145,141,63,57,80", "endOffsets": "294,450,572,678,824,946,1054,1152,1302,1405,1562,1685,1831,1973,2037,2095,2176"}, "to": {"startLines": "71,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5631,5737,5897,6023,6133,6283,6409,6521,6764,6918,7025,7186,7313,7463,7609,7677,7739", "endColumns": "105,159,125,109,149,125,111,101,153,106,160,126,149,145,67,61,84", "endOffsets": "5732,5892,6018,6128,6278,6404,6516,6618,6913,7020,7181,7308,7458,7604,7672,7734,7819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\02bec446bb5b07b8548f83ec26b6ae83\\transformed\\appcompat-1.7.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1157,1278,1375,1482,1568,1672,1794,1879,1961,2052,2145,2240,2334,2434,2527,2622,2717,2808,2899,2987,3090,3194,3300,3405,3519,3622,3791,18019", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "1273,1370,1477,1563,1667,1789,1874,1956,2047,2140,2235,2329,2429,2522,2617,2712,2803,2894,2982,3085,3189,3295,3400,3514,3617,3786,3882,18101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3ef9533b8d5a0884be92f5c6e6c0dbd2\\transformed\\exoplayer-ui-2.18.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,835,921,1008,1096,1194,1301,1371,1438,1534,1626,1691,1764,1827,1895,2009,2125,2241,2321,2405,2476,2547,2648,2750,2822,2892,2945,3003,3051,3112,3184,3251,3315,3386,3450,3509,3574,3639,3710,3762,3829,3910,3991", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "288,566,830,916,1003,1091,1189,1296,1366,1433,1529,1621,1686,1759,1822,1890,2004,2120,2236,2316,2400,2471,2542,2643,2745,2817,2887,2940,2998,3046,3107,3179,3246,3310,3381,3445,3504,3569,3634,3705,3757,3824,3905,3986,4042"}, "to": {"startLines": "2,11,16,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,388,666,8335,8421,8508,8596,8694,8801,8871,8938,9034,9126,9191,9264,9327,9395,9509,9625,9741,9821,9905,9976,10047,10148,10250,10322,11072,11125,11183,11231,11292,11364,11431,11495,11566,11630,11689,11754,11819,11890,11942,12009,12090,12171", "endLines": "10,15,20,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "383,661,925,8416,8503,8591,8689,8796,8866,8933,9029,9121,9186,9259,9322,9390,9504,9620,9736,9816,9900,9971,10042,10143,10245,10317,10387,11120,11178,11226,11287,11359,11426,11490,11561,11625,11684,11749,11814,11885,11937,12004,12085,12166,12222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\5deb70712b1e9df4e3892e963cadfc60\\transformed\\material-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1129,1195,1287,1364,1427,1535,1595,1661,1717,1788,1848,1902,2021,2078,2140,2194,2269,2393,2481,2558,2652,2736,2819,2964,3049,3135,3268,3356,3434,3488,3542,3608,3682,3760,3831,3913,3985,4062,4135,4205,4314,4407,4479,4571,4667,4741,4817,4913,4966,5048,5115,5202,5289,5351,5415,5478,5547,5655,5760,5861,5964,6022,6080,6160,6246,6322", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "322,401,481,563,665,759,855,981,1062,1124,1190,1282,1359,1422,1530,1590,1656,1712,1783,1843,1897,2016,2073,2135,2189,2264,2388,2476,2553,2647,2731,2814,2959,3044,3130,3263,3351,3429,3483,3537,3603,3677,3755,3826,3908,3980,4057,4130,4200,4309,4402,4474,4566,4662,4736,4812,4908,4961,5043,5110,5197,5284,5346,5410,5473,5542,5650,5755,5856,5959,6017,6075,6155,6241,6317,6394"}, "to": {"startLines": "21,54,55,56,57,58,66,67,68,93,94,146,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,214,218,219,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "930,3977,4056,4136,4218,4320,5139,5235,5361,8207,8269,12227,12633,12879,12942,13050,13110,13176,13232,13303,13363,13417,13536,13593,13655,13709,13784,13908,13996,14073,14167,14251,14334,14479,14564,14650,14783,14871,14949,15003,15057,15123,15197,15275,15346,15428,15500,15577,15650,15720,15829,15922,15994,16086,16182,16256,16332,16428,16481,16563,16630,16717,16804,16866,16930,16993,17062,17170,17275,17376,17479,17537,17772,18106,18192,18341", "endLines": "25,54,55,56,57,58,66,67,68,93,94,146,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,214,218,219,221", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "1152,4051,4131,4213,4315,4409,5230,5356,5437,8264,8330,12314,12705,12937,13045,13105,13171,13227,13298,13358,13412,13531,13588,13650,13704,13779,13903,13991,14068,14162,14246,14329,14474,14559,14645,14778,14866,14944,14998,15052,15118,15192,15270,15341,15423,15495,15572,15645,15715,15824,15917,15989,16081,16177,16251,16327,16423,16476,16558,16625,16712,16799,16861,16925,16988,17057,17165,17270,17371,17474,17532,17590,17847,18187,18263,18413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cf4e6ff07dbeac5bcd8d8e9bb606754a\\transformed\\exoplayer-core-2.18.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,581,662", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "125,186,251,324,403,476,576,657,730"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10392,10467,10528,10593,10666,10745,10818,10918,10999", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "10462,10523,10588,10661,10740,10813,10913,10994,11067"}}]}]}