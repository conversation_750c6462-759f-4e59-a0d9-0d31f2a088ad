# C/C++ build system timings
generate_cxx_metadata
  [gap of 53ms]
  create-invalidation-state 23ms
  generate-prefab-packages
    [gap of 25ms]
    exec-prefab 690ms
    [gap of 30ms]
  generate-prefab-packages completed in 745ms
  execute-generate-process
    exec-configure 1191ms
    [gap of 57ms]
  execute-generate-process completed in 1256ms
  [gap of 65ms]
generate_cxx_metadata completed in 2165ms

