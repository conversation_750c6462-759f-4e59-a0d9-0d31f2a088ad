{"artifacts": [{"path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/build/intermediates/cxx/Debug/335l6r1b/obj/arm64-v8a/libexpo-modules-core.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_options", "target_include_directories", "target_precompile_headers"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 38, "parent": 0}, {"command": 1, "file": 0, "line": 122, "parent": 0}, {"command": 1, "file": 0, "line": 134, "parent": 0}, {"command": 2, "file": 0, "line": 116, "parent": 0}, {"command": 3, "file": 0, "line": 101, "parent": 0}, {"command": 4, "file": 0, "line": 47, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=81 -fno-limit-debug-info  -fPIC"}, {"backtrace": 4, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}, {"backtrace": 2, "fragment": "-O2"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 2, "fragment": "-fstack-protector-all"}, {"backtrace": 2, "fragment": "-DUSE_HERMES=0"}, {"backtrace": 2, "fragment": "-DUNIT_TEST=0"}, {"backtrace": 2, "fragment": "-DIS_NEW_ARCHITECTURE_ENABLED=1"}, {"backtrace": 2, "fragment": "-DRN_FABRIC_ENABLED=1"}, {"backtrace": 2, "fragment": "-DRN_SERIALIZABLE_STATE=1"}, {"fragment": "-std=gnu++20"}, {"fragment": "-Winvalid-pch -fpch-instantiate-templates -Xclang -emit-pch -Xclang -include -Xclang C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/335l6r1b/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx -x c++-header"}], "defines": [{"define": "expo_modules_core_EXPORTS"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include/react"}, {"backtrace": 5, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule"}, {"backtrace": 5, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp"}, {"backtrace": 5, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "precompileHeaders": [{"backtrace": 6, "header": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/ExpoHeader.pch"}], "sourceIndexes": [0], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, {"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=81 -fno-limit-debug-info  -fPIC"}, {"backtrace": 4, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}, {"backtrace": 2, "fragment": "-O2"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 2, "fragment": "-fstack-protector-all"}, {"backtrace": 2, "fragment": "-DUSE_HERMES=0"}, {"backtrace": 2, "fragment": "-DUNIT_TEST=0"}, {"backtrace": 2, "fragment": "-DIS_NEW_ARCHITECTURE_ENABLED=1"}, {"backtrace": 2, "fragment": "-DRN_FABRIC_ENABLED=1"}, {"backtrace": 2, "fragment": "-DRN_SERIALIZABLE_STATE=1"}, {"fragment": "-std=gnu++20"}, {"fragment": "-Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/335l6r1b/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/335l6r1b/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx"}], "defines": [{"define": "expo_modules_core_EXPORTS"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include/react"}, {"backtrace": 5, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule"}, {"backtrace": 5, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp"}, {"backtrace": 5, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/90aef289aa3b9d5bc378af6dcd7b813d/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/673a27f53479c4bbce3e2bb096fa9438/transformed/react-android-0.81.4-debug/prefab/modules/reactnative/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "precompileHeaders": [{"backtrace": 6, "header": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/ExpoHeader.pch"}], "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 2, "id": "fabric::@3c04bbf757b97f4dae7c"}], "id": "expo-modules-core::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\24\\liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "src\\fabric\\libfabric.a", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\90aef289aa3b9d5bc378af6dcd7b813d\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "expo-modules-core", "nameOnDisk": "libexpo-modules-core.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40]}, {"name": "Precompile Header File", "sourceIndexes": [41]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "path": ".cxx/Debug/335l6r1b/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.cxx", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "C:/Users/<USER>/gemini-cli/live-mc/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/Exceptions.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/ExpoModulesHostObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JNIDeallocator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JNIFunctionBody.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JNIInjector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JNIUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JSIContext.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JSReferencesCache.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JSharedObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaReferencesCache.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaScriptFunction.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaScriptModuleObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaScriptObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaScriptRuntime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaScriptTypedArray.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaScriptValue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/JavaScriptWeakObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/MethodMetadata.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/RuntimeHolder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/WeakRuntimeHolder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/types/AnyType.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/types/ExpectedType.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/types/FrontendConverter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/types/FrontendConverterProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/types/JNIToJSIConverter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/decorators/JSClassesDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/decorators/JSConstantsDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/decorators/JSFunctionsDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/decorators/JSObjectDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/main/cpp/decorators/JSPropertiesDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "path": ".cxx/Debug/335l6r1b/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}