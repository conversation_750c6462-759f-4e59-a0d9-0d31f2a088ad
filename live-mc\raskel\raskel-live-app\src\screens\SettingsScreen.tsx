import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Alert,
  Switch,
  Dimensions,
  Modal,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation';
import { useAuth } from '../contexts/AuthContext';

type SettingsNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Settings'>;

const { width: screenWidth } = Dimensions.get('window');

interface SettingsOption {
  id: string;
  title: string;
  subtitle?: string;
  type: 'toggle' | 'navigation' | 'action' | 'info';
  icon: string;
  value?: boolean;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
  danger?: boolean;
  section: string;
}

interface FeedbackForm {
  category: string;
  message: string;
  email: string;
}

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation<SettingsNavigationProp>();
  const { user, signOut } = useAuth();
  
  // Settings state
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(false);
  const [streamNotifications, setStreamNotifications] = useState(true);
  const [projectUpdates, setProjectUpdates] = useState(true);
  const [dataSharing, setDataSharing] = useState(false);
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true);
  const [locationServices, setLocationServices] = useState(true);
  
  // Modal states
  const [showFeedback, setShowFeedback] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [showAbout, setShowAbout] = useState(false);
  
  // Feedback form
  const [feedbackForm, setFeedbackForm] = useState<FeedbackForm>({
    category: 'general',
    message: '',
    email: user?.email || '',
  });

  const handlePrivacyPolicyPress = () => {
    setShowPrivacyPolicy(true);
  };

  const handleTermsPress = () => {
    setShowTerms(true);
  };

  const handleHelpSupportPress = () => {
    Alert.alert(
      'Help & Support',
      'How can we help you today?',
      [
        { text: 'FAQ', onPress: () => handleFAQPress() },
        { text: 'Contact Support', onPress: () => handleContactSupport() },
        { text: 'Report Issue', onPress: () => handleReportIssue() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleFAQPress = () => {
    Alert.alert(
      'Frequently Asked Questions',
      'Common questions and answers:\n\n• How do I start a fishing stream?\n• How do I identify fish species?\n• How do I find fishing spots?\n• How do I update my profile?\n\nFor more detailed help, contact our support team.',
      [{ text: 'OK' }]
    );
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'Get in touch with our support team:\n\n📧 Email: <EMAIL>\n📞 Phone: 1-800-RASKEL\n💬 Live Chat: Available 9am-6pm EST\n\nWe typically respond within 24 hours.',
      [
        { text: 'Send Email', onPress: () => Alert.alert('Email', 'Opening email client...') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleReportIssue = () => {
    setShowFeedback(true);
  };

  const handleSubmitFeedback = () => {
    if (!feedbackForm.message.trim()) {
      Alert.alert('Error', 'Please enter your feedback message.');
      return;
    }

    // TODO: Implement actual feedback submission
    Alert.alert(
      'Feedback Submitted',
      'Thank you for your feedback! We appreciate your input and will review it carefully.',
      [
        {
          text: 'OK',
          onPress: () => {
            setShowFeedback(false);
            setFeedbackForm({ ...feedbackForm, message: '' });
          }
        }
      ]
    );
  };

  const handleAccountDeletion = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone and will permanently remove all your data, projects, and profile information.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Account',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Final Confirmation',
              'This will permanently delete your account. Type "DELETE" to confirm.',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Confirm Deletion',
                  style: 'destructive',
                  onPress: () => {
                    // TODO: Implement account deletion
                    Alert.alert('Account Deleted', 'Your account has been successfully deleted.');
                  }
                }
              ]
            );
          }
        }
      ]
    );
  };

  const handleDataExport = () => {
    Alert.alert(
      'Export Data',
      'We will prepare your data export and send it to your email address within 24-48 hours. This includes your profile information, project data, and activity history.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Request Export',
          onPress: () => {
            // TODO: Implement data export
            Alert.alert('Export Requested', 'Your data export has been requested and will be sent to your email.');
          }
        }
      ]
    );
  };

  const settingsOptions: SettingsOption[] = [
    // Notifications Section
    {
      id: 'push_notifications',
      title: 'Push Notifications',
      subtitle: 'Receive notifications on your device',
      type: 'toggle',
      icon: '🔔',
      value: pushNotifications,
      onToggle: setPushNotifications,
      section: 'Notifications',
    },
    {
      id: 'email_notifications',
      title: 'Email Notifications',
      subtitle: 'Receive updates via email',
      type: 'toggle',
      icon: '📧',
      value: emailNotifications,
      onToggle: setEmailNotifications,
      section: 'Notifications',
    },
    {
      id: 'stream_notifications',
      title: 'Stream Notifications',
      subtitle: 'Notify when followed users go live',
      type: 'toggle',
      icon: '📺',
      value: streamNotifications,
      onToggle: setStreamNotifications,
      section: 'Notifications',
    },
    {
      id: 'project_updates',
      title: 'Project Updates',
      subtitle: 'Updates on your active projects',
      type: 'toggle',
      icon: '🚧',
      value: projectUpdates,
      onToggle: setProjectUpdates,
      section: 'Notifications',
    },

    // Privacy Section
    {
      id: 'data_sharing',
      title: 'Data Sharing',
      subtitle: 'Share usage data to improve experience',
      type: 'toggle',
      icon: '🔒',
      value: dataSharing,
      onToggle: setDataSharing,
      section: 'Privacy',
    },
    {
      id: 'analytics',
      title: 'Analytics',
      subtitle: 'Help us improve with anonymous analytics',
      type: 'toggle',
      icon: '📊',
      value: analyticsEnabled,
      onToggle: setAnalyticsEnabled,
      section: 'Privacy',
    },
    {
      id: 'location_services',
      title: 'Location Services',
      subtitle: 'Find nearby fishing spots and guides',
      type: 'toggle',
      icon: '📍',
      value: locationServices,
      onToggle: setLocationServices,
      section: 'Privacy',
    },
    {
      id: 'privacy_policy',
      title: 'Privacy Policy',
      subtitle: 'How we protect your data',
      type: 'navigation',
      icon: '🛡️',
      onPress: handlePrivacyPolicyPress,
      section: 'Privacy',
    },

    // Account Section
    {
      id: 'export_data',
      title: 'Export My Data',
      subtitle: 'Download your account data',
      type: 'action',
      icon: '📤',
      onPress: handleDataExport,
      section: 'Account',
    },
    {
      id: 'delete_account',
      title: 'Delete Account',
      subtitle: 'Permanently remove your account',
      type: 'action',
      icon: '🗑️',
      onPress: handleAccountDeletion,
      danger: true,
      section: 'Account',
    },

    // Legal Section
    {
      id: 'terms_of_service',
      title: 'Terms of Service',
      subtitle: 'Our terms and conditions',
      type: 'navigation',
      icon: '📄',
      onPress: handleTermsPress,
      section: 'Legal',
    },
    {
      id: 'help_support',
      title: 'Help & Support',
      subtitle: 'Get help with your account',
      type: 'navigation',
      icon: '💬',
      onPress: handleHelpSupportPress,
      section: 'Support',
    },
    {
      id: 'submit_feedback',
      title: 'Submit Feedback',
      subtitle: 'Tell us how we can improve',
      type: 'action',
      icon: '💭',
      onPress: () => setShowFeedback(true),
      section: 'Support',
    },
    {
      id: 'about',
      title: 'About Raskel',
      subtitle: 'App version and information',
      type: 'navigation',
      icon: 'ℹ️',
      onPress: () => setShowAbout(true),
      section: 'About',
    },
  ];

  const groupedSettings = settingsOptions.reduce((acc, option) => {
    if (!acc[option.section]) {
      acc[option.section] = [];
    }
    acc[option.section].push(option);
    return acc;
  }, {} as Record<string, SettingsOption[]>);

  const renderSettingItem = (option: SettingsOption) => (
    <TouchableOpacity
      key={option.id}
      style={[styles.settingItem, option.danger && styles.dangerItem]}
      onPress={option.onPress}
      disabled={option.type === 'toggle'}
      activeOpacity={0.7}
    >
      <View style={styles.settingLeft}>
        <Text style={styles.settingIcon}>{option.icon}</Text>
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, option.danger && styles.dangerText]}>
            {option.title}
          </Text>
          {option.subtitle && (
            <Text style={styles.settingSubtitle}>{option.subtitle}</Text>
          )}
        </View>
      </View>
      
      <View style={styles.settingRight}>
        {option.type === 'toggle' && (
          <Switch
            value={option.value}
            onValueChange={option.onToggle}
            trackColor={{ false: '#E0E0E0', true: '#2E7DFF' }}
            thumbColor={option.value ? 'white' : '#f4f3f4'}
          />
        )}
        {(option.type === 'navigation' || option.type === 'action') && (
          <Text style={styles.chevron}>→</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Info Header */}
        <View style={styles.userHeader}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {user?.email?.charAt(0).toUpperCase() || 'U'}
            </Text>
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{user?.email?.split('@')[0] || 'User'}</Text>
            <Text style={styles.userEmail}>{user?.email || '<EMAIL>'}</Text>
          </View>
        </View>

        {/* Settings Sections */}
        {Object.entries(groupedSettings).map(([section, options]) => (
          <View key={section} style={styles.section}>
            <Text style={styles.sectionTitle}>{section}</Text>
            <View style={styles.sectionContent}>
              {options.map(renderSettingItem)}
            </View>
          </View>
        ))}

        {/* Sign Out Button */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.signOutButton} onPress={signOut}>
            <Text style={styles.signOutText}>Sign Out</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Feedback Modal */}
      <Modal
        visible={showFeedback}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowFeedback(false)}>
              <Text style={styles.modalCancel}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Submit Feedback</Text>
            <TouchableOpacity onPress={handleSubmitFeedback}>
              <Text style={styles.modalSubmit}>Submit</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <Text style={styles.inputLabel}>Category</Text>
            <View style={styles.categoryContainer}>
              {['general', 'bug', 'feature', 'ui'].map((cat) => (
                <TouchableOpacity
                  key={cat}
                  style={[
                    styles.categoryButton,
                    feedbackForm.category === cat && styles.categoryButtonActive
                  ]}
                  onPress={() => setFeedbackForm({ ...feedbackForm, category: cat })}
                >
                  <Text style={[
                    styles.categoryText,
                    feedbackForm.category === cat && styles.categoryTextActive
                  ]}>
                    {cat.charAt(0).toUpperCase() + cat.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <Text style={styles.inputLabel}>Your Feedback</Text>
            <TextInput
              style={styles.textArea}
              multiline
              numberOfLines={6}
              placeholder="Tell us what you think or report an issue..."
              value={feedbackForm.message}
              onChangeText={(text) => setFeedbackForm({ ...feedbackForm, message: text })}
              textAlignVertical="top"
            />
            
            <Text style={styles.inputLabel}>Email (Optional)</Text>
            <TextInput
              style={styles.textInput}
              placeholder="<EMAIL>"
              value={feedbackForm.email}
              onChangeText={(text) => setFeedbackForm({ ...feedbackForm, email: text })}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Privacy Policy Modal */}
      <Modal
        visible={showPrivacyPolicy}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowPrivacyPolicy(false)}>
              <Text style={styles.modalCancel}>Close</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Privacy Policy</Text>
            <View style={styles.placeholder} />
          </View>
          
          <ScrollView style={styles.modalContent}>
            <Text style={styles.legalText}>
              {/* Privacy Policy Content */}
              <Text style={styles.legalHeader}>Raskel Live Privacy Policy{'\n\n'}</Text>
              
              <Text style={styles.legalSubheader}>1. Information We Collect{'\n'}</Text>
              We collect information you provide directly to us, such as when you create an account, update your profile, or communicate with us.{'\n\n'}
              
              <Text style={styles.legalSubheader}>2. How We Use Your Information{'\n'}</Text>
              We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.{'\n\n'}
              
              <Text style={styles.legalSubheader}>3. Information Sharing{'\n'}</Text>
              We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.{'\n\n'}
              
              <Text style={styles.legalSubheader}>4. Data Security{'\n'}</Text>
              We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.{'\n\n'}
              
              <Text style={styles.legalSubheader}>5. Your Rights{'\n'}</Text>
              You have the right to access, update, or delete your personal information. You can do this through your account settings or by contacting us.{'\n\n'}
              
              <Text style={styles.legalSubheader}>6. Contact Us{'\n'}</Text>
              If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.{'\n\n'}
              
              Last updated: {new Date().toLocaleDateString()}
            </Text>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Terms of Service Modal */}
      <Modal
        visible={showTerms}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowTerms(false)}>
              <Text style={styles.modalCancel}>Close</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Terms of Service</Text>
            <View style={styles.placeholder} />
          </View>
          
          <ScrollView style={styles.modalContent}>
            <Text style={styles.legalText}>
              {/* Terms of Service Content */}
              <Text style={styles.legalHeader}>Raskel Live Terms of Service{'\n\n'}</Text>
              
              <Text style={styles.legalSubheader}>1. Acceptance of Terms{'\n'}</Text>
              By using Raskel Live, you agree to be bound by these Terms of Service and all applicable laws and regulations.{'\n\n'}
              
              <Text style={styles.legalSubheader}>2. Use License{'\n'}</Text>
              Permission is granted to temporarily use Raskel Live for personal, non-commercial transitory viewing only.{'\n\n'}
              
              <Text style={styles.legalSubheader}>3. User Accounts{'\n'}</Text>
              You are responsible for maintaining the confidentiality of your account and password and for restricting access to your account.{'\n\n'}
              
              <Text style={styles.legalSubheader}>4. Prohibited Uses{'\n'}</Text>
              You may not use our service for any unlawful purpose or to solicit others to perform unlawful acts.{'\n\n'}
              
              <Text style={styles.legalSubheader}>5. Content{'\n'}</Text>
              You retain ownership of content you submit, but grant us a license to use, modify, and distribute your content.{'\n\n'}
              
              <Text style={styles.legalSubheader}>6. Limitation of Liability{'\n'}</Text>
              Raskel Live shall not be liable for any indirect, incidental, special, consequential, or punitive damages.{'\n\n'}
              
              <Text style={styles.legalSubheader}>7. Contact Information{'\n'}</Text>
              Questions about the Terms of Service should be sent to <NAME_EMAIL>.{'\n\n'}
              
              Last updated: {new Date().toLocaleDateString()}
            </Text>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* About Modal */}
      <Modal
        visible={showAbout}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAbout(false)}>
              <Text style={styles.modalCancel}>Close</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>About Raskel</Text>
            <View style={styles.placeholder} />
          </View>
          
          <ScrollView style={styles.modalContent}>
            <View style={styles.aboutContent}>
              <Text style={styles.appIcon}>🎣</Text>
              <Text style={styles.appName}>Fish Kaster</Text>
              <Text style={styles.appVersion}>Version 1.0.0</Text>

              <Text style={styles.aboutDescription}>
                Fish Kaster is the premier platform for fishing enthusiasts and professionals. Connect with anglers, watch live fishing streams, and enhance your fishing adventures with AI-powered features.
              </Text>

              <View style={styles.aboutSection}>
                <Text style={styles.aboutSectionTitle}>Features</Text>
                <Text style={styles.aboutItem}>• Live streaming fishing adventures</Text>
                <Text style={styles.aboutItem}>• AI-powered fish species identification</Text>
                <Text style={styles.aboutItem}>• Weather and tides dashboard</Text>
                <Text style={styles.aboutItem}>• Fishing spot finder and mapping</Text>
                <Text style={styles.aboutItem}>• Expert fishing tips and tutorials</Text>
              </View>
              
              <View style={styles.aboutSection}>
                <Text style={styles.aboutSectionTitle}>Contact</Text>
                <Text style={styles.aboutItem}>📧 <EMAIL></Text>
                <Text style={styles.aboutItem}>🌐 www.raskel.com</Text>
                <Text style={styles.aboutItem}>📞 1-800-RASKEL</Text>
              </View>
              
              <Text style={styles.copyright}>
                © 2024 Raskel Live. All rights reserved.
              </Text>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  
  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E8E8E8',
  },
  
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  backIcon: {
    fontSize: 18,
    color: '#333',
  },
  
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A2E',
  },
  
  placeholder: {
    width: 40,
  },
  
  // Content
  content: {
    flex: 1,
  },
  
  // User Header
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 20,
  },
  
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#2E7DFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  
  avatarText: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
  },
  
  userInfo: {
    flex: 1,
  },
  
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A2E',
    marginBottom: 4,
  },
  
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  
  // Sections
  section: {
    marginBottom: 20,
  },
  
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
    marginLeft: 20,
  },
  
  sectionContent: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#E8E8E8',
  },
  
  // Setting Items
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  
  dangerItem: {
    backgroundColor: '#FFF5F5',
  },
  
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  
  settingIcon: {
    fontSize: 20,
    marginRight: 12,
    width: 24,
    textAlign: 'center',
  },
  
  settingText: {
    flex: 1,
  },
  
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1A1A2E',
    marginBottom: 2,
  },
  
  dangerText: {
    color: '#F44336',
  },
  
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  
  settingRight: {
    alignItems: 'center',
  },
  
  chevron: {
    fontSize: 16,
    color: '#999',
    fontWeight: '300',
  },
  
  // Sign Out Button
  signOutButton: {
    backgroundColor: '#F44336',
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E8E8E8',
  },
  
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A2E',
  },
  
  modalCancel: {
    fontSize: 16,
    color: '#666',
  },
  
  modalSubmit: {
    fontSize: 16,
    color: '#2E7DFF',
    fontWeight: '600',
  },
  
  modalContent: {
    flex: 1,
    padding: 20,
  },
  
  // Feedback Form
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A2E',
    marginBottom: 8,
    marginTop: 16,
  },
  
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  
  categoryButtonActive: {
    backgroundColor: '#2E7DFF',
    borderColor: '#2E7DFF',
  },
  
  categoryText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  
  categoryTextActive: {
    color: 'white',
  },
  
  textInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  
  textArea: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    height: 120,
  },
  
  // Legal Text
  legalText: {
    fontSize: 14,
    lineHeight: 22,
    color: '#333',
  },
  
  legalHeader: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1A2E',
  },
  
  legalSubheader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A2E',
  },
  
  // About Content
  aboutContent: {
    alignItems: 'center',
    padding: 20,
  },
  
  appIcon: {
    fontSize: 80,
    marginBottom: 16,
  },
  
  appName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A1A2E',
    marginBottom: 8,
  },
  
  appVersion: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
  },
  
  aboutDescription: {
    fontSize: 16,
    lineHeight: 24,
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
  },
  
  aboutSection: {
    width: '100%',
    marginBottom: 24,
  },
  
  aboutSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A2E',
    marginBottom: 12,
  },
  
  aboutItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  
  copyright: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    marginTop: 24,
  },
});

export default SettingsScreen;