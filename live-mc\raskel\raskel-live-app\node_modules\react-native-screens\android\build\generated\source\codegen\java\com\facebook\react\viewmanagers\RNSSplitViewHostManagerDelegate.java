/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNSSplitViewHostManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNSSplitViewHostManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNSSplitViewHostManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "preferredDisplayMode":
        mViewManager.setPreferredDisplayMode(view, (String) value);
        break;
      case "preferredSplitBehavior":
        mViewManager.setPreferredSplitBehavior(view, (String) value);
        break;
      case "primaryEdge":
        mViewManager.setPrimaryEdge(view, (String) value);
        break;
      case "showSecondaryToggleButton":
        mViewManager.setShowSecondaryToggleButton(view, value == null ? false : (boolean) value);
        break;
      case "displayModeButtonVisibility":
        mViewManager.setDisplayModeButtonVisibility(view, (String) value);
        break;
      case "columnMetrics":
        mViewManager.setColumnMetrics(view, (ReadableMap) value);
        break;
      case "orientation":
        mViewManager.setOrientation(view, (String) value);
        break;
      case "presentsWithGesture":
        mViewManager.setPresentsWithGesture(view, value == null ? true : (boolean) value);
        break;
      case "showInspector":
        mViewManager.setShowInspector(view, value == null ? false : (boolean) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
