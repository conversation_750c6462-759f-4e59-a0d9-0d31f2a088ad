# C/C++ build system timings
generate_cxx_metadata
  [gap of 53ms]
  create-invalidation-state 23ms
  generate-prefab-packages
    [gap of 25ms]
    exec-prefab 630ms
    [gap of 42ms]
  generate-prefab-packages completed in 697ms
  execute-generate-process
    [gap of 12ms]
    exec-configure 1300ms
    [gap of 57ms]
  execute-generate-process completed in 1369ms
  [gap of 24ms]
generate_cxx_metadata completed in 2184ms

