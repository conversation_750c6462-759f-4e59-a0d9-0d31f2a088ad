import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, Button, Alert } from 'react-native';
import Purchases, { PurchasesPackage } from 'react-native-purchases';

const SubscriptionScreen = () => {
  const [products, setProducts] = useState<PurchasesPackage[]>([]);
  const [isPro, setIsPro] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const offerings = await Purchases.getOfferings();
        if (offerings.current && offerings.current.availablePackages.length > 0) {
          setProducts(offerings.current.availablePackages);
        }
      } catch (e) {
        console.error(e);
        Alert.alert('Error fetching products', (e as Error).message);
      }
    };

    const checkSubscriptionStatus = async () => {
      try {
        const purchaserInfo = await Purchases.getCustomerInfo();
        if (purchaserInfo.entitlements.active.pro_entitlement) { // Assuming 'pro_entitlement' is your entitlement ID
          setIsPro(true);
        } else {
          setIsPro(false);
        }
      } catch (e) {
        console.error(e);
        Alert.alert('Error checking subscription', (e as Error).message);
      }
    };

    fetchProducts();
    checkSubscriptionStatus();
  }, []);

  const purchasePackage = async (pkg: PurchasesPackage) => {
    try {
      const { customerInfo } = await Purchases.purchasePackage(pkg);
      if (customerInfo.entitlements.active.pro_entitlement) {
        setIsPro(true);
        Alert.alert('Success', 'You are now a Pro member!');
      }
    } catch (e) {
      if (!Purchases.is
        ((e as Purchases.PurchasesError).userCancelled)) {
        console.error(e);
        Alert.alert('Purchase Error', (e as Error).message);
      }
    }
  };

  const restorePurchases = async () => {
    try {
      const customerInfo = await Purchases.restorePurchases();
      if (customerInfo.entitlements.active.pro_entitlement) {
        setIsPro(true);
        Alert.alert('Success', 'Your purchases have been restored!');
      } else {
        Alert.alert('No purchases to restore', 'No active subscriptions found.');
      }
    } catch (e) {
      console.error(e);
      Alert.alert('Restore Error', (e as Error).message);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Subscription Options</Text>
      {isPro ? (
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>You are currently a Pro member!</Text>
          <Button title="Restore Purchases" onPress={restorePurchases} />
        </View>
      ) : (
        <>
          <FlatList
            data={products}
            keyExtractor={(item) => item.identifier}
            renderItem={({ item }) => (
              <View style={styles.productCard}>
                <Text style={styles.productTitle}>{item.product.title}</Text>
                <Text style={styles.productDescription}>{item.product.description}</Text>
                <Text style={styles.productPrice}>{item.product.priceString}</Text>
                <Button title={`Buy ${item.product.priceString}`} onPress={() => purchasePackage(item)} />
              </View>
            )}
          />
          <Button title="Restore Purchases" onPress={restorePurchases} />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f0f2f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  statusContainer: {
    alignItems: 'center',
    marginTop: 50,
  },
  statusText: {
    fontSize: 18,
    marginBottom: 20,
  },
  productCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  productDescription: {
    fontSize: 14,
    marginBottom: 10,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
});

export default SubscriptionScreen;
