{"logs": [{"outputFile": "com.fishkaster.app-mergeDebugResources-106:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8aa23bce709bfe96db10568360097556\\transformed\\core-1.15.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "57,58,59,60,61,62,63,244", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4224,4323,4425,4523,4620,4728,4839,20354", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "4318,4420,4518,4615,4723,4834,4956,20450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\5deb70712b1e9df4e3892e963cadfc60\\transformed\\material-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1142,1242,1312,1371,1469,1531,1595,1654,1726,1789,1843,1960,2017,2079,2133,2205,2340,2423,2502,2598,2681,2759,2900,2984,3066,3214,3304,3382,3435,3494,3560,3631,3710,3781,3864,3940,4018,4090,4163,4267,4356,4428,4522,4621,4695,4767,4868,4918,5003,5069,5159,5248,5310,5374,5437,5504,5620,5733,5842,5947,6004,6067,6150,6235,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1137,1237,1307,1366,1464,1526,1590,1649,1721,1784,1838,1955,2012,2074,2128,2200,2335,2418,2497,2593,2676,2754,2895,2979,3061,3209,3299,3377,3430,3489,3555,3626,3705,3776,3859,3935,4013,4085,4158,4262,4351,4423,4517,4616,4690,4762,4863,4913,4998,5064,5154,5243,5305,5369,5432,5499,5615,5728,5837,5942,5999,6062,6145,6230,6304,6382"}, "to": {"startLines": "19,52,53,54,55,56,64,65,66,92,93,145,150,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,220,228,229,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,3789,3868,3946,4032,4132,4961,5062,5188,8283,8348,12304,12788,13183,13311,13409,13471,13535,13594,13666,13729,13783,13900,13957,14019,14073,14145,14513,14596,14675,14771,14854,14932,15073,15157,15239,15387,15477,15555,15608,15667,15733,15804,15883,15954,16037,16113,16191,16263,16336,16440,16529,16601,16695,16794,16868,16940,17041,17091,17176,17242,17332,17421,17483,17547,17610,17677,17793,17906,18015,18120,18177,18422,19093,19178,19325", "endLines": "22,52,53,54,55,56,64,65,66,92,93,145,150,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,220,228,229,231", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "928,3863,3941,4027,4127,4219,5057,5183,5266,8343,8408,12399,12853,13237,13404,13466,13530,13589,13661,13724,13778,13895,13952,14014,14068,14140,14275,14591,14670,14766,14849,14927,15068,15152,15234,15382,15472,15550,15603,15662,15728,15799,15878,15949,16032,16108,16186,16258,16331,16435,16524,16596,16690,16789,16863,16935,17036,17086,17171,17237,17327,17416,17478,17542,17605,17672,17788,17901,18010,18115,18172,18235,18500,19173,19247,19398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cf4e6ff07dbeac5bcd8d8e9bb606754a\\transformed\\exoplayer-core-2.18.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,254,320,398,477,569,655", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "121,182,249,315,393,472,564,650,720"}, "to": {"startLines": "118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10503,10574,10635,10702,10768,10846,10925,11017,11103", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "10569,10630,10697,10763,10841,10920,11012,11098,11168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\29b62c1e8951214205c1999e10893e42\\transformed\\foundation-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,239", "endColumns": "85,97,98", "endOffsets": "136,234,333"}, "to": {"startLines": "51,252,253", "startColumns": "4,4,4", "startOffsets": "3703,21034,21132", "endColumns": "85,97,98", "endOffsets": "3784,21127,21226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\30f51c8a36c85c07b7e3abc1db11ccb6\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,287,370,469,571,667,748,841,933,1023,1110,1201,1274,1363,1438,1514,1587,1664,1730", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,88,74,75,72,76,65,120", "endOffsets": "282,365,464,566,662,743,836,928,1018,1105,1196,1269,1358,1433,1509,1582,1659,1725,1846"}, "to": {"startLines": "67,68,89,90,91,153,154,218,219,223,224,230,232,236,239,241,246,247,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5271,5366,7986,8085,8187,13009,13090,18240,18332,18677,18764,19252,19403,19737,19970,20121,20532,20609,20756", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,88,74,75,72,76,65,120", "endOffsets": "5361,5444,8080,8182,8278,13085,13178,18327,18417,18759,18850,19320,19487,19807,20041,20189,20604,20670,20872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f198488ab5a5112dc7ed7a95c5218eda\\transformed\\browser-1.6.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "88,146,147,148", "startColumns": "4,4,4,4", "startOffsets": "7871,12404,12505,12616", "endColumns": "114,100,110,100", "endOffsets": "7981,12500,12611,12712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3ef9533b8d5a0884be92f5c6e6c0dbd2\\transformed\\exoplayer-ui-2.18.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,671,752,833,919,1023,1115,1188,1251,1341,1431,1496,1559,1626,1694,1843,1992,2135,2202,2284,2356,2429,2528,2627,2691,2761,2814,2872,2920,2981,3046,3112,3174,3242,3306,3365,3431,3496,3562,3614,3679,3757,3835", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,148,148,142,66,81,71,72,98,98,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "281,480,666,747,828,914,1018,1110,1183,1246,1336,1426,1491,1554,1621,1689,1838,1987,2130,2197,2279,2351,2424,2523,2622,2686,2756,2809,2867,2915,2976,3041,3107,3169,3237,3301,3360,3426,3491,3557,3609,3674,3752,3830,3887"}, "to": {"startLines": "2,11,15,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,580,8413,8494,8575,8661,8765,8857,8930,8993,9083,9173,9238,9301,9368,9436,9585,9734,9877,9944,10026,10098,10171,10270,10369,10433,11173,11226,11284,11332,11393,11458,11524,11586,11654,11718,11777,11843,11908,11974,12026,12091,12169,12247", "endLines": "10,14,18,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,148,148,142,66,81,71,72,98,98,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "376,575,761,8489,8570,8656,8760,8852,8925,8988,9078,9168,9233,9296,9363,9431,9580,9729,9872,9939,10021,10093,10166,10265,10364,10428,10498,11221,11279,11327,11388,11453,11519,11581,11649,11713,11772,11838,11903,11969,12021,12086,12164,12242,12299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\1ff7e39d1dce962b72e0e33d3ff513a9\\transformed\\play-services-base-18.5.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "70,71,72,73,74,75,76,77,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5530,5637,5810,5947,6054,6215,6349,6475,6720,6890,6998,7173,7311,7473,7657,7722,7789", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "5632,5805,5942,6049,6210,6344,6470,6586,6885,6993,7168,7306,7468,7652,7717,7784,7866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\72fd1eaed4249ec9561f48df05200d45\\transformed\\play-services-basement-18.4.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "6591", "endColumns": "128", "endOffsets": "6715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\02bec446bb5b07b8548f83ec26b6ae83\\transformed\\appcompat-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "933,1047,1147,1259,1345,1451,1574,1656,1734,1825,1918,2013,2107,2208,2301,2396,2493,2584,2677,2758,2864,2968,3066,3172,3276,3378,3532,19011", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "1042,1142,1254,1340,1446,1569,1651,1729,1820,1913,2008,2102,2203,2296,2391,2488,2579,2672,2753,2859,2963,3061,3167,3271,3373,3527,3624,19088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\673a27f53479c4bbce3e2bb096fa9438\\transformed\\react-android-0.81.4-debug\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,210,281,350,432,501,568,650,734,823,906,976,1062,1151,1226,1307,1388,1465,1540,1613,1700,1777,1858,1932", "endColumns": "73,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "124,205,276,345,427,496,563,645,729,818,901,971,1057,1146,1221,1302,1383,1460,1535,1608,1695,1772,1853,1927,2010"}, "to": {"startLines": "50,69,149,151,152,156,170,171,172,221,222,225,226,233,234,235,237,238,240,242,243,245,248,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3629,5449,12717,12858,12927,13242,14280,14347,14429,18505,18594,18855,18925,19492,19581,19656,19812,19893,20046,20194,20267,20455,20675,20877,20951", "endColumns": "73,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "3698,5525,12783,12922,13004,13306,14342,14424,14508,18589,18672,18920,19006,19576,19651,19732,19888,19965,20116,20262,20349,20527,20751,20946,21029"}}]}]}